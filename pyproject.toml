[project]
name = "airflow"
dynamic = ["version"]
description = "JIRA data pipeline for extracting and storing data from Jira Cloud to PostgreSQL"
requires-python = ">=3.10"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "<PERSON><PERSON><PERSON>", email = "vishal<PERSON><PERSON><EMAIL>"}
]
dependencies = [
    "setuptools",
    "alembic",
    "alembic-utils",
    "alembic-postgresql-enum",
    "aiohttp",
    "aiomonitor",
    "aiodns",
    "cffi",
    "asyncpg",
    "babel",
    "beautifulsoup4",
    "dependency-injector",
    "hypothesis",
    "hatchling",
    "hatch-vcs",
    "markdownify",
    "num2words",
    "openpyxl",
    "pandas",
    "pyarrow",
    "passlib",
    "python-dotenv",
    "pykeepass",
    "psycopg2-binary",
    "pydantic",
    "PyYAML",
    "psutil",
    "requests",
    "rich",
    "sqlalchemy",
    "sqlalchemy_utils",
    "sqlalchemy_json",
    "sqlalchemy-citext",
    "xlsxwriter",
]

[project.optional-dependencies]
dev = [
    "black",
    "isort",
    "mypy",
    "types-SQLAlchemy",

]
linux = [
    "apache-airflow>=3.0.0"
]
docs = [
    "sphinx",
    "sphinx_autodoc_typehints",
    "sphinx_copybutton",
    "myst_parser[linkify]",
    "nbsphinx",
    "sphinx-rtd-theme",
    "sphinx-autoapi",
]



[dependency-groups]
# Legacy database drivers (psycopg2) - recommended for existing codebases
legacy = [
    "psycopg2-binary",  # PostgreSQL adapter for Python (sync)
]

# Modern database drivers (psycopg3) - for new projects
modern = [
    "psycopg[binary]",  # Modern PostgreSQL adapter
]
# Test dependencies group
test = [
    # Core testing framework
    "pytest",
    "pytest-env",
    "pytest-asyncio",
    "pytest-bdd",
    "pytest-mock",
    "pytest-dotenv",
    # Coverage reporting
    "pytest-cov",
    "coverage[toml]",

    # Allure reporting
    "allure-pytest",
    "allure-python-commons",
    "pytest-allure-spec-coverage",

    # Additional testing utilities
    "pytest-xdist",  # Parallel test execution
    "pytest-timeout",  # Test timeouts
    "pytest-benchmark",  # Performance benchmarking
    "pytest-html",  # HTML test reports
    # Mocking and fixtures
    "responses",  # HTTP mocking
    "freezegun",  # Time mocking
    "factory-boy",  # Test data factories

    # Database testing utilities
    "pytest-postgresql==4.1.1",  # PostgreSQL testing. This is the last version that supports pyscopg2
    "sqlalchemy-utils",  # Database utilities

    # Async testing utilities
    "aioresponses",  # Async HTTP mocking
    "pytest-aiohttp",  # aiohttp testing
    # Additional test-only dependencies can go here
    "pytest-sugar",  # Better test output
    "pytest-clarity",  # Better assertion output
]

[build-system]
requires = ["hatchling", "hatch-vcs"]
build-backend = "hatchling.build"


[tool.uv]
required-version = "0.7.13"

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false
disallow_incomplete_defs = false


[tool.black]
line-length = 100
target-version = ["py310", 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
known_first_party = ["dags"]

[tool.pytest.ini_options]
# Pytest configuration
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

# Allure configuration
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--tb=short",
    "--alluredir=allure_results",
    "--clean-alluredir",
    "--asyncio-mode=auto"
]

# Allure labels for better reporting
allure_labels = [
    "epic",
    "feature", 
    "story"
]

# BDD configuration
bdd_features_base_dir = "tests/features"

# Markers
markers = [
    "unit: fast isolated tests",
    "integration: component interaction tests",
    "functional: end-to-end feature tests",
    "performance: performance and load tests",
    "slow: tests that take more than 1 second",
    "smoke: basic functionality tests",
    "regression: tests for previously fixed bugs",
    "database: Tests requiring database connection",
    "contract: API contract validation tests",
    "stress: Stress tests",    
    "async: Asynchronous tests",
    "bdd: Marker for BDD-style tests (e.g., using Gherkin)",
    "jira(issue_id): Link test to a Jira issue",
    "feature: Feature marker for allure reporting",
    "epic: Epic marker for allure reporting",
    "story: Story marker for allure reporting",
    "suite_smart_retry: Smart retry test suite",
    "scenario(scenario_id): Link test to a specific scenario",
    "parametrize: Parameterized test marker"
]

# Async test configuration
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "module"

# Logging
log_cli = true
log_cli_level = "INFO"
log_cli_format = "%(asctime)s [%(levelname)8s] %(name)s: %(message)s"
log_cli_date_format = "%Y-%m-%d %H:%M:%S"

# Warnings
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning"
]

# Environment variables
env = [
    "PYTHONTRACEMALLOC=1"
]

env_files = [
    ".env"
]

# Python path
pythonpath = ["."]

[tool.hatch.version]
source = "vcs"

[tool.hatch.build.targets.wheel]
packages = ["dags"]

[tool.coverage.run]
source = ["dags.data_pipeline"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/.venv/*",
    "*/archived/*",
    '*/site-packages/*',
    'setup.py'
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.alembic]

# path to migration scripts.
# this is typically a path given in POSIX (e.g. forward slashes)
# format, relative to the token %(here)s which refers to the location of this
# ini file
script_location = "alembic"

# template used to generate migration file names; The default value is %%(rev)s_%%(slug)s
# Uncomment the line below if you want the files to be prepended with date and time
# see https://alembic.sqlalchemy.org/en/latest/tutorial.html#editing-the-ini-file
# for all available tokens
# file_template = "%%(year)d_%%(month).2d_%%(day).2d_%%(hour).2d%%(minute).2d-%%(rev)s_%%(slug)s"

# additional paths to be prepended to sys.path. defaults to the current working directory.
prepend_sys_path = [
    "."
]

# timezone to use when rendering the date within the migration file
# as well as the filename.
# If specified, requires the python>=3.9 or backports.zoneinfo library and tzdata library.
# Any required deps can installed by adding `alembic[tz]` to the pip requirements
# string value is passed to ZoneInfo()
# leave blank for localtime
# timezone =

# max length of characters to apply to the "slug" field
# truncate_slug_length = 40

# set to 'true' to run the environment during
# the 'revision' command, regardless of autogenerate
# revision_environment = false

# set to 'true' to allow .pyc and .pyo files without
# a source .py file to be detected as revisions in the
# versions/ directory
# sourceless = false

# version location specification; This defaults
# to <script_location>/versions.  When using multiple version
# directories, initial revisions must be specified with --version-path.
# version_locations = [
#    "%(here)s/alembic/versions",
#    "%(here)s/foo/bar"
# ]


# set to 'true' to search source files recursively
# in each "version_locations" directory
# new in Alembic version 1.10
# recursive_version_locations = false

# the output encoding used when revision files
# are written from script.py.mako
# output_encoding = "utf-8"

# This section defines scripts or Python functions that are run
# on newly generated revision scripts.  See the documentation for further
# detail and examples
# [[tool.alembic.post_write_hooks]]
# format using "black" - use the console_scripts runner,
# against the "black" entrypoint
# name = "black"
# type = "console_scripts"
# entrypoint = "black"
# options = "-l 79 REVISION_SCRIPT_FILENAME"
#
# [[tool.alembic.post_write_hooks]]
# lint with attempts to fix using "ruff" - use the exec runner,
# execute a binary
# name = "ruff"
# type = "exec"
# executable = "%(here)s/.venv/bin/ruff"
# options = "check --fix REVISION_SCRIPT_FILENAME"

