"""
Test suite for the enhanced GlobalCircuitBreaker with error classification and cancellable sleep.
"""

import asyncio
import pytest
import time
from unittest.mock import AsyncMock, patch

# Import the enhanced circuit breaker
from dags.data_pipeline.utils.circuit_breaker import CircuitState, ErrorType
from archived.archived_code import GlobalCircuitBreaker, CircuitBreakerConfig


@pytest.mark.asyncio
async def test_error_classification():
    """Test that errors are properly classified."""
    print("🔧 Testing Error Classification")
    print("=" * 50)
    
    config = CircuitBreakerConfig(failure_threshold=3, recovery_timeout=5.0)
    circuit_breaker = GlobalCircuitBreaker(config=config)
    
    # Test rate limit error classification
    rate_limit_error = Exception("Rate limit exceeded - 429")
    assert circuit_breaker.classify_error(rate_limit_error) == ErrorType.RATE_LIMIT
    print("✅ Rate limit error classified correctly")
    
    # Test connection pool error classification
    pool_error = Exception("Connection pool exhausted")
    assert circuit_breaker.classify_error(pool_error) == ErrorType.CONNECTION_POOL
    print("✅ Connection pool error classified correctly")
    
    # Test network error classification
    network_error = Exception("Connection timeout occurred")
    assert circuit_breaker.classify_error(network_error) == ErrorType.NETWORK
    print("✅ Network error classified correctly")
    
    # Test service error classification
    service_error = Exception("Service unavailable (503)")
    assert circuit_breaker.classify_error(service_error) == ErrorType.SERVICE
    print("✅ Service error classified correctly")
    
    await circuit_breaker.cleanup()
    print("🎉 Error classification tests passed!")


@pytest.mark.asyncio
async def test_rate_limit_recovery():
    """Test that rate limit errors don't abort processes but wait for recovery."""
    print("\n🔧 Testing Rate Limit Recovery")
    print("=" * 50)
    
    config = CircuitBreakerConfig(failure_threshold=3, recovery_timeout=2.0)
    circuit_breaker = GlobalCircuitBreaker(config=config)
    
    # Simulate rate limit error
    rate_limit_error = Exception("Rate limit - 429")
    await circuit_breaker.record_error(rate_limit_error, 1000)  # 1 second delay
    
    # Check that circuit is still closed (rate limits don't open circuit immediately)
    assert circuit_breaker.state == CircuitState.CLOSED
    print("✅ Circuit remains closed for rate limit")
    
    # Check that requests are blocked due to rate limit backoff
    can_execute_before = await circuit_breaker.can_execute()
    assert not can_execute_before
    print("✅ Requests blocked during rate limit backoff")
    
    # Wait for recovery
    start_time = time.time()
    await circuit_breaker.wait_for_recovery(timeout=2.0, error_type=ErrorType.RATE_LIMIT)
    elapsed = time.time() - start_time
    
    print(f"   Recovery took {elapsed:.2f} seconds")
    assert 0.9 <= elapsed <= 2.5  # Should recover in ~1-2 seconds (allowing for scheduling delays)
    
    # Check that requests can execute after recovery
    can_execute_after = await circuit_breaker.can_execute()
    assert can_execute_after
    print("✅ Requests allowed after rate limit recovery")
    
    await circuit_breaker.cleanup()
    print("🎉 Rate limit recovery tests passed!")


@pytest.mark.asyncio
async def test_connection_pool_error_handling():
    """Test that connection pool errors use exponential backoff without aborting."""
    print("\n🔧 Testing Connection Pool Error Handling")
    print("=" * 50)
    
    config = CircuitBreakerConfig(
        failure_threshold=5, 
        recovery_timeout=3.0,
        connection_pool_retry_multiplier=2.0
    )
    circuit_breaker = GlobalCircuitBreaker(config=config)
    
    # Simulate connection pool error
    pool_error = Exception("Connection pool exhausted")
    await circuit_breaker.record_error(pool_error)
    
    # Check that circuit is still closed
    assert circuit_breaker.state == CircuitState.CLOSED
    print("✅ Circuit remains closed for connection pool error")
    
    # Check that requests are blocked due to connection pool backoff
    can_execute = await circuit_breaker.can_execute()
    assert not can_execute
    print("✅ Requests blocked during connection pool backoff")
    
    # Get circuit status to check backoff details
    status = await circuit_breaker.get_circuit_status()
    assert status["connection_pool_errors"] == 1
    print(f"✅ Connection pool errors tracked: {status['connection_pool_errors']}")
    
    await circuit_breaker.cleanup()
    print("🎉 Connection pool error handling tests passed!")


@pytest.mark.asyncio
async def test_network_error_retry_multiplier():
    """Test that network errors get increased retry attempts."""
    print("\n🔧 Testing Network Error Retry Multiplier")
    print("=" * 50)
    
    config = CircuitBreakerConfig(
        failure_threshold=3, 
        recovery_timeout=2.0,
        network_error_retry_multiplier=2
    )
    circuit_breaker = GlobalCircuitBreaker(config=config)
    
    # Simulate network error
    network_error = Exception("Connection timeout")
    await circuit_breaker.record_error(network_error)
    
    # Check status
    status = await circuit_breaker.get_circuit_status()
    assert status["network_errors"] == 1
    assert status["network_retry_multiplier"] >= 2
    print(f"✅ Network retry multiplier: {status['network_retry_multiplier']}")
    
    await circuit_breaker.cleanup()
    print("🎉 Network error retry multiplier tests passed!")


@pytest.mark.asyncio
async def test_cancellable_sleep():
    """Test that sleep operations can be cancelled when conditions change."""
    print("\n🔧 Testing Cancellable Sleep")
    print("=" * 50)
    
    config = CircuitBreakerConfig(failure_threshold=1, recovery_timeout=5.0)
    circuit_breaker = GlobalCircuitBreaker(config=config)
    
    # Open the circuit
    await circuit_breaker.record_error(Exception("Service error"))
    assert circuit_breaker.state == CircuitState.OPEN
    print("✅ Circuit opened")
    
    # Start waiting for recovery
    async def wait_for_recovery():
        start_time = time.time()
        await circuit_breaker.wait_for_recovery(timeout=10.0)
        return time.time() - start_time
    
    # Start the wait task
    wait_task = asyncio.create_task(wait_for_recovery())
    
    # Wait a bit, then record success to trigger recovery
    await asyncio.sleep(0.5)
    await circuit_breaker.record_success()
    
    # Wait should complete quickly due to cancellation
    elapsed = await wait_task
    print(f"   Wait completed in {elapsed:.2f} seconds")
    assert elapsed < 2.0  # Should complete much faster than 10 seconds
    print("✅ Sleep was cancelled when circuit recovered")
    
    await circuit_breaker.cleanup()
    print("🎉 Cancellable sleep tests passed!")


@pytest.mark.asyncio
async def test_health_monitoring():
    """Test that health monitoring works correctly."""
    print("\n🔧 Testing Health Monitoring")
    print("=" * 50)
    
    config = CircuitBreakerConfig(
        failure_threshold=3, 
        recovery_timeout=2.0,
        health_check_interval=1.0,
        max_concurrent_connections=10
    )
    circuit_breaker = GlobalCircuitBreaker(config=config)
    
    # Let health monitor run for a short time
    await asyncio.sleep(0.5)
    
    # Check that health monitoring task is running
    assert circuit_breaker._health_check_task is not None
    assert not circuit_breaker._health_check_task.done()
    print("✅ Health monitoring task is running")
    
    # Simulate high connection usage
    for _ in range(15):  # Exceed 80% of max_concurrent_connections
        await circuit_breaker.enter_request()
    
    status = await circuit_breaker.get_circuit_status()
    assert status["active_connections"] == 15
    print(f"✅ Connection tracking: {status['active_connections']} connections")
    
    await circuit_breaker.cleanup()
    print("🎉 Health monitoring tests passed!")


@pytest.mark.asyncio
async def test_comprehensive_circuit_status():
    """Test that circuit status provides comprehensive information."""
    print("\n🔧 Testing Comprehensive Circuit Status")
    print("=" * 50)
    
    config = CircuitBreakerConfig(failure_threshold=2, recovery_timeout=3.0)
    circuit_breaker = GlobalCircuitBreaker(config=config)
    
    # Record various types of errors
    await circuit_breaker.record_error(Exception("Rate limit - 429"), 2000)
    await circuit_breaker.record_error(Exception("Connection pool exhausted"))
    await circuit_breaker.record_error(Exception("Network timeout"))
    
    # Get comprehensive status
    status = await circuit_breaker.get_circuit_status()
    
    # Verify all status fields are present
    required_fields = [
        "state", "failure_count", "consecutive_rate_limits",
        "connection_pool_errors", "network_errors", "network_retry_multiplier",
        "active_requests", "active_connections", "backoffs", "recovery_events"
    ]
    
    for field in required_fields:
        assert field in status, f"Missing status field: {field}"
    
    print("✅ All status fields present")
    print(f"   State: {status['state']}")
    print(f"   Rate limits: {status['consecutive_rate_limits']}")
    print(f"   Connection pool errors: {status['connection_pool_errors']}")
    print(f"   Network errors: {status['network_errors']}")
    print(f"   Network retry multiplier: {status['network_retry_multiplier']}")
    
    await circuit_breaker.cleanup()
    print("🎉 Circuit status tests passed!")


async def main():
    """Run all enhanced circuit breaker tests."""
    print("🚀 Testing Enhanced GlobalCircuitBreaker")
    print("=" * 60)
    
    await test_error_classification()
    await test_rate_limit_recovery()
    await test_connection_pool_error_handling()
    await test_network_error_retry_multiplier()
    await test_cancellable_sleep()
    await test_health_monitoring()
    await test_comprehensive_circuit_status()
    
    print("\n" + "=" * 60)
    print("🎉 All Enhanced Circuit Breaker Tests Passed!")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
