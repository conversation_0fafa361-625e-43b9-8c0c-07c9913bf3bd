#!/usr/bin/env python3
"""
Test script to verify the CircuitBreaker fixes work correctly.
This script tests the key issues that were identified:
1. Circuit breaker recovery logic
2. Global rate limit warning coordination
"""

import asyncio
import time
import logging
import pytest
from datetime import datetime
from zoneinfo import ZoneInfo
import sys
from unittest.mock import AsyncMock, patch, MagicMock
import aiohttp
import types
import builtins
import importlib

# Setup basic logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import the fixed circuit breaker
from dags.data_pipeline.utils.circuit_breaker import CircuitState
from archived.archived_code import GlobalCircuitBreaker, CircuitBreakerConfig
from dags.data_pipeline.jira.api_client import fetch_with_retries


@pytest.mark.asyncio
async def test_circuit_breaker_recovery():
    """Test that circuit breaker recovery works correctly."""
    print("🔧 Testing Circuit Breaker Recovery Logic")
    print("=" * 50)
    
    # Create circuit breaker with short recovery timeout for testing
    config = CircuitBreakerConfig(
        failure_threshold=2,
        recovery_timeout=3.0,  # 3 seconds for quick testing
        half_open_max_calls=2
    )
    
    circuit_breaker = GlobalCircuitBreaker(config=config)
    
    # Test 1: Normal operation
    print("Test 1: Normal operation")
    can_execute = await circuit_breaker.can_execute()
    print(f"   Can execute (should be True): {can_execute}")
    assert can_execute, "Circuit should allow execution initially"
    
    # Test 2: Record failures to open circuit
    print("\nTest 2: Recording failures to open circuit")
    await circuit_breaker.record_failure()
    await circuit_breaker.record_failure()  # This should open the circuit
    
    print(f"   Circuit state: {circuit_breaker.state}")
    assert circuit_breaker.state == CircuitState.OPEN, "Circuit should be OPEN after failures"
    
    # Test 3: Circuit should block execution when OPEN
    print("\nTest 3: Circuit blocking when OPEN")
    can_execute = await circuit_breaker.can_execute()
    print(f"   Can execute (should be False): {can_execute}")
    assert not can_execute, "Circuit should block execution when OPEN"
    
    # Test 4: Wait for recovery and test transition to HALF_OPEN
    print(f"\nTest 4: Waiting {config.recovery_timeout} seconds for recovery...")
    await asyncio.sleep(config.recovery_timeout + 0.1)  # Wait a bit longer than recovery timeout
    
    can_execute = await circuit_breaker.can_execute()
    print(f"   Can execute after timeout (should be True): {can_execute}")
    print(f"   Circuit state: {circuit_breaker.state}")
    assert can_execute, "Circuit should allow execution after recovery timeout"
    assert circuit_breaker.state == CircuitState.HALF_OPEN, "Circuit should be HALF_OPEN after recovery"
    
    # Test 5: Record success to close circuit
    print("\nTest 5: Recording success to close circuit")
    await circuit_breaker.record_success()
    print(f"   Circuit state: {circuit_breaker.state}")
    assert circuit_breaker.state == CircuitState.CLOSED, "Circuit should be CLOSED after success"
    
    print("✅ Circuit breaker recovery logic working correctly!")

@pytest.mark.asyncio
async def test_rate_limit_warning_coordination():
    """Test global rate limit warning coordination."""
    print("\n🚦 Testing Rate Limit Warning Coordination")
    print("=" * 50)
    
    circuit_breaker = GlobalCircuitBreaker()
    
    # Test 1: Normal execution
    print("Test 1: Normal execution")
    can_execute = await circuit_breaker.can_execute()
    print(f"   Can execute (should be True): {can_execute}")
    assert can_execute, "Should allow execution normally"
    
    # Test 2: Activate rate limit warning
    print("\nTest 2: Activating rate limit warning")
    warning_duration = 2.0
    await circuit_breaker.record_rate_limit_warning(warning_duration)
    
    # Test 3: Should block execution during warning period
    print("\nTest 3: Execution during warning period")
    can_execute = await circuit_breaker.can_execute()
    print(f"   Can execute (should be False): {can_execute}")
    assert not can_execute, "Should block execution during rate limit warning"
    
    # Test 4: Wait for warning to clear
    print(f"\nTest 4: Waiting {warning_duration} seconds for warning to clear...")
    await asyncio.sleep(warning_duration + 0.1)
    
    can_execute = await circuit_breaker.can_execute()
    print(f"   Can execute after warning (should be True): {can_execute}")
    assert can_execute, "Should allow execution after warning clears"
    
    print("✅ Rate limit warning coordination working correctly!")

@pytest.mark.asyncio
async def test_wait_for_recovery_logic():
    """Test the wait_for_recovery method works correctly."""
    print("\n⏳ Testing wait_for_recovery Logic")
    print("=" * 50)
    
    config = CircuitBreakerConfig(
        failure_threshold=1,
        recovery_timeout=2.0,  # 2 seconds for testing
    )
    
    circuit_breaker = GlobalCircuitBreaker(config=config)
    
    # Open the circuit
    print("Test 1: Opening circuit")
    await circuit_breaker.record_failure()
    assert circuit_breaker.state == CircuitState.OPEN, "Circuit should be OPEN"
    
    # Test wait_for_recovery with timeout
    print("\nTest 2: Testing wait_for_recovery with short timeout")
    start_time = time.time()
    await circuit_breaker.wait_for_recovery(timeout=1.0)  # Shorter than recovery timeout
    elapsed = time.time() - start_time
    print(f"   Elapsed time: {elapsed:.2f} seconds (should be ~1.0)")
    assert 0.9 <= elapsed <= 1.2, f"Should timeout after ~1 second, got {elapsed}"
    
    # Test wait_for_recovery without timeout (should wait for full recovery)
    print("\nTest 3: Testing wait_for_recovery without timeout")
    start_time = time.time()
    
    # Start a task that will trigger recovery after the timeout
    async def trigger_recovery():
        await asyncio.sleep(config.recovery_timeout + 0.1)
        # Simulate the circuit transitioning to HALF_OPEN and then success
        await circuit_breaker.can_execute()  # This should transition to HALF_OPEN
        await circuit_breaker.record_success()  # This should close the circuit
    
    # Run both tasks concurrently
    recovery_task = asyncio.create_task(trigger_recovery())
    await circuit_breaker.wait_for_recovery()
    await recovery_task
    
    elapsed = time.time() - start_time
    print(f"   Elapsed time: {elapsed:.2f} seconds")
    print(f"   Circuit state: {circuit_breaker.state}")
    assert circuit_breaker.state == CircuitState.CLOSED, "Circuit should be CLOSED after recovery"
    
    print("✅ wait_for_recovery logic working correctly!")

@pytest.mark.asyncio
async def test_record_success_resets_counters():
    """Test that record_success properly resets counters."""
    print("\n🔄 Testing record_success counter reset")
    print("=" * 50)
    
    config = CircuitBreakerConfig(failure_threshold=5, recovery_timeout=10.0, half_open_max_calls=2)
    cb = GlobalCircuitBreaker(config=config)
    # Simulate some failures and rate limits
    cb._failure_count = 3
    cb._consecutive_rate_limits = 4
    cb._state = CircuitState.CLOSED
    await cb.record_success()
    print(f"   Failure count after success: {cb._failure_count} (should be 2)")
    print(f"   Consecutive rate limits after success: {cb._consecutive_rate_limits} (should be 0)")
    assert cb._failure_count == 2  # Should decrement by 1
    assert cb._consecutive_rate_limits == 0  # Should reset to 0
    print("✅ record_success counter reset working correctly!")

@pytest.mark.asyncio
async def test_record_rate_limit_sets_backoff():
    """Test that record_rate_limit properly sets backoff timing."""
    print("\n⏰ Testing record_rate_limit backoff timing")
    print("=" * 50)
    
    config = CircuitBreakerConfig()
    cb = GlobalCircuitBreaker(config=config)
    retry_delay = 1500  # ms
    before = time.time()
    await cb.record_rate_limit(retry_delay)
    after = time.time()
    expected_backoff = before + (retry_delay / 1000)
    print(f"   Backoff until: {cb._backoff_until}")
    print(f"   Expected: {expected_backoff}")
    print(f"   Consecutive rate limits: {cb._consecutive_rate_limits}")
    # _backoff_until should be set to after + retry_delay/1000
    assert abs(cb._backoff_until - expected_backoff) < 0.01  # Allow small timing variance
    assert cb._consecutive_rate_limits == 1
    print("✅ record_rate_limit backoff timing working correctly!")

@pytest.mark.asyncio
async def test_request_context_tracking():
    """Test request context tracking functionality."""
    print("\n📊 Testing request context tracking")
    print("=" * 50)
    
    cb = GlobalCircuitBreaker()
    # Initial active requests should be 0
    print(f"   Initial active requests: {cb._active_requests} (should be 0)")
    assert cb._active_requests == 0
    
    await cb.enter_request()
    print(f"   After enter_request: {cb._active_requests} (should be 1)")
    assert cb._active_requests == 1
    
    await cb.enter_request()
    print(f"   After second enter_request: {cb._active_requests} (should be 2)")
    assert cb._active_requests == 2
    
    await cb.exit_request()
    print(f"   After exit_request: {cb._active_requests} (should be 1)")
    assert cb._active_requests == 1
    
    await cb.exit_request()
    print(f"   After second exit_request: {cb._active_requests} (should be 0)")
    assert cb._active_requests == 0
    print("✅ Request context tracking working correctly!")

@pytest.mark.asyncio
async def test_open_on_consecutive_rate_limits():
    """Test circuit opens on consecutive rate limits."""
    print("\n🚨 Testing circuit opening on consecutive rate limits")
    print("=" * 50)
    
    class CustomConfig(CircuitBreakerConfig):
        max_consecutive_rate_limits = 3
    
    config = CustomConfig()
    cb = GlobalCircuitBreaker(config=config)
    
    print(f"   Max consecutive rate limits: {config.max_consecutive_rate_limits}")
    
    # Hit rate limit max_consecutive_rate_limits times
    for i in range(config.max_consecutive_rate_limits):
        await cb.record_rate_limit(100)
        print(f"   Rate limit {i+1}: State = {cb.state}, Consecutive = {cb._consecutive_rate_limits}")
    
    print(f"   Final state: {cb.state} (should be OPEN)")
    print(f"   Circuit healthy event set: {cb._circuit_healthy_event.is_set()} (should be False)")
    assert cb.state == CircuitState.OPEN
    assert not cb._circuit_healthy_event.is_set()
    print("✅ Circuit opening on consecutive rate limits working correctly!")

@pytest.mark.asyncio
async def test_can_execute_blocks_during_backoff():
    """Test that can_execute blocks during backoff period."""
    print("\n⛔ Testing can_execute blocking during backoff")
    print("=" * 50)
    
    cb = GlobalCircuitBreaker()
    
    # Set backoff to future
    cb._backoff_until = time.time() + 1.0
    can_exec = await cb.can_execute()
    print(f"   Can execute during backoff: {can_exec} (should be False)")
    assert can_exec is False
    
    # After backoff expires, should allow execution
    cb._backoff_until = time.time() - 1.0
    can_exec = await cb.can_execute()
    print(f"   Can execute after backoff: {can_exec} (should be True)")
    assert can_exec is True
    print("✅ Backoff blocking working correctly!")

@pytest.mark.asyncio
async def test_half_open_call_limit():
    """Test half-open state call limiting."""
    print("\n🔄 Testing half-open call limiting")
    print("=" * 50)
    
    config = CircuitBreakerConfig(failure_threshold=1, recovery_timeout=1.0, half_open_max_calls=2)
    cb = GlobalCircuitBreaker(config=config)
    
    # Force state to HALF_OPEN
    cb._state = CircuitState.HALF_OPEN
    cb._half_open_calls = 0
    
    print(f"   Max calls in half-open: {config.half_open_max_calls}")
    
    # Should allow up to half_open_max_calls
    for i in range(config.half_open_max_calls):
        allowed = await cb.can_execute()
        print(f"   Call {i+1}: Allowed = {allowed} (should be True)")
        assert allowed is True
        cb._half_open_calls += 1
    
    # Next call should be blocked
    allowed = await cb.can_execute()
    print(f"   Call {config.half_open_max_calls + 1}: Allowed = {allowed} (should be False)")
    assert allowed is False
    print("✅ Half-open call limiting working correctly!")


@pytest.mark.asyncio
async def test_open_to_half_open_transition():
    """Verifies that after the recovery timeout elapses in the OPEN state, the circuit breaker correctly transitions to HALF_OPEN, allowing limited requests for recovery."""
    config = CircuitBreakerConfig(failure_threshold=1, recovery_timeout=1.0, half_open_max_calls=2)
    cb = GlobalCircuitBreaker(config=config)
    # Open the circuit
    await cb.record_failure()
    assert cb.state == CircuitState.OPEN
    # Wait for recovery timeout to elapse
    await asyncio.sleep(config.recovery_timeout + 0.1)
    # Should transition to HALF_OPEN on next can_execute
    allowed = await cb.can_execute()
    assert allowed is True
    assert cb.state == CircuitState.HALF_OPEN


@pytest.mark.asyncio
async def test_wait_for_recovery_timeout():
    """Ensures that wait_for_recovery properly handles timeout scenarios when the circuit breaker does not recover within the specified period, preventing indefinite blocking."""
    config = CircuitBreakerConfig(failure_threshold=1, recovery_timeout=5.0)
    cb = GlobalCircuitBreaker(config=config)
    # Open the circuit
    await cb.record_failure()
    assert cb.state == CircuitState.OPEN
    # Call wait_for_recovery with a short timeout, should timeout and return
    start = time.time()
    await cb.wait_for_recovery(timeout=1.0)
    elapsed = time.time() - start
    assert 0.9 <= elapsed <= 1.2
    # Circuit should still be OPEN
    assert cb.state == CircuitState.OPEN


@pytest.mark.asyncio
async def test_wait_for_recovery_all_waiters():
    """Test that wait_for_recovery does not deadlock if all coroutines are waiting and none call can_execute directly."""
    config = CircuitBreakerConfig(failure_threshold=1, recovery_timeout=1.0, half_open_max_calls=1)
    cb = GlobalCircuitBreaker(config=config)
    # Open the circuit
    await cb.record_failure()
    assert cb.state == CircuitState.OPEN

    # Start several coroutines that all wait for recovery
    async def waiter():
        await cb.wait_for_recovery(timeout=2.0)
        # After recovery, should be able to execute
        return await cb.can_execute()

    # Wait a bit longer than recovery_timeout to ensure transition
    results = await asyncio.gather(*(waiter() for _ in range(5)))
    # All should be True (HALF_OPEN or better)
    assert all(results), f"Not all waiters were released: {results}"
    # Now record success to close the circuit
    await cb.record_success()
    assert cb.state == CircuitState.CLOSED

@pytest.mark.asyncio
async def test_fetch_with_retries_triggers_shutdown_on_persistent_error():
    """Test that fetch_with_retries triggers graceful shutdown on persistent non-rate-limit errors."""
    # Patch the shutdown handler
    with patch("dags.data_pipeline.containers.shutdown_handler.shutdown", new_callable=AsyncMock) as mock_shutdown:
        # Patch the aiohttp.ClientSession.request to always raise a ClientResponseError
        class DummyResponse:
            status = 500
            headers = {}
            async def __aenter__(self):
                raise aiohttp.ClientResponseError(request_info=None, history=None, status=500, message="Internal Server Error")
            async def __aexit__(self, exc_type, exc, tb):
                return False
        
        dummy_session = MagicMock()
        dummy_session.request.return_value = DummyResponse()

        # Patch the global_circuit_breaker to a real instance with a mocked trigger_graceful_shutdown
        from dags.data_pipeline.containers import GlobalCircuitBreaker
        circuit_breaker = GlobalCircuitBreaker()
        circuit_breaker.trigger_graceful_shutdown = AsyncMock()

        # Call fetch_with_retries with the dummy session and circuit breaker
        result = await fetch_with_retries(
            session=dummy_session,
            method="GET",
            url="http://dummy-url",
            retries=2,
            my_logger=logger,
            global_circuit_breaker=circuit_breaker
        )
        # It should return failure
        assert result["success"] is False
        # The circuit breaker should have triggered shutdown
        circuit_breaker.trigger_graceful_shutdown.assert_awaited()
        # The shutdown handler should have been called (if the circuit breaker implementation calls it)
        # mock_shutdown.assert_awaited()  # Uncomment if you want to check the actual shutdown handler

        print("✅ fetch_with_retries triggers graceful shutdown on persistent error!")

@pytest.mark.asyncio
async def main():
    """Run all tests."""
    print("🧪 Testing Circuit Breaker Fixes")
    print("=" * 60)
    
    try:
        await test_circuit_breaker_recovery()
        await test_rate_limit_warning_coordination()
        await test_wait_for_recovery_logic()
        await test_record_success_resets_counters()
        await test_record_rate_limit_sets_backoff()
        await test_request_context_tracking()
        await test_open_on_consecutive_rate_limits()
        await test_can_execute_blocks_during_backoff()
        await test_half_open_call_limit()
        await test_open_to_half_open_transition()
        await test_wait_for_recovery_timeout()
        await test_wait_for_recovery_all_waiters()
        await test_fetch_with_retries_triggers_shutdown_on_persistent_error()
        
        print("\n🎉 All tests passed! Circuit breaker fixes are working correctly.")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
