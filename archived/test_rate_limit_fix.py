#!/usr/bin/env python3
"""
Test script to verify the rate limit warning loop fix.
This script simulates the conditions that were causing the infinite loop.
"""

import asyncio
import time
from unittest.mock import patch, MagicMock
from archived.archived_code import GlobalCircuitBreaker, CircuitBreakerConfig


async def test_rate_limit_warning_loop_fix():
    """Test that rate limit warnings don't create infinite loops."""
    print("🔧 Testing Rate Limit Warning Loop Fix")
    print("=" * 50)
    
    # Create circuit breaker with short health check interval for testing
    config = CircuitBreakerConfig(
        failure_threshold=5,
        recovery_timeout=3.0,
        health_check_interval=2.0  # Short interval for testing
    )
    circuit_breaker = GlobalCircuitBreaker(config=config)
    
    # Mock psutil to simulate high system load
    mock_memory = MagicMock()
    mock_memory.percent = 96.0  # High memory usage
    mock_cpu_percent = MagicMock(return_value=95.0)  # High CPU usage
    
    warning_count = 0
    original_activate_warning = circuit_breaker._activate_rate_limit_warning
    
    async def count_warnings(*args, **kwargs):
        nonlocal warning_count
        warning_count += 1
        print(f"   Warning #{warning_count} activated at {time.time()}")
        return await original_activate_warning(*args, **kwargs)
    
    # Patch the warning activation to count calls
    circuit_breaker._activate_rate_limit_warning = count_warnings
    
    print("   Starting test with simulated high system load...")
    
    with patch('psutil.virtual_memory', return_value=mock_memory), \
         patch('psutil.cpu_percent', mock_cpu_percent):
        
        # Perform multiple health checks in quick succession
        print("   Performing 5 health checks with 1-second intervals...")
        for i in range(5):
            print(f"   Health check #{i+1}")
            await circuit_breaker._perform_health_check()
            await asyncio.sleep(1)  # Wait 1 second between checks
    
    print(f"   Total warnings activated: {warning_count}")
    
    # The fix should prevent multiple warnings from being activated
    # We expect only 1 warning to be activated, not 5
    if warning_count == 1:
        print("   ✅ SUCCESS: Only one warning was activated (fix working)")
    elif warning_count > 1:
        print(f"   ❌ FAILURE: {warning_count} warnings were activated (fix not working)")
    else:
        print("   ❌ FAILURE: No warnings were activated (unexpected)")
    
    # Wait for warning to expire
    print("   Waiting for warning to expire...")
    await asyncio.sleep(6)  # Wait longer than the 5-second warning duration
    
    # Perform another health check - this should activate a new warning
    print("   Performing health check after warning expiry...")
    await circuit_breaker._perform_health_check()
    
    print(f"   Total warnings after expiry: {warning_count}")
    
    if warning_count == 2:
        print("   ✅ SUCCESS: New warning activated after expiry (fix working)")
    else:
        print(f"   ❌ FAILURE: Expected 2 warnings total, got {warning_count}")
    
    # Cleanup
    await circuit_breaker.cleanup()
    
    return warning_count


async def test_concurrent_health_checks():
    """Test that concurrent health checks don't create multiple warnings."""
    print("\n🔧 Testing Concurrent Health Checks")
    print("=" * 50)
    
    config = CircuitBreakerConfig(failure_threshold=5, recovery_timeout=3.0)
    circuit_breaker = GlobalCircuitBreaker(config=config)
    
    # Mock high system load
    mock_memory = MagicMock()
    mock_memory.percent = 96.0
    mock_cpu_percent = MagicMock(return_value=95.0)
    
    warning_count = 0
    original_activate_warning = circuit_breaker._activate_rate_limit_warning
    
    async def count_warnings(*args, **kwargs):
        nonlocal warning_count
        warning_count += 1
        print(f"   Concurrent warning #{warning_count} activated")
        return await original_activate_warning(*args, **kwargs)
    
    circuit_breaker._activate_rate_limit_warning = count_warnings
    
    print("   Starting 3 concurrent health checks...")
    
    with patch('psutil.virtual_memory', return_value=mock_memory), \
         patch('psutil.cpu_percent', mock_cpu_percent):
        
        # Run 3 health checks concurrently
        tasks = [
            circuit_breaker._perform_health_check(),
            circuit_breaker._perform_health_check(),
            circuit_breaker._perform_health_check()
        ]
        
        await asyncio.gather(*tasks)
    
    print(f"   Total warnings from concurrent checks: {warning_count}")
    
    if warning_count == 1:
        print("   ✅ SUCCESS: Only one warning activated from concurrent checks")
    else:
        print(f"   ❌ FAILURE: {warning_count} warnings activated from concurrent checks")
    
    await circuit_breaker.cleanup()
    return warning_count


async def main():
    """Run all tests."""
    print("🚀 Starting Rate Limit Warning Loop Fix Tests")
    print("=" * 60)
    
    try:
        # Test 1: Sequential health checks
        result1 = await test_rate_limit_warning_loop_fix()
        
        # Test 2: Concurrent health checks
        result2 = await test_concurrent_health_checks()
        
        print("\n" + "=" * 60)
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 60)
        
        if result1 == 2 and result2 == 1:
            print("🎉 ALL TESTS PASSED!")
            print("   ✅ Sequential health checks: PASS")
            print("   ✅ Concurrent health checks: PASS")
            print("   ✅ Rate limit warning loop fix is working correctly")
        else:
            print("❌ SOME TESTS FAILED!")
            print(f"   Sequential test result: {result1} (expected: 2)")
            print(f"   Concurrent test result: {result2} (expected: 1)")
            
    except Exception as e:
        print(f"❌ TEST ERROR: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
