# coding=utf-8
"""
Tests for the bug fixes in the scalable coordination system.

This module tests the specific bug fixes:
1. Timeout handling to prevent indefinite blocking on queue.get()
2. Initial consumer startup in dynamic consumer manager
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch

from dags.data_pipeline.scalable_coordination import (
    ScalableCoordinationManager,
    DynamicConsumerManager,
    ScalingConfig
)


class TestTimeoutBugFix:
    """Test suite for timeout bug fix."""
    
    @pytest.mark.asyncio
    async def test_queue_get_timeout_handling(self):
        """Test that queue get operations don't block indefinitely when termination is requested."""
        from unittest.mock import patch
        
        # Mock priority queue manager
        with patch('dags.data_pipeline.utility_code.priority_queue_manager') as mock_pqm:
            # Mock coordination manager
            with patch('dags.data_pipeline.utility_code.coordination_manager') as mock_coord:
                # Set up mocks
                mock_coord.should_terminate_consumer = AsyncMock(side_effect=[False, True])
                
                # Mock queue get that would normally block
                mock_pqm.get_priority_message = AsyncMock(side_effect=asyncio.TimeoutError())
                
                # Mock other dependencies
                mock_queue_manager = MagicMock()
                mock_queue_manager.get_queues.return_value = {"queue_issues": MagicMock()}
                
                mock_issue_processor = MagicMock()
                mock_logger = MagicMock()
                
                # Import the function to test
                from dags.data_pipeline.utility_code import _process_issues_loop
                
                # This should not hang indefinitely
                start_time = asyncio.get_event_loop().time()
                await _process_issues_loop(
                    mock_queue_manager,
                    mock_issue_processor,
                    mock_coord,
                    0,  # consumer_id
                    "test_consumer",
                    mock_logger
                )
                end_time = asyncio.get_event_loop().time()
                
                # Should complete quickly (within 5 seconds)
                assert end_time - start_time < 5.0
                
                # Should have checked termination condition
                assert mock_coord.should_terminate_consumer.call_count >= 1
    
    @pytest.mark.asyncio
    async def test_process_upsert_queue_timeout_handling(self):
        """Test timeout handling in process_upsert_queue."""
        from unittest.mock import patch
        
        # Mock dependencies
        mock_q_container = MagicMock()
        mock_queue = MagicMock()
        mock_q_container.queue_selector.return_value = {"queue_upsert_issue": mock_queue}
        
        mock_db_async = MagicMock()
        mock_logger = MagicMock()
        
        with patch('dags.data_pipeline.utility_code.coordination_manager') as mock_coord:
            with patch('dags.data_pipeline.utility_code.priority_queue_manager') as mock_pqm:
                # Set up termination condition
                mock_coord.should_terminate_consumer = AsyncMock(side_effect=[False, True])
                mock_coord.immediate_processing.is_set.return_value = False
                mock_coord.register_queue = MagicMock()
                
                # Mock timeout on queue get
                mock_pqm.get_priority_message = AsyncMock(side_effect=asyncio.TimeoutError())
                
                # Import and test
                from dags.data_pipeline.utility_code import process_upsert_queue
                
                # Should not hang
                start_time = asyncio.get_event_loop().time()
                await process_upsert_queue(
                    project_key="TEST",
                    q_container=mock_q_container,
                    db_rw_async={"TEST": mock_db_async},
                    my_logger=mock_logger
                )
                end_time = asyncio.get_event_loop().time()
                
                # Should complete quickly
                assert end_time - start_time < 5.0


class TestInitialConsumerBugFix:
    """Test suite for initial consumer startup bug fix."""
    
    @pytest.fixture
    def coordination_manager(self):
        """Create coordination manager for testing."""
        return ScalableCoordinationManager()
    
    @pytest.fixture
    def dynamic_manager(self, coordination_manager):
        """Create dynamic consumer manager for testing."""
        return DynamicConsumerManager(coordination_manager)
    
    @pytest.mark.asyncio
    async def test_initial_consumer_startup(self, dynamic_manager, coordination_manager):
        """Test that at least one consumer starts immediately."""
        # Mock consumer factory
        mock_consumer = AsyncMock()
        consumer_factory = AsyncMock(return_value=mock_consumer)
        
        # Mock queue with no initial load
        mock_queue = MagicMock()
        mock_queue.qsize.return_value = 0  # Empty queue initially
        coordination_manager.register_queue("test_queue", mock_queue)
        
        # Start scaling management
        scaling_task = asyncio.create_task(
            dynamic_manager.manage_queue_scaling(
                "test_queue",
                consumer_factory,
                ("arg1", "arg2"),
                {"kwarg1": "value1"},
                check_interval=0.1
            )
        )
        
        # Let it run briefly to start initial consumers
        await asyncio.sleep(0.2)
        
        # Should have started at least one consumer even with empty queue
        assert len(dynamic_manager.consumer_tasks["test_queue"]) >= 1
        
        # Verify consumer was registered
        assert coordination_manager.consumer_counts["test_queue"] >= 1
        
        # Cleanup
        scaling_task.cancel()
        try:
            await scaling_task
        except asyncio.CancelledError:
            pass
    
    @pytest.mark.asyncio
    async def test_minimum_consumers_maintained(self, dynamic_manager, coordination_manager):
        """Test that minimum number of consumers is maintained."""
        config = ScalingConfig(min_consumers_per_queue=2)
        coordination_manager.config = config
        
        # Mock consumer factory
        mock_consumer = AsyncMock()
        consumer_factory = AsyncMock(return_value=mock_consumer)
        
        # Mock queue
        mock_queue = MagicMock()
        mock_queue.qsize.return_value = 0
        coordination_manager.register_queue("test_queue", mock_queue)
        
        # Start scaling management
        scaling_task = asyncio.create_task(
            dynamic_manager.manage_queue_scaling(
                "test_queue",
                consumer_factory,
                ("arg1", "arg2"),
                {"kwarg1": "value1"},
                check_interval=0.1
            )
        )
        
        # Let it run briefly
        await asyncio.sleep(0.2)
        
        # Should have started minimum number of consumers
        assert len(dynamic_manager.consumer_tasks["test_queue"]) >= 2
        assert coordination_manager.consumer_counts["test_queue"] >= 2
        
        # Cleanup
        scaling_task.cancel()
        try:
            await scaling_task
        except asyncio.CancelledError:
            pass
    
    @pytest.mark.asyncio
    async def test_consumers_start_before_producers(self, dynamic_manager, coordination_manager):
        """Test that consumers start even when no producers are active yet."""
        # Mock consumer factory
        mock_consumer = AsyncMock()
        consumer_factory = AsyncMock(return_value=mock_consumer)
        
        # Mock queue
        mock_queue = MagicMock()
        mock_queue.qsize.return_value = 0
        coordination_manager.register_queue("test_queue", mock_queue)
        
        # No producers registered yet
        assert coordination_manager.producer_count == 0
        
        # Start scaling management
        scaling_task = asyncio.create_task(
            dynamic_manager.manage_queue_scaling(
                "test_queue",
                consumer_factory,
                ("arg1", "arg2"),
                {"kwarg1": "value1"},
                check_interval=0.1
            )
        )
        
        # Let it run briefly
        await asyncio.sleep(0.2)
        
        # Should have started consumers even without producers
        assert len(dynamic_manager.consumer_tasks["test_queue"]) >= 1
        
        # Cleanup
        scaling_task.cancel()
        try:
            await scaling_task
        except asyncio.CancelledError:
            pass


@pytest.mark.asyncio
async def test_integration_timeout_and_initial_consumers():
    """Integration test combining both bug fixes."""
    coordination_manager = ScalableCoordinationManager()
    dynamic_manager = DynamicConsumerManager(coordination_manager)
    
    # Mock consumer that would normally block
    async def mock_consumer_with_timeout(*args, **kwargs):
        # Simulate a consumer that gets timeout on queue operations
        while True:
            try:
                await asyncio.wait_for(asyncio.sleep(10), timeout=1.0)
            except asyncio.TimeoutError:
                # Check termination condition
                if await coordination_manager.should_terminate_consumer("test_queue"):
                    break
                continue
    
    # Mock queue
    mock_queue = MagicMock()
    mock_queue.qsize.return_value = 0
    coordination_manager.register_queue("test_queue", mock_queue)
    
    # Start scaling management
    scaling_task = asyncio.create_task(
        dynamic_manager.manage_queue_scaling(
            "test_queue",
            mock_consumer_with_timeout,
            (),
            {},
            check_interval=0.1
        )
    )
    
    # Let it run briefly
    await asyncio.sleep(0.3)
    
    # Should have started consumers
    assert len(dynamic_manager.consumer_tasks["test_queue"]) >= 1
    
    # Simulate producer completion to trigger termination
    await coordination_manager.register_producer("test_producer")
    await coordination_manager.unregister_producer("test_producer")
    
    # Let termination propagate
    await asyncio.sleep(0.2)
    
    # Should terminate gracefully without hanging
    assert scaling_task.done() or len(dynamic_manager.consumer_tasks["test_queue"]) == 0


class TestProcessUpsertQueueTermination:
    """Test suite for process_upsert_queue termination logic."""

    @pytest.mark.asyncio
    async def test_upsert_queue_terminates_when_specialized_consumers_done(self):
        """Test that process_upsert_queue terminates when all specialized consumers are done."""
        coordination_manager = ScalableCoordinationManager()

        # Register specialized consumers
        specialized_queues = ["queue_changelog", "queue_worklog", "queue_comment", "queue_issue_links", "queue_issue"]
        for queue_name in specialized_queues:
            await coordination_manager.register_consumer(queue_name, f"consumer_{queue_name}")

        # Mock queue with some messages
        mock_queue = MagicMock()
        mock_queue.qsize.return_value = 5  # 5 messages in queue
        coordination_manager.register_queue("queue_upsert_issue", mock_queue)

        # Should not terminate yet (consumers still active)
        should_terminate = await coordination_manager.should_terminate_consumer("queue_upsert_issue")
        assert not should_terminate

        # Unregister all specialized consumers
        for queue_name in specialized_queues:
            await coordination_manager.unregister_consumer(queue_name, f"consumer_{queue_name}")

        # Should still not terminate (queue not empty)
        should_terminate = await coordination_manager.should_terminate_consumer("queue_upsert_issue")
        assert not should_terminate

        # Empty the queue
        mock_queue.qsize.return_value = 0

        # Now should terminate
        should_terminate = await coordination_manager.should_terminate_consumer("queue_upsert_issue")
        assert should_terminate

    @pytest.mark.asyncio
    async def test_upsert_queue_different_from_other_queues(self):
        """Test that upsert queue termination logic is different from other queues."""
        coordination_manager = ScalableCoordinationManager()

        # Mock empty queues
        mock_queue = MagicMock()
        mock_queue.qsize.return_value = 0
        coordination_manager.register_queue("queue_upsert_issue", mock_queue)
        coordination_manager.register_queue("queue_changelog", mock_queue)

        # For regular queue, should terminate when producers are done
        await coordination_manager.register_producer("producer_1")
        should_terminate = await coordination_manager.should_terminate_consumer("queue_changelog")
        assert not should_terminate  # Producer still active

        await coordination_manager.unregister_producer("producer_1")
        should_terminate = await coordination_manager.should_terminate_consumer("queue_changelog")
        assert should_terminate  # Producer done, queue empty

        # For upsert queue, should not terminate even though producers are done
        should_terminate = await coordination_manager.should_terminate_consumer("queue_upsert_issue")
        assert not should_terminate  # No specialized consumers registered, so none to wait for

    @pytest.mark.asyncio
    async def test_are_all_specialized_consumers_done_method(self):
        """Test the helper method for checking specialized consumer status."""
        coordination_manager = ScalableCoordinationManager()

        # Initially all done (none registered)
        assert coordination_manager.are_all_specialized_consumers_done()

        # Register some consumers
        await coordination_manager.register_consumer("queue_changelog", "consumer_1")
        await coordination_manager.register_consumer("queue_worklog", "consumer_2")

        # Not all done
        assert not coordination_manager.are_all_specialized_consumers_done()

        # Unregister one
        await coordination_manager.unregister_consumer("queue_changelog", "consumer_1")

        # Still not all done
        assert not coordination_manager.are_all_specialized_consumers_done()

        # Unregister the other
        await coordination_manager.unregister_consumer("queue_worklog", "consumer_2")

        # Now all done
        assert coordination_manager.are_all_specialized_consumers_done()


class TestConsumeIssuesCompletionTracking:
    """Test suite for consume_issues completion tracking."""

    @pytest.mark.asyncio
    async def test_consume_issues_completion_events(self):
        """Test that consume_issues completion is properly tracked."""
        coordination_manager = ScalableCoordinationManager()

        # Initially not complete
        assert not coordination_manager.is_consume_issues_complete()
        assert not coordination_manager.are_all_consume_issues_complete()

        # Signal completion
        await coordination_manager.signal_consume_issues_complete("consumer_1")

        # Should be complete now
        assert coordination_manager.is_consume_issues_complete()
        assert coordination_manager.are_all_consume_issues_complete()

    @pytest.mark.asyncio
    async def test_specialized_consumers_done_with_consume_issues_tracking(self):
        """Test that specialized consumers are considered done only when consume_issues is complete."""
        coordination_manager = ScalableCoordinationManager()

        # Mock empty queues
        for queue_name in ["queue_changelog", "queue_worklog", "queue_comment", "queue_issue_links", "queue_issue"]:
            mock_queue = MagicMock()
            mock_queue.qsize.return_value = 0
            coordination_manager.register_queue(queue_name, mock_queue)

        # No consumers registered, but consume_issues not complete
        assert not coordination_manager.are_all_specialized_consumers_done()

        # Signal consume_issues completion
        await coordination_manager.signal_consume_issues_complete("consumer_1")

        # Now should be done (no consumers, empty queues, consume_issues complete)
        assert coordination_manager.are_all_specialized_consumers_done()

    @pytest.mark.asyncio
    async def test_specialized_queue_termination_logic(self):
        """Test termination logic for specialized queues."""
        coordination_manager = ScalableCoordinationManager()

        # Mock empty queue
        mock_queue = MagicMock()
        mock_queue.qsize.return_value = 0
        coordination_manager.register_queue("queue_changelog", mock_queue)

        # Should not terminate yet (consume_issues not complete)
        should_terminate = await coordination_manager.should_terminate_consumer("queue_changelog")
        assert not should_terminate

        # Signal consume_issues completion
        await coordination_manager.signal_consume_issues_complete("consumer_1")

        # Now should terminate (consume_issues complete and queue empty)
        should_terminate = await coordination_manager.should_terminate_consumer("queue_changelog")
        assert should_terminate

        # Test with non-empty queue
        mock_queue.qsize.return_value = 5
        should_terminate = await coordination_manager.should_terminate_consumer("queue_changelog")
        assert not should_terminate  # Queue not empty

    @pytest.mark.asyncio
    async def test_upsert_queue_termination_with_consume_issues_complete(self):
        """Test that upsert queue terminates correctly when consume_issues is complete."""
        coordination_manager = ScalableCoordinationManager()

        # Mock empty upsert queue
        mock_queue = MagicMock()
        mock_queue.qsize.return_value = 0
        coordination_manager.register_queue("queue_upsert_issue", mock_queue)

        # Mock empty specialized queues
        for queue_name in ["queue_changelog", "queue_worklog", "queue_comment", "queue_issue_links", "queue_issue"]:
            mock_specialized_queue = MagicMock()
            mock_specialized_queue.qsize.return_value = 0
            coordination_manager.register_queue(queue_name, mock_specialized_queue)

        # Should not terminate yet (specialized consumers might still be maintained)
        should_terminate = await coordination_manager.should_terminate_consumer("queue_upsert_issue")
        assert not should_terminate

        # Signal consume_issues completion
        await coordination_manager.signal_consume_issues_complete("consumer_1")

        # Now should terminate (no specialized consumers, all queues empty)
        should_terminate = await coordination_manager.should_terminate_consumer("queue_upsert_issue")
        assert should_terminate


if __name__ == "__main__":
    pytest.main([__file__])
