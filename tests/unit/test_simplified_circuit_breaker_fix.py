"""
Test for SimplifiedCircuitBreaker fixes.
"""
import pytest
import asyncio
import time
from unittest.mock import Mock

from dags.data_pipeline.utils.circuit_breaker import SimplifiedCircuitBreaker, CircuitState


@pytest.mark.asyncio
async def test_wait_for_clearance_initialization():
    """Test that wait_for_clearance properly initializes last_log_time."""
    circuit_breaker = SimplifiedCircuitBreaker()
    
    # Mock the can_execute to return False initially, then True
    call_count = 0
    async def mock_can_execute():
        nonlocal call_count
        call_count += 1
        return call_count > 2  # Return True after 2 calls
    
    circuit_breaker.can_execute = mock_can_execute
    
    # This should not raise a NameError for last_log_time
    start_time = time.time()
    result = await circuit_breaker.wait_for_clearance(timeout=2.0)
    elapsed = time.time() - start_time
    
    assert result is True
    assert 2.0 <= elapsed < 2.1
    assert call_count > 2


@pytest.mark.asyncio
async def test_wait_for_clearance_timeout():
    """Test that wait_for_clearance respects timeout."""
    circuit_breaker = SimplifiedCircuitBreaker()
    
    # Mock can_execute to always return False
    async def mock_can_execute():
        return False
    
    circuit_breaker.can_execute = mock_can_execute
    
    start_time = time.time()
    result = await circuit_breaker.wait_for_clearance(timeout=1.0)
    elapsed = time.time() - start_time
    
    assert result is False
    assert 0.9 <= elapsed <= 1.2  # Should timeout around 1 second


@pytest.mark.asyncio
async def test_wait_for_clearance_logging():
    """Test that wait_for_clearance logs appropriately when stuck."""
    circuit_breaker = SimplifiedCircuitBreaker()
    
    # Mock logger
    mock_logger = Mock()
    circuit_breaker.logger = mock_logger
    
    # Mock can_execute to always return False
    async def mock_can_execute():
        return False
    
    circuit_breaker.can_execute = mock_can_execute
    
    # Test with very short timeout to avoid long waits
    result = await circuit_breaker.wait_for_clearance(timeout=0.1)
    
    assert result is False
    # Logger should have been called for debug message about timeout
    mock_logger.debug.assert_called()


@pytest.mark.asyncio
async def test_circuit_breaker_basic_functionality():
    """Test basic circuit breaker functionality."""
    circuit_breaker = SimplifiedCircuitBreaker()
    
    # Initially should allow execution
    assert await circuit_breaker.can_execute() is True
    
    # Record some errors to test state changes
    await circuit_breaker.record_error(Exception("Test error"))
    
    # Should still work after one error
    assert await circuit_breaker.can_execute() is True
    
    # Record success
    await circuit_breaker.record_success()
    
    # Should still allow execution
    assert await circuit_breaker.can_execute() is True


if __name__ == "__main__":
    asyncio.run(test_wait_for_clearance_initialization())
    asyncio.run(test_wait_for_clearance_timeout())
    asyncio.run(test_wait_for_clearance_logging())
    asyncio.run(test_circuit_breaker_basic_functionality())
    print("✅ All SimplifiedCircuitBreaker tests passed!")
