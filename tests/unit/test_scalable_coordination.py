# coding=utf-8
"""
Tests for the scalable coordination system.

This module tests the event-based coordination, Fibonacci scaling logic,
and dynamic consumer management functionality.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from collections import defaultdict

from dags.data_pipeline.scalable_coordination import (
    FibonacciProducerScaler,
    ScalableCoordinationManager,
    DynamicConsumerManager,
    ScalingConfig,
    QueueType
)


class TestFibonacciProducerScaler:
    """Test suite for Fibonacci-based producer scaling logic."""
    
    def test_fibonacci_scaling_thresholds(self):
        """Test that producer count follows Fibonacci scaling rules."""
        scaler = FibonacciProducerScaler()
        
        # Test specific Fibonacci thresholds
        assert scaler.get_producer_count(1597) == 1
        assert scaler.get_producer_count(2584) == 2
        assert scaler.get_producer_count(4181) == 3
        assert scaler.get_producer_count(6765) == 4
        assert scaler.get_producer_count(10946) == 5
        assert scaler.get_producer_count(1346269) == 15
        
        # Test edge cases
        assert scaler.get_producer_count(0) == 1
        assert scaler.get_producer_count(1) == 1
        assert scaler.get_producer_count(2000000) == 15  # Above max threshold
    
    def test_batch_parameters_calculation(self):
        """Test batch parameter calculation based on record count."""
        scaler = FibonacciProducerScaler()
        
        # Test small record count
        max_batch_size, num_batches = scaler.get_batch_parameters(500)
        assert max_batch_size == 500
        assert num_batches == 1
        
        # Test medium record count
        max_batch_size, num_batches = scaler.get_batch_parameters(10000)
        assert num_batches == 5  # Based on Fibonacci scaling
        assert max_batch_size == 2000  # 10000 / 5
        
        # Test large record count
        max_batch_size, num_batches = scaler.get_batch_parameters(2000000)
        assert num_batches == 15  # Max producers
        assert max_batch_size == 5000  # Capped at 5000
    
    def test_custom_scaling_config(self):
        """Test scaler with custom configuration."""
        custom_config = ScalingConfig(
            max_producers=10,
            fibonacci_thresholds=[(1000, 1), (2000, 2), (5000, 5)]
        )
        scaler = FibonacciProducerScaler(custom_config)
        
        assert scaler.get_producer_count(1500) == 2
        assert scaler.get_producer_count(10000) == 10  # Max producers


class TestScalableCoordinationManager:
    """Test suite for event-based coordination manager."""
    
    @pytest.fixture
    def coordination_manager(self):
        """Create a fresh coordination manager for each test."""
        return ScalableCoordinationManager()
    
    @pytest.mark.asyncio
    async def test_producer_registration_and_completion(self, coordination_manager):
        """Test producer registration and completion signaling."""
        # Register producers
        await coordination_manager.register_producer("producer_1")
        await coordination_manager.register_producer("producer_2")
        
        assert coordination_manager.producer_count == 2
        assert "producer_1" in coordination_manager.active_producers
        assert "producer_2" in coordination_manager.active_producers
        assert not coordination_manager.producers_completed.is_set()
        
        # Complete first producer
        await coordination_manager.unregister_producer("producer_1")
        assert coordination_manager.producer_count == 1
        assert not coordination_manager.producers_completed.is_set()
        
        # Complete second producer
        await coordination_manager.unregister_producer("producer_2")
        assert coordination_manager.producer_count == 0
        assert coordination_manager.producers_completed.is_set()
    
    @pytest.mark.asyncio
    async def test_consumer_registration_and_tracking(self, coordination_manager):
        """Test consumer registration and tracking."""
        # Register consumers for different queues
        await coordination_manager.register_consumer("queue_changelog", "consumer_1")
        await coordination_manager.register_consumer("queue_changelog", "consumer_2")
        await coordination_manager.register_consumer("queue_worklog", "consumer_3")
        
        assert coordination_manager.consumer_counts["queue_changelog"] == 2
        assert coordination_manager.consumer_counts["queue_worklog"] == 1
        
        # Unregister consumer
        await coordination_manager.unregister_consumer("queue_changelog", "consumer_1")
        assert coordination_manager.consumer_counts["queue_changelog"] == 1
    
    @pytest.mark.asyncio
    async def test_queue_monitoring(self, coordination_manager):
        """Test queue size monitoring."""
        # Create mock queue
        mock_queue = MagicMock()
        mock_queue.qsize.return_value = 75
        
        coordination_manager.register_queue("test_queue", mock_queue)
        
        assert coordination_manager.get_queue_size("test_queue") == 75
        assert coordination_manager.get_queue_size("nonexistent_queue") == 0
    
    @pytest.mark.asyncio
    async def test_consumer_termination_logic(self, coordination_manager):
        """Test consumer termination decision logic."""
        # Create mock queue
        mock_queue = MagicMock()
        mock_queue.qsize.return_value = 0  # Empty queue
        coordination_manager.register_queue("test_queue", mock_queue)
        
        # Producers still active
        await coordination_manager.register_producer("producer_1")
        should_terminate = await coordination_manager.should_terminate_consumer("test_queue")
        assert not should_terminate
        
        # All producers completed, queue empty
        await coordination_manager.unregister_producer("producer_1")
        should_terminate = await coordination_manager.should_terminate_consumer("test_queue")
        assert should_terminate
        
        # All producers completed, but queue not empty
        mock_queue.qsize.return_value = 5
        should_terminate = await coordination_manager.should_terminate_consumer("test_queue")
        assert not should_terminate
    
    @pytest.mark.asyncio
    async def test_consumer_scaling_decisions(self, coordination_manager):
        """Test consumer scaling decision logic."""
        config = ScalingConfig(consumer_scale_threshold=50, max_consumers_per_queue=3)
        coordination_manager.config = config
        
        # Mock queue with high load
        mock_queue = MagicMock()
        mock_queue.qsize.return_value = 75
        coordination_manager.register_queue("test_queue", mock_queue)
        coordination_manager.consumer_counts["test_queue"] = 1
        
        scaling_action = await coordination_manager.should_scale_consumers("test_queue")
        assert scaling_action == 'up'
        
        # Mock queue with low load and multiple consumers
        mock_queue.qsize.return_value = 25
        coordination_manager.consumer_counts["test_queue"] = 2
        
        scaling_action = await coordination_manager.should_scale_consumers("test_queue")
        assert scaling_action == 'down'
        
        # No scaling needed
        mock_queue.qsize.return_value = 30
        coordination_manager.consumer_counts["test_queue"] = 1
        
        scaling_action = await coordination_manager.should_scale_consumers("test_queue")
        assert scaling_action is None
    
    @pytest.mark.asyncio
    async def test_early_processing_trigger(self, coordination_manager):
        """Test early processing trigger mechanism."""
        # Initially not triggered
        assert not coordination_manager.consume_issue_complete.is_set()
        assert not coordination_manager.immediate_processing.is_set()
        
        # Signal completion
        await coordination_manager.signal_consume_issue_complete()
        
        assert coordination_manager.consume_issue_complete.is_set()
        assert coordination_manager.immediate_processing.is_set()
        
        # Test waiting for trigger
        triggered = await coordination_manager.wait_for_processing_trigger()
        assert triggered is True
    
    @pytest.mark.asyncio
    async def test_shutdown_coordination(self, coordination_manager):
        """Test shutdown coordination."""
        assert not coordination_manager.is_shutdown_requested()
        
        await coordination_manager.request_shutdown()
        
        assert coordination_manager.is_shutdown_requested()
        assert coordination_manager.shutdown_requested.is_set()


class TestDynamicConsumerManager:
    """Test suite for dynamic consumer management."""
    
    @pytest.fixture
    def coordination_manager(self):
        """Create coordination manager for testing."""
        return ScalableCoordinationManager()
    
    @pytest.fixture
    def dynamic_manager(self, coordination_manager):
        """Create dynamic consumer manager for testing."""
        return DynamicConsumerManager(coordination_manager)
    
    @pytest.mark.asyncio
    async def test_consumer_scaling_up(self, dynamic_manager, coordination_manager):
        """Test scaling up consumers when queue size exceeds threshold."""
        # Mock consumer factory
        mock_consumer = AsyncMock()
        consumer_factory = AsyncMock(return_value=mock_consumer)
        
        # Mock queue with high load
        mock_queue = MagicMock()
        mock_queue.qsize.return_value = 75
        coordination_manager.register_queue("test_queue", mock_queue)
        coordination_manager.producer_count = 2  # Simulate active producers
        
        # Start scaling management
        scaling_task = asyncio.create_task(
            dynamic_manager.manage_queue_scaling(
                "test_queue",
                consumer_factory,
                ("arg1", "arg2"),
                {"kwarg1": "value1"},
                check_interval=0.1
            )
        )
        
        # Let it run briefly
        await asyncio.sleep(0.2)
        
        # Should have scaled up
        assert len(dynamic_manager.consumer_tasks["test_queue"]) > 0
        
        # Cleanup
        scaling_task.cancel()
        try:
            await scaling_task
        except asyncio.CancelledError:
            pass
    
    @pytest.mark.asyncio
    async def test_consumer_termination_on_completion(self, dynamic_manager, coordination_manager):
        """Test consumer termination when all producers are done."""
        # Mock consumer factory
        mock_consumer = AsyncMock()
        consumer_factory = AsyncMock(return_value=mock_consumer)
        
        # Mock empty queue
        mock_queue = MagicMock()
        mock_queue.qsize.return_value = 0
        coordination_manager.register_queue("test_queue", mock_queue)
        
        # Register and then complete producer
        await coordination_manager.register_producer("producer_1")
        await coordination_manager.unregister_producer("producer_1")
        
        # Start scaling management
        scaling_task = asyncio.create_task(
            dynamic_manager.manage_queue_scaling(
                "test_queue",
                consumer_factory,
                ("arg1", "arg2"),
                {"kwarg1": "value1"},
                check_interval=0.1
            )
        )
        
        # Let it run briefly
        await asyncio.sleep(0.2)
        
        # Should have terminated
        assert scaling_task.done()
        assert len(dynamic_manager.consumer_tasks["test_queue"]) == 0


@pytest.mark.asyncio
async def test_integration_fibonacci_scaling_with_coordination():
    """Integration test combining Fibonacci scaling with coordination."""
    scaler = FibonacciProducerScaler()
    coordination_manager = ScalableCoordinationManager()
    
    # Test scaling for different record counts
    record_counts = [1000, 5000, 50000, 500000, 2000000]
    
    for record_count in record_counts:
        producer_count = scaler.get_producer_count(record_count)
        max_batch_size, num_batches = scaler.get_batch_parameters(record_count)
        
        # Register producers based on scaling decision
        for i in range(producer_count):
            await coordination_manager.register_producer(f"producer_{i}")
        
        assert coordination_manager.producer_count == producer_count
        assert num_batches == producer_count
        
        # Complete all producers
        for i in range(producer_count):
            await coordination_manager.unregister_producer(f"producer_{i}")
        
        assert coordination_manager.producers_completed.is_set()
        
        # Reset for next iteration
        coordination_manager.producers_completed.clear()
        coordination_manager.active_producers.clear()


@pytest.mark.asyncio
async def test_split_jql_fibonacci_integration():
    """Test integration of Fibonacci scaling with split_jql_by_count function."""
    from unittest.mock import patch, AsyncMock

    # Mock the split_jql_by_count function dependencies
    with patch('dags.data_pipeline.utility_code.fetch_with_retries_post') as mock_fetch:
        # Mock JIRA API response
        mock_fetch.return_value = {
            'success': True,
            'result': {'count': 10000}  # 10k records should trigger scaling
        }

        # Mock JIRA entry
        mock_jira_entry = MagicMock()
        mock_jira_entry.url = "https://test.atlassian.net"
        mock_jira_entry.custom_properties = {"Authorization": "Bearer test"}

        # Mock logger
        mock_logger = MagicMock()

        # Import and test the function
        from dags.data_pipeline.utility_code import split_jql_by_count

        # Test with auto-scaling (None parameters)
        with patch('aiohttp.ClientSession') as mock_session:
            mock_session.return_value.__aenter__.return_value = AsyncMock()

            jql_queries, total_count = await split_jql_by_count(
                "project = TEST",
                max_batch_size=None,  # Should auto-calculate
                num_batches=None,     # Should auto-calculate
                jira_entry=mock_jira_entry,
                my_logger=mock_logger
            )

            # Should have used Fibonacci scaling
            assert total_count == 10000
            # For 10k records, should get 5 producers based on Fibonacci scaling


@pytest.mark.asyncio
async def test_process_upsert_queue_early_trigger():
    """Test early processing trigger in process_upsert_queue."""
    from unittest.mock import patch, AsyncMock, MagicMock
    from collections import defaultdict

    # Mock dependencies
    mock_q_container = MagicMock()
    mock_queue = AsyncMock()
    mock_q_container.queue_selector.return_value = {"queue_upsert_issue": mock_queue}

    mock_db_async = MagicMock()
    mock_logger = MagicMock()

    # Mock coordination manager
    with patch('dags.data_pipeline.utility_code.coordination_manager') as mock_coord:
        mock_coord.should_terminate_consumer = AsyncMock(return_value=False)
        mock_coord.immediate_processing.is_set.return_value = True
        mock_coord.immediate_processing.clear = MagicMock()

        # Mock priority queue manager
        with patch('dags.data_pipeline.utility_code.priority_queue_manager') as mock_pqm:
            mock_pqm.get_priority_message = AsyncMock(side_effect=[
                {"model": MagicMock(__name__="TestModel"), "df": MagicMock()},
                None  # Termination
            ])

            # Mock the processing function
            with patch('dags.data_pipeline.utility_code._process_consolidated_dataframes') as mock_process:
                mock_process.return_value = None

                # Import and test
                from dags.data_pipeline.utility_code import process_upsert_queue

                # This should trigger early processing
                try:
                    await process_upsert_queue(
                        project_key="TEST",
                        q_container=mock_q_container,
                        db_rw_async={"TEST": mock_db_async},
                        my_logger=mock_logger
                    )
                except Exception:
                    pass  # Expected due to mocking

                # Verify early processing was triggered
                mock_coord.immediate_processing.clear.assert_called()


class TestEventBasedTermination:
    """Test suite for event-based termination logic."""

    @pytest.mark.asyncio
    async def test_producer_consumer_coordination(self):
        """Test full producer-consumer coordination cycle."""
        coordination_manager = ScalableCoordinationManager()

        # Simulate producer lifecycle
        producers = ["producer_1", "producer_2", "producer_3"]
        for producer in producers:
            await coordination_manager.register_producer(producer)

        # Simulate consumer registration
        consumers = ["consumer_1", "consumer_2"]
        for consumer in consumers:
            await coordination_manager.register_consumer("queue_test", consumer)

        # Mock queue
        mock_queue = MagicMock()
        mock_queue.qsize.return_value = 10  # Non-empty queue
        coordination_manager.register_queue("queue_test", mock_queue)

        # Consumers should not terminate while producers are active
        should_terminate = await coordination_manager.should_terminate_consumer("queue_test")
        assert not should_terminate

        # Complete all producers
        for producer in producers:
            await coordination_manager.unregister_producer(producer)

        # Queue still has items, should not terminate
        should_terminate = await coordination_manager.should_terminate_consumer("queue_test")
        assert not should_terminate

        # Empty the queue
        mock_queue.qsize.return_value = 0
        should_terminate = await coordination_manager.should_terminate_consumer("queue_test")
        assert should_terminate

    @pytest.mark.asyncio
    async def test_graceful_shutdown_sequence(self):
        """Test graceful shutdown sequence."""
        coordination_manager = ScalableCoordinationManager()
        dynamic_manager = DynamicConsumerManager(coordination_manager)

        # Register components
        await coordination_manager.register_producer("producer_1")
        await coordination_manager.register_consumer("queue_test", "consumer_1")

        # Request shutdown
        await coordination_manager.request_shutdown()

        # Verify shutdown state
        assert coordination_manager.is_shutdown_requested()

        # Simulate cleanup
        await coordination_manager.unregister_producer("producer_1")
        await coordination_manager.unregister_consumer("queue_test", "consumer_1")

        assert coordination_manager.producer_count == 0
        assert coordination_manager.consumer_counts["queue_test"] == 0


if __name__ == "__main__":
    pytest.main([__file__])
