# coding=utf-8
"""
Unit tests for referential integrity in the Protocol-based queue processors.

This module tests that the new simplified queue processors maintain proper
referential integrity between parent (Issue) and child tables.
"""
import pytest
import asyncio
import pandas as pd
from unittest.mock import AsyncMock, MagicMock, patch
from logging import Logger

from dags.data_pipeline.queue_processors.protocols import (
    ReferentialIntegrityManager,
    SimpleCoordinator
)
from dags.data_pipeline.queue_processors.dataframe_processor import DataFrameProcessor
from dags.data_pipeline.queue_processors.simple_processors import (
    IssueQueueProcessor,
    OthersQueueProcessor
)


class TestReferentialIntegrityManager:
    """Test the ReferentialIntegrityManager implementation."""
    
    def test_table_classification(self):
        """Test that tables are correctly classified as parent or child."""
        manager = ReferentialIntegrityManager()
        
        # Test parent table
        assert manager.is_parent_table("Issue") is True
        assert manager.is_child_table("Issue") is False
        
        # Test child tables
        assert manager.is_child_table("ChangelogJSON") is True
        assert manager.is_parent_table("ChangelogJSON") is False
        
        assert manager.is_child_table("IssueComments") is True
        assert manager.is_child_table("IssueLinks") is True
        assert manager.is_child_table("Worklog") is True
        
        # Test unknown table
        assert manager.is_parent_table("UnknownTable") is False
        assert manager.is_child_table("UnknownTable") is False
    
    @pytest.mark.asyncio
    async def test_parent_commitment_signaling(self):
        """Test parent table commitment signaling."""
        manager = ReferentialIntegrityManager()
        
        # Initially, parent tables should not be committed
        result = await manager.wait_for_parent_commitment(timeout=0.1)
        assert result is False
        
        # Signal commitment
        await manager.signal_parent_tables_committed()
        
        # Now waiting should succeed immediately
        result = await manager.wait_for_parent_commitment(timeout=0.1)
        assert result is True
    
    @pytest.mark.asyncio
    async def test_timeout_behavior(self):
        """Test timeout behavior when parent tables are not committed."""
        manager = ReferentialIntegrityManager()
        
        # Should timeout and return False
        result = await manager.wait_for_parent_commitment(timeout=0.1)
        assert result is False


class TestDataFrameProcessor:
    """Test the DataFrameProcessor referential integrity logic."""
    
    @pytest.mark.asyncio
    async def test_processing_order(self):
        """Test that Issue table is processed before child tables."""
        # Mock logger
        logger = MagicMock(spec=Logger)
        
        # Create test DataFrames
        issue_df = pd.DataFrame({"id": [1, 2], "key": ["TEST-1", "TEST-2"], "issue_hierarchy_level": [0, 0]})
        changelog_df = pd.DataFrame({"id": [1, 2], "issue_id": [1, 2]})
        
        consolidated_dataframes = {
            "ChangelogJSON": [changelog_df],  # Child table first in dict
            "Issue": [issue_df]  # Parent table second
        }
        
        consolidated_configs = {
            "Issue": {
                "model": MagicMock(__name__="Issue"),
                "no_update_cols": (),
                "on_conflict_update": True,
                "conflict_condition": None
            },
            "ChangelogJSON": {
                "model": MagicMock(__name__="ChangelogJSON"),
                "no_update_cols": (),
                "on_conflict_update": True,
                "conflict_condition": None
            }
        }
        
        # Mock the referential integrity manager
        mock_integrity = AsyncMock()
        mock_integrity.is_child_table.return_value = True
        mock_integrity.wait_for_parent_commitment.return_value = True
        
        processor = DataFrameProcessor(referential_integrity=mock_integrity, logger=logger)
        
        # Mock the database processing methods
        with patch.object(processor, '_process_issue_hierarchy_levels') as mock_issue_process, \
             patch.object(processor, '_process_single_model') as mock_single_process:
            
            await processor.process_consolidated_dataframes(
                project_key="TEST",
                consolidated_dataframes=consolidated_dataframes,
                consolidated_configs=consolidated_configs,
                logger=logger,
                message_count=1
            )
            
            # Verify Issue was processed first (called once)
            mock_issue_process.assert_called_once()
            
            # Verify child table was processed after parent commitment
            mock_single_process.assert_called_once()
            mock_integrity.wait_for_parent_commitment.assert_called_once()
            mock_integrity.signal_parent_tables_committed.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_hierarchy_level_processing_order(self):
        """Test that Issue hierarchy levels are processed in correct order."""
        logger = MagicMock(spec=Logger)
        
        # Create test DataFrame with different hierarchy levels
        issue_df = pd.DataFrame({
            "id": [1, 2, 3, 4],
            "key": ["INIT-1", "EPIC-1", "STORY-1", "SUB-1"],
            "issue_hierarchy_level": [2, 1, 0, -1]  # Initiative, Epic, Story, Subtask
        })
        
        processor = DataFrameProcessor(logger=logger)
        
        # Mock the database session and upsert function
        with patch('dags.data_pipeline.queue_processors.dataframe_processor.upsert_async') as mock_upsert:
            mock_upsert.return_value = True
            
            # Mock the database session manager
            mock_session_manager = MagicMock()
            mock_session = AsyncMock()
            mock_session_manager.__getitem__.return_value.async_session.return_value.__aenter__.return_value = mock_session
            mock_session.begin.return_value.__aenter__.return_value = mock_session
            
            with patch('dags.data_pipeline.queue_processors.dataframe_processor.Provide') as mock_provide:
                mock_provide.__getitem__.return_value = mock_session_manager
                
                model = MagicMock(__name__="Issue")
                config = {
                    "no_update_cols": (),
                    "on_conflict_update": True,
                    "conflict_condition": None
                }
                
                await processor._process_issue_hierarchy_levels(
                    project_key="TEST",
                    df=issue_df,
                    model=model,
                    config=config,
                    logger=logger,
                    message_count=1
                )
                
                # Verify upsert was called 4 times (once for each hierarchy level)
                assert mock_upsert.call_count == 4
                
                # Verify the order of processing by checking the DataFrames passed to upsert
                call_args_list = mock_upsert.call_args_list
                
                # First call should be Initiative (hierarchy_level=2)
                first_df = call_args_list[0][0][2]  # Third argument is the DataFrame
                assert first_df["issue_hierarchy_level"].iloc[0] == 2
                
                # Second call should be Epic (hierarchy_level=1)
                second_df = call_args_list[1][0][2]
                assert second_df["issue_hierarchy_level"].iloc[0] == 1
                
                # Third call should be Story (hierarchy_level=0)
                third_df = call_args_list[2][0][2]
                assert third_df["issue_hierarchy_level"].iloc[0] == 0
                
                # Fourth call should be Subtask (hierarchy_level=-1)
                fourth_df = call_args_list[3][0][2]
                assert fourth_df["issue_hierarchy_level"].iloc[0] == -1


class TestQueueProcessorIntegration:
    """Test integration between IssueQueueProcessor and OthersQueueProcessor."""
    
    @pytest.mark.asyncio
    async def test_referential_integrity_coordination(self):
        """Test that OthersQueueProcessor waits for IssueQueueProcessor completion."""
        # Create mock coordination components
        mock_integrity = AsyncMock()
        mock_coordinator = AsyncMock()
        
        # Create processors
        issue_processor = IssueQueueProcessor(
            referential_integrity=mock_integrity,
            coordinator=mock_coordinator
        )
        
        others_processor = OthersQueueProcessor(
            referential_integrity=mock_integrity,
            coordinator=mock_coordinator
        )
        
        # Mock queue and coordination manager
        mock_queue = AsyncMock()
        mock_queue.empty.return_value = True
        mock_coordination_manager = AsyncMock()
        mock_coordination_manager.should_terminate_consumer.return_value = True
        
        logger = MagicMock(spec=Logger)
        
        # Process issue queue first
        await issue_processor.process_queue(
            project_key="TEST",
            queue=mock_queue,
            coordination_manager=mock_coordination_manager,
            logger=logger
        )
        
        # Verify issue processor signaled completion
        mock_coordinator.signal_processing_complete.assert_called_with("process_upsert_queue")
        
        # Process others queue
        mock_integrity.wait_for_parent_commitment.return_value = True
        
        await others_processor.process_queue(
            project_key="TEST",
            queue=mock_queue,
            coordination_manager=mock_coordination_manager,
            logger=logger
        )
        
        # Verify others processor waited for parent commitment
        mock_integrity.wait_for_parent_commitment.assert_called()
        mock_coordinator.signal_processing_complete.assert_called_with("process_upsert_others")


@pytest.mark.asyncio
async def test_end_to_end_referential_integrity():
    """End-to-end test of referential integrity flow."""
    # This test verifies the complete flow:
    # 1. Issue data is processed first
    # 2. Parent tables are committed
    # 3. Child tables wait for parent commitment
    # 4. Child tables are processed
    
    integrity_manager = ReferentialIntegrityManager()
    coordinator = SimpleCoordinator()
    
    # Simulate the flow
    processing_order = []
    
    async def mock_issue_processing():
        processing_order.append("issue_start")
        await asyncio.sleep(0.1)  # Simulate processing time
        await integrity_manager.signal_parent_tables_committed()
        processing_order.append("issue_committed")
        await coordinator.signal_processing_complete("process_upsert_queue")
        processing_order.append("issue_complete")
    
    async def mock_others_processing():
        processing_order.append("others_start")
        # Wait for parent commitment
        committed = await integrity_manager.wait_for_parent_commitment(timeout=1.0)
        assert committed is True
        processing_order.append("others_processing")
        await coordinator.signal_processing_complete("process_upsert_others")
        processing_order.append("others_complete")
    
    # Run both processors concurrently
    await asyncio.gather(
        mock_issue_processing(),
        mock_others_processing()
    )
    
    # Verify the correct order
    expected_order = [
        "issue_start",
        "others_start",  # Others can start but will wait
        "issue_committed",  # Issue commits first
        "others_processing",  # Others proceeds after commitment
        "issue_complete",
        "others_complete"
    ]
    
    assert processing_order == expected_order
