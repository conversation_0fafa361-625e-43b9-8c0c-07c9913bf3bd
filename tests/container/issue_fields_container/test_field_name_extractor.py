import os

import pytest
import allure
import yaml

from dags.data_pipeline.containers import FieldNameExtractor


# Load the YAML configuration for testing

@pytest.fixture(scope="module")
def config():
    config_path = os.getenv("ISSUE_FIELDS_YAML_FILE", "dags/data_pipeline/issue_fields.yaml")
    with open(config_path, "r") as file:
        return yaml.safe_load(file)


@pytest.fixture(scope="module")
def field_name_extractor(config):
    return FieldNameExtractor(config=config)

@allure.feature("FieldNameExtractor")
class TestFieldNameExtractor:

    @allure.story("Extract field names")
    @allure.title("Verify all field names are extracted correctly")
    def test_get_field_names(self, field_name_extractor):
        with allure.step("Extract field names"):
            field_names = field_name_extractor.get_field_names()
        with allure.step("Verify the field names match the expected values"):
            assert len(field_names) > 0, "No field names were extracted"
            assert "aggregateprogress" in field_names, "Field ID 'aggregateprogress' is missing"

    @allure.story("Extract field IDs by datatype")
    @allure.title("Verify field IDs for a specific datatype")
    @pytest.mark.parametrize("datatype,expected_ids", [
        ("progress", ["aggregateprogress", "progress"]),
        ("number", [
            'aggregatetimeestimate', 'aggregatetimeoriginalestimate', 'aggregatetimespent',
            'customfield_10120', 'customfield_10121', 'customfield_10122',
            'customfield_10123', 'customfield_10124', 'customfield_10125',
            'customfield_10126', 'customfield_10199',
            'timeestimate', 'timeoriginalestimate', 'timespent'
        ]),
        ("user", ["assignee", "reporter"]),
        ("datetime", ["created", "resolutiondate", "updated", "statuscategorychangedate"]),
        ("nonexistent", []),  # Test for no matches
    ])
    def test_get_field_ids_by_datatype(self, field_name_extractor, datatype, expected_ids):
        with allure.step(f"Extract field IDs for datatype '{datatype}'"):
            result = field_name_extractor.get_field_ids_by_datatype(datatype)
        with allure.step("Verify the field IDs match the expected values"):
            assert sorted(result) == sorted(expected_ids), f"Field IDs for datatype '{datatype}' do not match"

