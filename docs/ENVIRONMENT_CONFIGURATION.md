# Environment Configuration Guide

This guide explains how to use the new environment configuration system with `python-dotenv` integration in the JIRA Data Pipeline project.

## Overview

The environment configuration system provides:
- **Environment-specific configuration files** (`.env.development`, `.env.staging`, `.env.production`)
- **Automatic environment detection** and configuration loading
- **Type conversion and validation** for environment variables
- **Integration with existing YAML configurations** for complex settings
- **Fallback mechanisms** for missing configurations

## Quick Start

1. **Copy the example environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Customize your environment variables:**
   ```bash
   # Edit .env with your specific settings
   nano .env
   ```

3. **Set your environment:**
   ```bash
   export ENVIRONMENT=development  # or staging, production
   ```

4. **Use in your code:**
   ```python
   from dags.data_pipeline.utils.env_config import get_env_config
   
   env_config = get_env_config()
   db_config = env_config.get_database_config()
   print(f"Database host: {db_config.host}")
   ```

## Environment Files

### File Structure
```
.env                    # Base environment file (loaded for all environments)
.env.example           # Template with all available variables
.env.development       # Development-specific settings
.env.staging          # Staging-specific settings
.env.production       # Production-specific settings
```

### Loading Priority
1. **Environment-specific file** (`.env.{ENVIRONMENT}`) - highest priority
2. **Base file** (`.env`) - fallback values
3. **System environment variables** - override everything
4. **Default values** - final fallback

## Configuration Categories

### 🗄️ Database Configuration
```bash
# Database Connection
DB_HOST=localhost
DB_PORT_RW=5432
DB_PORT_RO=5433
DB_NAME=alembic_migration
DB_DRIVER=postgresql+psycopg2

# Database Pool Settings
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=10
DB_POOL_RECYCLE=3600
DB_POOL_TIMEOUT=60
DB_POOL_PRE_PING=true

# Schema Configuration
DB_PUBLIC_SCHEMA=public
DB_CUSTOM_SCHEMAS=plat,plp,acq
DB_DEFAULT_SCHEMA=plat
```

### 🔗 JIRA Configuration
```bash
# JIRA Connection
JIRA_BASE_URL=https://corecard.atlassian.net
JIRA_ACCEPT_HEADER=application/json
JIRA_CONTENT_TYPE=application/json

# JIRA Rate Limiting & Retry Logic
JIRA_MAX_RETRIES=5
JIRA_INITIAL_RETRY_DELAY=5000
JIRA_MAX_RETRY_DELAY=10000
JIRA_JITTER_MIN=0.5
JIRA_JITTER_MAX=1.5
```

### ⚡ Circuit Breaker Configuration
```bash
# Circuit Breaker Settings
CB_FAILURE_THRESHOLD=5
CB_RECOVERY_TIMEOUT=30.0
CB_HALF_OPEN_MAX_CALLS=3
CB_MAX_CONSECUTIVE_RATE_LIMITS=3
CB_CONNECTION_POOL_RETRY_MULTIPLIER=2.0
CB_NETWORK_ERROR_RETRY_MULTIPLIER=1.5
CB_MAX_CONCURRENT_CONNECTIONS=50
CB_HEALTH_CHECK_INTERVAL=10.0
```

### 📝 Logging Configuration
```bash
# Logging Configuration
LOGGING_CONFIG_PATH=logging_config.yaml
LOG_LEVEL=INFO
LOG_FILE_LEVEL=DEBUG
LOG_CONSOLE_LEVEL=WARNING

# Database Logging
DB_LOG_BATCH_SIZE=50
DB_LOG_FLUSH_INTERVAL=30
DB_LOG_ENABLED=true

# Debug Monitoring
ENABLE_DEBUG_MONITOR=true
DEBUG_MONITOR_INTERVAL=30
```

### 🔐 KeePass Configuration
```bash
# KeePass Database Paths
KEEPASS_DB_PATH=C:/KeePass/Database.kdbx
KEEPASS_KEY_PATH=C:/KeePass/Database.key
KEEPASS_JIRA_ENTRY=corecard Jira
```

## Usage Examples

### Basic Usage
```python
from dags.data_pipeline.utils.env_config import get_env_config

# Get the global environment configuration
env_config = get_env_config()

# Get typed configuration objects
db_config = env_config.get_database_config()
jira_config = env_config.get_jira_config()
cb_config = env_config.get_circuit_breaker_config()
log_config = env_config.get_logging_config()

print(f"Database: {db_config.host}:{db_config.port_rw}")
print(f"JIRA: {jira_config.base_url}")
```

### Individual Environment Variables
```python
from dags.data_pipeline.utils.env_config import get_env_config

env_config = get_env_config()

# Get individual variables with type conversion
debug_mode = env_config.get_env_var("DEBUG_MODE", bool, False)
queue_size = env_config.get_env_var("QUEUE_ISSUES_SIZE", int, 1000)
schemas = env_config.get_env_var("DB_CUSTOM_SCHEMAS", list, ["plat", "plp"])

# With validation
def validate_port(port):
    return 1024 <= port <= 65535

db_port = env_config.get_env_var(
    "DB_PORT_RW", 
    int, 
    5432, 
    required=True,
    validator=validate_port
)
```

### Container Integration
```python
from dags.data_pipeline.containers import EnhancedApplicationContainer

# The containers automatically use environment configuration
container = EnhancedApplicationContainer()
container.wire(modules=[__name__])

# Database configuration is loaded from environment variables
db_manager = container.database_rw_managed()
```

## Environment-Specific Settings

### Development Environment
- **Verbose logging** (DEBUG level)
- **Smaller connection pools** (5 connections)
- **More lenient circuit breaker** (10 failure threshold)
- **Frequent monitoring** (10-second intervals)
- **SQL query logging enabled**

### Staging Environment
- **Moderate logging** (INFO level)
- **Medium connection pools** (15 connections)
- **Balanced circuit breaker** (7 failure threshold)
- **Regular monitoring** (20-second intervals)
- **Production-like settings** with more monitoring

### Production Environment
- **Minimal logging** (WARNING level)
- **Large connection pools** (20 connections)
- **Conservative circuit breaker** (5 failure threshold)
- **Minimal monitoring** (60-second intervals)
- **Optimized for performance**

## Migration Guide

### From Hardcoded Values
1. **Identify hardcoded configuration** in your code
2. **Add corresponding environment variables** to `.env` files
3. **Update code to use environment configuration:**
   ```python
   # Before
   MAX_RETRIES = 5
   
   # After
   from dags.data_pipeline.utils.env_config import get_env_config
   env_config = get_env_config()
   jira_config = env_config.get_jira_config()
   MAX_RETRIES = jira_config.max_retries
   ```

### From YAML-Only Configuration
1. **Keep complex configurations in YAML** (field mappings, complex structures)
2. **Move simple settings to environment variables** (hosts, ports, timeouts)
3. **Use hybrid approach** in containers:
   ```python
   # Environment variables for simple settings
   env_config = providers.Singleton(EnvironmentConfigLoader)
   database_config = providers.Singleton(
       providers.Callable(
           lambda env_loader: env_loader.get_database_config(),
           env_loader=env_config
       )
   )
   
   # YAML for complex configurations
   config = providers.Configuration(yaml_files=["config.yaml"])
   ```

## Best Practices

### 🔒 Security
- **Never commit sensitive data** to `.env` files
- **Use KeePass for passwords** and sensitive credentials
- **Add `.env*` to `.gitignore`** (except `.env.example`)
- **Use environment-specific files** for different deployment environments

### 🏗️ Configuration Management
- **Use descriptive variable names** with prefixes (DB_, JIRA_, CB_)
- **Provide sensible defaults** for all non-sensitive variables
- **Document all variables** in `.env.example`
- **Validate critical configurations** with custom validators

### 🚀 Deployment
- **Use environment-specific files** for different stages
- **Override with system environment variables** in production
- **Test configuration loading** in CI/CD pipelines
- **Monitor configuration changes** in production

## Troubleshooting

### Common Issues

1. **Environment file not found:**
   ```
   WARNING: Environment file not found: .env.development
   ```
   **Solution:** Create the missing environment file or check the `ENVIRONMENT` variable.

2. **Type conversion errors:**
   ```
   ValueError: Invalid value for required environment variable 'DB_PORT_RW': abc
   ```
   **Solution:** Check the variable value and ensure it matches the expected type.

3. **Import errors:**
   ```
   ImportError: No module named 'dags.data_pipeline.utils.env_config'
   ```
   **Solution:** Ensure the project root is in your Python path.

### Testing Configuration
```bash
# Run the demo script to test configuration loading
python examples/env_config_demo.py

# Test specific environment
ENVIRONMENT=staging python examples/env_config_demo.py
```

## Integration with Existing Systems

The environment configuration system is designed to work alongside existing YAML configurations:

- **Simple settings** → Environment variables
- **Complex structures** → YAML files
- **Sensitive data** → KeePass
- **Environment-specific** → `.env.{environment}` files

This hybrid approach provides flexibility while maintaining security and maintainability.
