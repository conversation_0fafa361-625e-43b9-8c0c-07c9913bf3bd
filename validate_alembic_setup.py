#!/usr/bin/env python3
"""
Alembic Multi-Schema Validation Script

This script validates that the Alembic multi-schema setup is working correctly
for PostgreSQL databases. It checks configuration, schema access, and migration status.

Usage:
    python validate_alembic_setup.py
"""

import os
import sys
import subprocess
import logging
from pathlib import Path
from typing import List, Dict, Any
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.exc import SQLAlchemyError, ProgrammingError

# Add the project root to Python path for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import environment configuration
try:
    from dags.data_pipeline.utils.env_config import get_env_config
    env_config = get_env_config()
    db_config = env_config.get_database_config()
    ENV_CONFIG_AVAILABLE = True
except ImportError:
    ENV_CONFIG_AVAILABLE = False
    logger.warning("Environment configuration not available, using hardcoded values")

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration from environment variables or fallback to hardcoded values
if ENV_CONFIG_AVAILABLE:
    PUBLIC_SCHEMA = db_config.public_schema
    CUSTOM_SCHEMAS = db_config.custom_schemas
    ALL_SCHEMAS = [PUBLIC_SCHEMA] + CUSTOM_SCHEMAS
    # Database connection details from environment variables
    DATABASE_URL = f"{db_config.driver}://postgres:Dope%40123@{db_config.host}:{db_config.port_rw}/{db_config.name}"
else:
    # Fallback to hardcoded values
    PUBLIC_SCHEMA = 'public'
    CUSTOM_SCHEMAS = ['plat', 'plp', 'acq']
    ALL_SCHEMAS = [PUBLIC_SCHEMA] + CUSTOM_SCHEMAS
    DATABASE_URL = "postgresql+psycopg2://postgres:Dope%40123@localhost/alembic_migration"


class AlembicValidator:
    """Validates Alembic multi-schema configuration."""
    
    def __init__(self, database_url: str):
        self.database_url = database_url
        self.engine = None
        self.errors = []
        self.warnings = []
        
    def connect_to_database(self) -> bool:
        """Establish database connection."""
        try:
            self.engine = create_engine(self.database_url)
            with self.engine.connect() as conn:
                result = conn.execute(text("SELECT version()"))
                version = result.fetchone()[0]
                logger.info(f"Connected to PostgreSQL: {version}")
                return True
        except SQLAlchemyError as e:
            self.errors.append(f"Database connection failed: {e}")
            return False
    
    def validate_schemas_exist(self) -> bool:
        """Check if all required schemas exist."""
        logger.info("Validating schema existence...")
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("""
                    SELECT schema_name 
                    FROM information_schema.schemata 
                    WHERE schema_name = ANY(:schemas)
                """), {"schemas": list(ALL_SCHEMAS)})
                
                existing_schemas = {row[0] for row in result.fetchall()}
                missing_schemas = set(ALL_SCHEMAS) - existing_schemas
                
                if missing_schemas:
                    self.warnings.append(f"Missing schemas: {missing_schemas}")
                    logger.warning(f"Missing schemas: {missing_schemas}")
                else:
                    logger.info("✓ All required schemas exist")
                    
                return len(missing_schemas) == 0
                
        except SQLAlchemyError as e:
            self.errors.append(f"Schema validation failed: {e}")
            return False
    
    def validate_alembic_version_tables(self) -> bool:
        """Check if alembic_version tables exist in all schemas."""
        logger.info("Validating alembic_version tables...")
        missing_version_tables = []
        
        try:
            with self.engine.connect() as conn:
                inspector = inspect(conn)
                
                for schema in ALL_SCHEMAS:
                    tables = inspector.get_table_names(schema=schema)
                    if 'alembic_version' not in tables:
                        missing_version_tables.append(schema)
                        
                if missing_version_tables:
                    self.warnings.append(f"Missing alembic_version tables in schemas: {missing_version_tables}")
                    logger.warning(f"Missing alembic_version tables in schemas: {missing_version_tables}")
                else:
                    logger.info("✓ All schemas have alembic_version tables")
                    
                return len(missing_version_tables) == 0
                
        except SQLAlchemyError as e:
            self.errors.append(f"Alembic version table validation failed: {e}")
            return False
    
    def get_migration_status(self) -> Dict[str, str]:
        """Get current migration revision for each schema."""
        logger.info("Checking migration status...")
        status = {}
        
        for schema in ALL_SCHEMAS:
            try:
                result = subprocess.run(
                    ['uv', 'run', 'alembic', '-x', f'schema={schema}', 'current'],
                    capture_output=True,
                    text=True,
                    check=False
                )
                
                if result.returncode == 0:
                    # Parse the output to get the revision
                    lines = result.stdout.strip().split('\n')
                    revision_line = [line for line in lines if line and not line.startswith('INFO')]
                    if revision_line:
                        status[schema] = revision_line[-1].strip()
                    else:
                        status[schema] = "No revision found"
                else:
                    status[schema] = f"Error: {result.stderr}"
                    
            except Exception as e:
                status[schema] = f"Exception: {e}"
                
        return status
    
    def validate_table_distribution(self) -> bool:
        """Validate that tables are in the correct schemas."""
        logger.info("Validating table distribution across schemas...")
        
        expected_public_tables = {
            'user', 'all_boards', 'issue_fields', 'nlp_training_data',
            'permission', 'role', 'requesttracker', 'deleted_worklog'
        }
        
        expected_custom_tables = {
            'issue', 'issue_comments', 'issue_links', 'worklog'
        }
        
        try:
            with self.engine.connect() as conn:
                inspector = inspect(conn)
                
                # Check public schema
                public_tables = set(inspector.get_table_names(schema=PUBLIC_SCHEMA))
                missing_public = expected_public_tables - public_tables
                extra_public = public_tables - expected_public_tables - {'alembic_version'}
                
                if missing_public:
                    self.warnings.append(f"Missing tables in public schema: {missing_public}")
                if extra_public:
                    logger.info(f"Extra tables in public schema: {extra_public}")
                
                # Check custom schemas
                for schema in CUSTOM_SCHEMAS:
                    custom_tables = set(inspector.get_table_names(schema=schema))
                    missing_custom = expected_custom_tables - custom_tables
                    
                    if missing_custom:
                        self.warnings.append(f"Missing tables in {schema} schema: {missing_custom}")
                
                logger.info("✓ Table distribution validation completed")
                return len(self.warnings) == 0
                
        except SQLAlchemyError as e:
            self.errors.append(f"Table distribution validation failed: {e}")
            return False
    
    def validate_foreign_keys(self) -> bool:
        """Validate cross-schema foreign key constraints."""
        logger.info("Validating foreign key constraints...")
        
        try:
            with self.engine.connect() as conn:
                # Check for foreign key constraints that reference across schemas
                result = conn.execute(text("""
                    SELECT 
                        tc.table_schema,
                        tc.table_name,
                        kcu.column_name,
                        ccu.table_schema AS foreign_table_schema,
                        ccu.table_name AS foreign_table_name,
                        ccu.column_name AS foreign_column_name
                    FROM information_schema.table_constraints AS tc
                    JOIN information_schema.key_column_usage AS kcu
                        ON tc.constraint_name = kcu.constraint_name
                        AND tc.table_schema = kcu.table_schema
                    JOIN information_schema.constraint_column_usage AS ccu
                        ON ccu.constraint_name = tc.constraint_name
                        AND ccu.table_schema = tc.table_schema
                    WHERE tc.constraint_type = 'FOREIGN KEY'
                        AND tc.table_schema = ANY(:schemas)
                        AND ccu.table_schema != tc.table_schema
                """), {"schemas": list(ALL_SCHEMAS)})
                
                cross_schema_fks = result.fetchall()
                
                if cross_schema_fks:
                    logger.info(f"✓ Found {len(cross_schema_fks)} cross-schema foreign keys")
                    for fk in cross_schema_fks:
                        logger.debug(f"  {fk.table_schema}.{fk.table_name}.{fk.column_name} -> "
                                   f"{fk.foreign_table_schema}.{fk.foreign_table_name}.{fk.foreign_column_name}")
                else:
                    self.warnings.append("No cross-schema foreign keys found")
                
                return True
                
        except SQLAlchemyError as e:
            self.errors.append(f"Foreign key validation failed: {e}")
            return False
    
    def run_validation(self) -> bool:
        """Run complete validation suite."""
        logger.info("Starting Alembic multi-schema validation...")
        
        validation_steps = [
            ("Database Connection", self.connect_to_database),
            ("Schema Existence", self.validate_schemas_exist),
            ("Alembic Version Tables", self.validate_alembic_version_tables),
            ("Table Distribution", self.validate_table_distribution),
            ("Foreign Key Constraints", self.validate_foreign_keys),
        ]
        
        results = {}
        for step_name, step_func in validation_steps:
            logger.info(f"\n--- {step_name} ---")
            try:
                results[step_name] = step_func()
            except Exception as e:
                self.errors.append(f"{step_name} failed with exception: {e}")
                results[step_name] = False
        
        # Get migration status
        logger.info("\n--- Migration Status ---")
        migration_status = self.get_migration_status()
        for schema, status in migration_status.items():
            logger.info(f"{schema}: {status}")
        
        # Print summary
        self.print_summary(results)
        
        return len(self.errors) == 0
    
    def print_summary(self, results: Dict[str, bool]):
        """Print validation summary."""
        logger.info("\n" + "="*60)
        logger.info("VALIDATION SUMMARY")
        logger.info("="*60)
        
        all_passed = all(results.values())
        
        for step, passed in results.items():
            status = "✓ PASS" if passed else "✗ FAIL"
            logger.info(f"{step:<30} {status}")
        
        if self.warnings:
            logger.info(f"\nWarnings ({len(self.warnings)}):")
            for warning in self.warnings:
                logger.warning(f"  • {warning}")
        
        if self.errors:
            logger.info(f"\nErrors ({len(self.errors)}):")
            for error in self.errors:
                logger.error(f"  • {error}")
        
        logger.info("\n" + "="*60)
        if all_passed and not self.errors:
            logger.info("🎉 ALL VALIDATIONS PASSED!")
            logger.info("Your Alembic multi-schema setup is working correctly.")
        else:
            logger.info("❌ SOME VALIDATIONS FAILED!")
            logger.info("Please review the errors and warnings above.")
        logger.info("="*60)


def main():
    """Main execution function."""
    validator = AlembicValidator(DATABASE_URL)
    success = validator.run_validation()
    
    # Return appropriate exit code
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
