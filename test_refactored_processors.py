#!/usr/bin/env python3
# coding=utf-8
"""
Test script for refactored queue processors.

This script tests the refactored process_upsert_queue and process_upsert_others
functions to ensure they work correctly and handle task_done() properly.
"""

import asyncio
import logging
import sys
from unittest.mock import Mock, AsyncMock, MagicMock
import pandas as pd

# Add the project root to the path
sys.path.append('.')

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def test_queue_processor_task_done():
    """Test that task_done() is called properly in the refactored processors."""
    
    try:
        # Import the refactored processors
        from dags.data_pipeline.queue_processors.concrete_processors import (
            UpsertQueueProcessor, UpsertOthersProcessor
        )
        
        logger.info("Successfully imported refactored processors")
        
        # Create mock queue
        mock_queue = AsyncMock()
        mock_queue.qsize.return_value = 0
        mock_queue._unfinished_tasks = 0
        
        # Create mock coordination manager
        mock_coordination_manager = Mock()
        mock_coordination_manager.should_terminate_consumer = AsyncMock(return_value=True)
        mock_coordination_manager.register_queue = AsyncMock()
        
        # Create mock q_container
        mock_q_container = Mock()
        
        # Test UpsertQueueProcessor
        logger.info("Testing UpsertQueueProcessor...")
        processor = UpsertQueueProcessor("TEST_PROJECT", logger)
        
        # Mock the _get_queue_item to return None (simulating empty queue)
        async def mock_get_queue_item(queue):
            return None
            
        processor._get_queue_item = mock_get_queue_item
        
        # Process the queue
        result = await processor.process_queue(mock_queue, mock_coordination_manager, mock_q_container)
        
        logger.info(f"UpsertQueueProcessor result: {result}")
        
        # Verify task_done was called if items were processed
        if mock_queue.task_done.called:
            logger.info("✅ task_done() was called on UpsertQueueProcessor")
        else:
            logger.info("ℹ️  task_done() was not called (no items processed)")
        
        # Test UpsertOthersProcessor
        logger.info("Testing UpsertOthersProcessor...")
        processor2 = UpsertOthersProcessor("TEST_PROJECT", logger)
        processor2._get_queue_item = mock_get_queue_item
        
        result2 = await processor2.process_queue(mock_queue, mock_coordination_manager, mock_q_container)
        
        logger.info(f"UpsertOthersProcessor result: {result2}")
        
        logger.info("✅ Both processors completed successfully")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise


async def test_with_mock_data():
    """Test processors with mock data to verify task_done() behavior."""
    
    try:
        from dags.data_pipeline.queue_processors.concrete_processors import (
            UpsertQueueProcessor
        )
        
        logger.info("Testing with mock data...")
        
        # Create a real asyncio.Queue to test task_done behavior
        test_queue = asyncio.Queue()
        
        # Add some mock data
        mock_item = {
            "model": Mock(__name__="TestModel"),
            "df": pd.DataFrame({"test_col": [1, 2, 3]}),
            "no_update_cols": (),
            "on_conflict_update": True,
            "conflict_condition": None
        }
        
        # Put items in queue
        await test_queue.put(mock_item)
        await test_queue.put(mock_item)
        
        logger.info(f"Queue size before processing: {test_queue.qsize()}")
        logger.info(f"Unfinished tasks before processing: {test_queue._unfinished_tasks}")
        
        # Create processor
        processor = UpsertQueueProcessor("TEST_PROJECT", logger)
        
        # Mock the _process_consolidated_dataframes_impl to avoid database calls
        async def mock_process_consolidated(project_key, consolidated_dataframes, consolidated_configs, logger, message_count):
            logger.info(f"Mock processing {len(consolidated_dataframes)} model types")
            
        processor._process_consolidated_dataframes_impl = mock_process_consolidated
        
        # Mock coordination manager to terminate after processing items
        mock_coordination_manager = Mock()
        call_count = 0
        
        async def mock_should_terminate(queue_name):
            nonlocal call_count
            call_count += 1
            # Terminate after a few calls to allow processing
            return call_count > 5
            
        mock_coordination_manager.should_terminate_consumer = mock_should_terminate
        mock_coordination_manager.register_queue = AsyncMock()
        
        # Mock early processing check
        async def mock_should_process_early(coordination_manager):
            return False
            
        processor._should_process_early = mock_should_process_early
        
        # Mock q_container
        mock_q_container = Mock()
        
        # Process the queue
        result = await processor.process_queue(test_queue, mock_coordination_manager, mock_q_container)
        
        logger.info(f"Queue size after processing: {test_queue.qsize()}")
        logger.info(f"Unfinished tasks after processing: {test_queue._unfinished_tasks}")
        logger.info(f"Processing result: {result}")
        
        # Check if task_done was called properly
        if test_queue._unfinished_tasks == 0:
            logger.info("✅ All tasks were marked as done - task_done() working correctly")
        else:
            logger.warning(f"⚠️  {test_queue._unfinished_tasks} tasks still unfinished - potential task_done() issue")
        
    except Exception as e:
        logger.error(f"❌ Mock data test failed: {e}")
        raise


async def main():
    """Main test function."""
    logger.info("Starting refactored processor tests...")
    
    try:
        await test_queue_processor_task_done()
        await test_with_mock_data()
        logger.info("✅ All tests completed successfully")
    except Exception as e:
        logger.error(f"❌ Tests failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
