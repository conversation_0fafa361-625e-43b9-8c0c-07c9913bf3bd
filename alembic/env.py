from logging.config import fileConfig
import sys
import os
import logging

from sqlalchemy import engine_from_config, text
from sqlalchemy import pool
from sqlalchemy.exc import ProgrammingError

from alembic import context

# Add the project root to Python path to import models
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import your models and Base
from dags.data_pipeline.dbmodels.base import Base
from dags.data_pipeline.dbmodels import (
    user, issue, initiativeattribute, issueclassification,
    allboards, versions, worklog, changelog, rundetails, teams
)

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
target_metadata = Base.metadata

# Define the schemas we want to create tables in
# Models with {'schema': 'public'} will only be created in public
# Models with {'schema': None} or no schema will be created in these custom schemas

# Read schema configuration from environment variables, alembic.ini, or fallback to hardcoded values
try:
    # Try to load from environment configuration first
    from dags.data_pipeline.utils.env_config import get_env_config
    env_config = get_env_config()
    db_config = env_config.get_database_config()
    CUSTOM_SCHEMAS = db_config.custom_schemas
    PUBLIC_SCHEMA = db_config.public_schema
except ImportError:
    # Fallback to alembic.ini configuration
    try:
        custom_schemas_str = config.get_main_option('custom_schemas', 'plat,plp,acq')
        CUSTOM_SCHEMAS = [s.strip() for s in custom_schemas_str.split(',')]
        PUBLIC_SCHEMA = config.get_main_option('public_schema', 'public')
    except:
        # Final fallback to hardcoded values
        CUSTOM_SCHEMAS = ['plat', 'plp', 'acq']
        PUBLIC_SCHEMA = 'public'

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def get_schema():
    """Get the target schema from command line arguments or environment variable."""
    # First check for -x schema=<name> command line argument
    schema = context.get_x_argument(as_dictionary=True).get('schema', None)

    # Fall back to environment variable
    if not schema:
        schema = os.environ.get('ALEMBIC_SCHEMA', PUBLIC_SCHEMA)

    return schema


def get_schema_translate_map():
    """
    Get the schema translation map for the current migration.
    This allows us to create the same table structure in multiple schemas.
    """
    current_schema = get_schema()

    if current_schema and current_schema in CUSTOM_SCHEMAS:
        # Translate None schema to the current custom schema
        return {None: current_schema}
    else:
        # For public schema or when no schema is specified, don't translate
        return {}


def include_object(object, name, type_, reflected, compare_to):
    """
    Determine whether to include an object in the migration.
    This function controls which tables are included based on the current schema context.
    """
    current_schema = get_schema()

    if type_ == "table":
        # Get the table's schema from its __table_args__
        table_schema = getattr(object, 'schema', None)

        if current_schema == PUBLIC_SCHEMA:
            # In public schema, only include tables explicitly marked for public
            return table_schema == PUBLIC_SCHEMA
        elif current_schema in CUSTOM_SCHEMAS:
            # In custom schemas, only include tables with no schema or schema=None
            return table_schema is None

    return True


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    current_schema = get_schema()

    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        include_object=include_object,
        version_table_schema=current_schema,
        **({'schema_translate_map': get_schema_translate_map()} if get_schema_translate_map() else {})
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        current_schema = get_schema()

        # Create schemas if they don't exist
        if current_schema in CUSTOM_SCHEMAS:
            try:
                with connection.begin():
                    connection.execute(text(f"CREATE SCHEMA IF NOT EXISTS {current_schema}"))
                    logging.info(f"Schema '{current_schema}' created or already exists")
            except ProgrammingError as e:
                logging.warning(f"Could not create schema '{current_schema}': {e}")
                # Continue anyway - schema might already exist

        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            include_object=include_object,
            version_table_schema=current_schema,
            **({'schema_translate_map': get_schema_translate_map()} if get_schema_translate_map() else {})
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
