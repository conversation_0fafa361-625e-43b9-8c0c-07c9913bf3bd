image: python:3.12

definitions:
  caches:
    uv: ~/.cache/uv
  services:
    postgres:
      image: postgres:16
      memory: 2048
      environment:
        POSTGRES_DB: jira
        POSTGRES_USER: postgres
        POSTGRES_PASSWORD: postgres
        DATABASE_URL: postgresql+psycopg2://postgres:postgres@localhost:5432/jira
    redis:
      image: redis:alpine
  steps:
    - step: &install_deps_and_test
        name: Install dependencies and run pytest
        runs-on:
          - self.hosted
          - linux
        services:
          - postgres
          - redis
        caches:
          - uv
        script:
          - ls -l $(pwd)
          - export AIRFLOW_HOME=$(pwd)
          - export LOGGING_CONFIG_PATH=$(pwd)/data_pipeline/logging_config.yaml
          - export ISSUE_FIELDS_YAML_FILE=$(pwd)/data_pipeline/issue_fields.yaml
          - export PYTHONPATH=$PYTHONPATH:$(pwd):$(pwd)/data_pipeline:$(pwd)/data_pipeline/utilities
          - echo ${LOGGING_CONFIG_PATH}
          - echo $AIRFLOW_HOME
          - echo ${ISSUE_FIELDS_YAML_FILE}
          - echo $PYTHONPATH
          # Install uv package manager
          - curl -LsSf https://astral.sh/uv/install.sh | sh
          - export PATH="$HOME/.cargo/bin:$PATH"
          - uv --version
          # Install dependencies using uv and pyproject.toml
          - uv sync --dev
          - apt-get update && apt-get install -y postgresql-client
          # Run pytest with allure reporting
          - uv run pytest --alluredir=allure_results
        artifacts:
          - allure_results

    - step: &generate_report
        name: Generate Allure Report
        runs-on:
          - self.hosted
          - linux
        script:
          - apt-get update && apt-get install -y software-properties-common wget unzip default-jdk
          - wget -qO- https://github.com/allure-framework/allure2/releases/download/2.32.0/allure-2.32.0.tgz | tar -xvz -C /opt/
          - export PATH=$PATH:/opt/allure-2.32.0/bin
          - allure generate allure_results -o allure_report --clean
        artifacts:
          - allure_report
        # Restrict artifact access for security
        condition:
          changesets:
            includePaths:
              - "**"

    - step: &build_docs
        name: Build and Deploy Documentation
        runs-on:
          - self.hosted
          - linux
        script:
          # Install uv if not cached
          - curl -LsSf https://astral.sh/uv/install.sh | sh
          - export PATH="$HOME/.cargo/bin:$PATH"
          # Install dependencies including mkdocs
          - uv sync --dev
          # Build documentation
          - uv run mkdocs build
          # Deploy to GitHub Pages (requires GITHUB_TOKEN environment variable)
          - |
            if [ "$BITBUCKET_BRANCH" = "main" ] || [ "$BITBUCKET_BRANCH" = "master" ]; then
              echo "Deploying documentation to GitHub Pages..."
              git config --global user.email "<EMAIL>"
              git config --global user.name "Bitbucket Pipeline"
              # Clone the GitHub repository
              git clone https://${GITHUB_TOKEN}@github.com/biyani701/airflow.git github_repo
              cd github_repo
              # Switch to gh-pages branch or create it
              git checkout gh-pages || git checkout -b gh-pages
              # Copy built documentation
              cp -r ../site/* .
              # Commit and push
              git add .
              git commit -m "Deploy documentation from Bitbucket Pipeline [skip ci]" || echo "No changes to commit"
              git push origin gh-pages
            else
              echo "Documentation deployment skipped - not on main/master branch"
            fi
        artifacts:
          - site
        only:
          - main
          - master

pipelines:
  default:
    - step: *install_deps_and_test
    - parallel:
        # this option allows a force stop on all running steps if any step fails
        fail-fast: true
        steps:
          - step:
              name: Create plat schema
              script:
                - curl -LsSf https://astral.sh/uv/install.sh | sh
                - export PATH="$HOME/.cargo/bin:$PATH"
                - uv sync --dev
                - uv run python -c "from data_pipeline.utility_code import create_schema_tables_ddl; create_schema_tables_ddl('plat');"
          - step:
              name: Create acq schema
              script:
                - curl -LsSf https://astral.sh/uv/install.sh | sh
                - export PATH="$HOME/.cargo/bin:$PATH"
                - uv sync --dev
                - uv run python -c "from data_pipeline.utility_code import create_schema_tables_ddl; create_schema_tables_ddl('acq');"
          - step:
              name: Create plp schema
              script:
                - curl -LsSf https://astral.sh/uv/install.sh | sh
                - export PATH="$HOME/.cargo/bin:$PATH"
                - uv sync --dev
                - uv run python -c "from data_pipeline.utility_code import create_schema_tables_ddl; create_schema_tables_ddl('plp');"
          - step:
              # option can be disabled for a step
              # and its failure won't stop other steps in a group
              fail-fast: false
              name: Create train schema
              script:
                - curl -LsSf https://astral.sh/uv/install.sh | sh
                - export PATH="$HOME/.cargo/bin:$PATH"
                - uv sync --dev
                - uv run python -c "from data_pipeline.utility_code import create_schema_tables_ddl; create_schema_tables_ddl('train');"
    - step: *generate_report
    - step: *build_docs

  branches:
    main:
      - step: *install_deps_and_test
      - parallel:
          fail-fast: true
          steps:
            - step:
                name: Create plat schema
                script:
                  - curl -LsSf https://astral.sh/uv/install.sh | sh
                  - export PATH="$HOME/.cargo/bin:$PATH"
                  - uv sync --dev
                  - uv run python -c "from data_pipeline.utility_code import create_schema_tables_ddl; create_schema_tables_ddl('plat');"
            - step:
                name: Create acq schema
                script:
                  - curl -LsSf https://astral.sh/uv/install.sh | sh
                  - export PATH="$HOME/.cargo/bin:$PATH"
                  - uv sync --dev
                  - uv run python -c "from data_pipeline.utility_code import create_schema_tables_ddl; create_schema_tables_ddl('acq');"
            - step:
                name: Create plp schema
                script:
                  - curl -LsSf https://astral.sh/uv/install.sh | sh
                  - export PATH="$HOME/.cargo/bin:$PATH"
                  - uv sync --dev
                  - uv run python -c "from data_pipeline.utility_code import create_schema_tables_ddl; create_schema_tables_ddl('plp');"
            - step:
                fail-fast: false
                name: Create train schema
                script:
                  - curl -LsSf https://astral.sh/uv/install.sh | sh
                  - export PATH="$HOME/.cargo/bin:$PATH"
                  - uv sync --dev
                  - uv run python -c "from data_pipeline.utility_code import create_schema_tables_ddl; create_schema_tables_ddl('train');"
      - step: *generate_report
      - step: *build_docs