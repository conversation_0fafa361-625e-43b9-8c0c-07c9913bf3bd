# Fix for Active Consumers Not Being Cleaned Up

## Problem

Even when `consume_issues` is complete and all specialized queues are empty, the active consumers are still registered:

```
Active consumers for queue_changelog: {'queue_changelog_consumer_1'}
Active consumers for queue_worklog: {'queue_worklog_consumer_1'}
Active consumers for queue_comment: {'queue_comment_consumer_1'}
Active consumers for queue_issue_links: {'queue_issue_links_consumer_1'}
Active consumers for queue_issue: {'queue_issue_consumer_1'}
```

This prevents `are_all_specialized_consumers_done()` from returning `true`, causing `process_upsert_queue` to remain in an infinite loop.

## Root Cause

The issue was that the `DynamicConsumerManager` was not aggressively cleaning up consumers that should have terminated. The consumers were still running (and thus still registered) even though they should have stopped when:
1. `consume_issues` completed
2. Their respective queues became empty

## Solution

### 1. Enhanced Consumer Cleanup Logic

Modified `_cleanup_completed_consumers` to force termination of consumers that should terminate:

```python
async def _cleanup_completed_consumers(self, queue_name: str) -> None:
    """Clean up consumers that have completed naturally or should be terminated"""
    async with self.scaling_lock:
        completed_tasks = []
        remaining_tasks = []

        for task in self.consumer_tasks[queue_name]:
            if task.done():
                # Natural completion
                completed_tasks.append(task)
                consumer_id = task.get_name()
                await self.coordination_manager.unregister_consumer(queue_name, consumer_id)
            else:
                # Check if this consumer should be terminated
                if await self.coordination_manager.should_terminate_consumer(queue_name):
                    consumer_id = task.get_name()
                    self.logger.info(f"Force cancelling consumer {consumer_id} for {queue_name}")
                    task.cancel()
                    completed_tasks.append(task)
                    await self.coordination_manager.unregister_consumer(queue_name, consumer_id)
                else:
                    remaining_tasks.append(task)
```

### 2. Force Cleanup When consume_issues Completes

Added automatic cleanup when `consume_issues` signals completion:

```python
async def signal_consume_issues_complete(self, consumer_id: str) -> None:
    """Signal that a consume_issues instance has completed"""
    self.logger.info(f"consume_issues {consumer_id} completed")
    self.consume_issues_complete.set()
    self.all_consume_issues_complete.set()
    
    # Force cleanup of specialized consumers that should now terminate
    await self._force_cleanup_specialized_consumers()
```

### 3. Added Force Cleanup Method

```python
async def _force_cleanup_specialized_consumers(self) -> None:
    """Force cleanup of specialized consumers when consume_issues is complete"""
    specialized_queues = ["queue_changelog", "queue_worklog", "queue_comment", "queue_issue_links", "queue_issue"]
    
    for queue_name in specialized_queues:
        if len(self.active_consumers[queue_name]) > 0:
            queue_empty = self.get_queue_size(queue_name) == 0
            if queue_empty:
                self.logger.info(f"Force cleaning up consumers for {queue_name}")
                # Unregister all consumers for this queue
                consumers_to_remove = list(self.active_consumers[queue_name])
                for consumer_id in consumers_to_remove:
                    await self.unregister_consumer(queue_name, consumer_id)
```

### 4. More Aggressive Termination in Dynamic Manager

Updated the dynamic consumer manager to force termination when conditions are met:

```python
# Force cleanup when consume_issues is complete and queue is empty
queue_empty = self.coordination_manager.get_queue_size(queue_name) == 0
if queue_empty:
    self.logger.info(f"consume_issues complete and {queue_name} empty - force terminating all consumers")
    await self._terminate_all_consumers(queue_name)
    break
```

### 5. Added Debug Logging

Since `self.logger.debug` wasn't writing to the main log file, added print statements for debugging:

```python
print(f"DEBUG: {queue_name} termination check: consume_issues_done={consume_issues_done}, queue_empty={queue_empty}, should_terminate={should_terminate}")
print(f"DEBUG: Specialized state: consume_issues_done={consume_issues_done}, active_consumers={active_counts}, queue_sizes={queue_sizes}")
```

## Files Modified

1. **`dags/data_pipeline/scalable_coordination.py`**:
   - Enhanced `_cleanup_completed_consumers()` with force termination
   - Added `_force_cleanup_specialized_consumers()` method
   - Modified `signal_consume_issues_complete()` to trigger cleanup
   - Updated dynamic consumer manager termination logic
   - Added debug print statements

## Expected Behavior

### Before Fix:
```
consume_issues completes
→ Queues become empty
→ Consumers keep running (maintained by dynamic manager)
→ are_all_specialized_consumers_done() returns False
→ process_upsert_queue loops indefinitely
```

### After Fix:
```
consume_issues completes
→ signal_consume_issues_complete() called
→ _force_cleanup_specialized_consumers() runs
→ Consumers are force-cancelled and unregistered
→ are_all_specialized_consumers_done() returns True
→ process_upsert_queue terminates
```

## Verification

### Debug Output to Look For:

1. **consume_issues completion**:
   ```
   consume_issues consumer_plat_apple completed
   ```

2. **Force cleanup triggered**:
   ```
   Force cleaning up consumers for queue_changelog (consume_issues complete, queue empty)
   Force unregistered consumer queue_changelog_consumer_1 from queue_changelog
   ```

3. **Termination checks**:
   ```
   DEBUG: queue_changelog termination check: consume_issues_done=True, queue_empty=True, should_terminate=True
   DEBUG: Specialized consumer for queue_changelog should terminate
   ```

4. **Final state**:
   ```
   DEBUG: All specialized consumers have completed and queues are empty
   All specialized consumers completed and queue empty - processing remaining data and shutting down
   ```

## Testing

Created `test_consumer_cleanup.py` to verify the fix works correctly. Run it to see the cleanup process in action.

## Impact

- **Fixes Infinite Loop**: `process_upsert_queue` will now terminate correctly
- **Aggressive Cleanup**: Consumers are force-terminated when they should stop
- **Better Debugging**: Print statements help diagnose issues
- **Immediate Response**: Cleanup happens as soon as `consume_issues` completes
- **Resource Management**: No more lingering consumer tasks

The fix ensures that when `consume_issues` completes and queues are empty, all specialized consumers are immediately cleaned up, allowing the system to terminate properly.
