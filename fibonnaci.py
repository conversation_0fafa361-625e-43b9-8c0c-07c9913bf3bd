def fibonacci_generator(n: int, max_limit: int = 10_000):
    """
    Yield Fibonacci numbers up to n terms, with a safety cap.

    Args:
        n (int): Number of terms requested.
        max_limit (int): Maximum allowed terms to avoid overload.

    Yields:
        int: Next number in the Fibonacci sequence.
    """
    if n <= 0:
        return
    if n > max_limit:
        raise ValueError(f"n exceeds safe limit ({max_limit}). Reduce the number.")

    a, b = 0, 1
    for _ in range(n):
        yield a
        a, b = b, a + b


# Example usage
try:
    n = 40  # Change this value to test
    for i, num in enumerate(fibonacci_generator(n)):
        if i > 16:
            print(i - 16, num)
except ValueError as e:
    print("Error:", e)
