# Scalable Queue Architecture for JIRA Data Pipeline

## Overview

This document describes the redesigned scalable queue architecture that replaces the None sentinel-based system with event-driven coordination using `asyncio.Event` and `asyncio.Condition`. The new system provides dynamic scaling based on workload and eliminates hardcoded producer/consumer counts.

## Key Improvements

### 1. **Event-Based Coordination**
- **Before**: Used None sentinel values with hardcoded counts (e.g., `none_count == 10`)
- **After**: Uses `asyncio.Event` for producer completion signaling and consumer termination
- **Benefits**: Eliminates race conditions, provides reliable coordination, scales with any number of producers

### 2. **Fibonacci-Based Producer Scaling**
- **Dynamic Scaling**: Producer count determined by record count using Fibonacci series
- **Scaling Rules**:
  - 1597 records → 1 producer
  - 2584 records → 2 producers
  - 4181 records → 3 producers
  - ...up to 1346269+ records → 15 producers (maximum)
- **Automatic Batch Sizing**: `split_jql_by_count` automatically calculates optimal `max_batch_size` and `num_batches`

### 3. **Dynamic Consumer Scaling**
- **Queue-Size Based**: Spawns additional consumers when queue size > 50 messages
- **Auto-scaling Rules**:
  - Queue size ≤ 50: 1 consumer per queue
  - Queue size > 50: Up to 3 consumers per queue
- **Applies to**: `queue_changelog`, `queue_worklog`, `queue_comment`, `queue_issue_links`

### 4. **Early Processing Triggers**
- **Before**: `process_upsert_queue` waited for 10 None counts or 10,000 records
- **After**: `consume_issue` completion triggers immediate processing of `consolidated_dataframes`
- **Benefits**: Faster processing, reduced memory usage, better resource utilization

## Architecture Components

### ScalableCoordinationManager
```python
class ScalableCoordinationManager:
    # Core coordination events
    producers_completed: asyncio.Event
    processing_complete: asyncio.Event
    shutdown_requested: asyncio.Event
    
    # Producer/Consumer tracking
    active_producers: Set[str]
    active_consumers: Dict[str, Set[str]]
    
    # Processing triggers
    consume_issue_complete: asyncio.Event
    immediate_processing: asyncio.Event
```

**Key Methods**:
- `register_producer()` / `unregister_producer()`: Track producer lifecycle
- `register_consumer()` / `unregister_consumer()`: Track consumer lifecycle
- `should_terminate_consumer()`: Event-based termination logic
- `signal_consume_issue_complete()`: Trigger early processing

### FibonacciProducerScaler
```python
class FibonacciProducerScaler:
    def get_producer_count(record_count: int) -> int:
        # Returns 1-15 producers based on Fibonacci thresholds
    
    def get_batch_parameters(record_count: int) -> Tuple[int, int]:
        # Returns (max_batch_size, num_batches)
```

### DynamicConsumerManager
```python
class DynamicConsumerManager:
    async def manage_queue_scaling(queue_name, consumer_factory, ...):
        # Monitors queue size and scales consumers dynamically
        # Spawns/terminates consumers based on workload
```

## Implementation Changes

### 1. **Producer Updates**
```python
# Before
for i in range(iterations):
    await queue.put(None)  # Hardcoded None signals

# After  
await coordination_manager.register_producer(name)
try:
    # ... producer logic ...
finally:
    await coordination_manager.unregister_producer(name)
```

### 2. **Consumer Updates**
```python
# Before
if none_count >= expected_none_count:
    break

# After
if await coordination_manager.should_terminate_consumer(queue_name):
    break
```

### 3. **Dynamic Scaling Integration**
```python
# Replace static consumer creation
task_group.create_task(consume_changelog(...))

# With dynamic consumer management
task_group.create_task(
    dynamic_consumer_manager.manage_queue_scaling(
        "queue_changelog", consume_changelog, args, kwargs
    )
)
```

### 4. **Early Processing Trigger**
```python
# In consume_issue completion
await coordination_manager.signal_consume_issue_complete()

# In process_upsert_queue
if coordination_manager.immediate_processing.is_set():
    await _process_consolidated_dataframes(...)
    coordination_manager.immediate_processing.clear()
```

## Scaling Behavior

### Producer Scaling Examples
| Record Count | Producers | Batch Size | Rationale |
|-------------|-----------|------------|-----------|
| 1,000 | 1 | 1,000 | Small dataset, single producer sufficient |
| 5,000 | 4 | 1,250 | Medium dataset, parallel processing beneficial |
| 50,000 | 9 | 5,000 | Large dataset, high parallelization |
| 500,000 | 13 | 5,000 | Very large dataset, near-maximum parallelization |
| 2,000,000 | 15 | 5,000 | Maximum producers, optimal batch size |

### Consumer Scaling Examples
| Queue Size | Consumers | Action |
|-----------|-----------|---------|
| 25 | 1 | Maintain single consumer |
| 75 | 2 | Scale up (queue > 50) |
| 150 | 3 | Scale up to maximum |
| 30 | 2 → 1 | Scale down (queue ≤ 50) |

## Benefits

### 1. **Scalability**
- **Automatic Scaling**: No manual configuration needed
- **Load-Adaptive**: Scales based on actual workload
- **Resource Efficient**: Uses optimal number of producers/consumers

### 2. **Reliability**
- **Event-Driven**: Eliminates race conditions from None counting
- **Graceful Termination**: Proper cleanup and resource management
- **Error Recovery**: Coordination-aware error handling

### 3. **Performance**
- **Early Processing**: Reduces processing latency
- **Dynamic Consumers**: Handles queue backlogs efficiently
- **Optimal Batching**: Fibonacci-based batch sizing

### 4. **Maintainability**
- **No Hardcoded Values**: Self-configuring system
- **Clear Separation**: Coordination logic separated from business logic
- **Testable**: Comprehensive test coverage for all components

## Migration Path

### Phase 1: Core Coordination (✅ Complete)
- Implement `ScalableCoordinationManager`
- Replace None sentinel logic with event-based coordination
- Update producer/consumer registration

### Phase 2: Dynamic Scaling (✅ Complete)
- Implement `FibonacciProducerScaler`
- Update `split_jql_by_count` with auto-scaling
- Implement `DynamicConsumerManager`

### Phase 3: Early Processing (✅ Complete)
- Implement early processing triggers
- Update `process_upsert_queue` logic
- Add completion signaling to `consume_issue`

### Phase 4: Testing & Validation (✅ Complete)
- Comprehensive test suite
- Integration testing
- Performance validation

## Configuration

### Default Configuration
```python
ScalingConfig(
    max_producers=15,
    consumer_scale_threshold=50,
    min_consumers_per_queue=1,
    max_consumers_per_queue=3,
    fibonacci_thresholds=[
        (1597, 1), (2584, 2), (4181, 3), (6765, 4), (10946, 5),
        (17711, 6), (28657, 7), (46368, 8), (75025, 9), (121393, 10),
        (196418, 11), (317811, 12), (514229, 13), (832040, 14), (1346269, 15)
    ]
)
```

### Customization
```python
# Custom scaling configuration
custom_config = ScalingConfig(
    max_producers=20,
    consumer_scale_threshold=100,
    max_consumers_per_queue=5
)

coordination_manager = ScalableCoordinationManager(custom_config)
```

## Monitoring

The new architecture provides enhanced monitoring capabilities:

### Metrics Available
- **Real-time Producer Count**: Active producers per project
- **Dynamic Consumer Count**: Consumers per queue with scaling events
- **Queue Depth Monitoring**: Real-time queue sizes
- **Scaling Events**: Producer/consumer scaling decisions
- **Processing Triggers**: Early processing events
- **Coordination Status**: Event states and coordination health

### Integration with Existing Monitoring
The system integrates with the existing `global_async_process_tracker` and provides additional coordination-specific metrics through the `ScalableCoordinationManager`.

## Conclusion

The new scalable queue architecture provides a robust, self-configuring system that automatically adapts to workload demands. By replacing hardcoded None sentinel logic with event-driven coordination and implementing dynamic scaling, the system achieves better performance, reliability, and maintainability while eliminating the scalability limitations of the previous design.
