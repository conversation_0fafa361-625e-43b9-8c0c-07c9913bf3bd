# coding=utf-8
"""
Specialized consumer functions for JIRA data processing.

This module contains the specialized consumer functions that were previously in
queue_processors_new.py. These functions handle the transformation of specific
JIRA data types (changelog, worklog, comments, issue links, issues).

These are different from the queue processors - they are the actual data
transformation functions that process JIRA API responses into database-ready DataFrames.
"""

# Import all the specialized consumer functions from queue_processors_new.py
# This preserves the existing functionality while organizing it properly

from dags.data_pipeline.queue_processors_new import (
    # Base classes
    BaseQueueProcessor,
    filter_null_ids,
    
    # Specialized processor classes
    ChangelogProcessor,
    WorklogProcessor,
    CommentProcessor,
    IssueLinksProcessor,
    IssueProcessor,
    
    # Factory functions
    create_changelog_processor,
    create_worklog_processor,
    create_comment_processor,
    create_issue_links_processor,
    create_issue_processor,
    
    # Consumer functions
    consume_changelog,
    consume_worklog,
    consume_comment,
    consume_issue_links,
    consume_issue
)

# Re-export everything for backward compatibility
__all__ = [
    # Base classes
    "BaseQueueProcessor",
    "filter_null_ids",
    
    # Specialized processor classes
    "ChangelogProcessor",
    "WorklogProcessor", 
    "CommentProcessor",
    "IssueLinksProcessor",
    "IssueProcessor",
    
    # Factory functions
    "create_changelog_processor",
    "create_worklog_processor",
    "create_comment_processor",
    "create_issue_links_processor",
    "create_issue_processor",
    
    # Consumer functions
    "consume_changelog",
    "consume_worklog",
    "consume_comment",
    "consume_issue_links",
    "consume_issue"
]
