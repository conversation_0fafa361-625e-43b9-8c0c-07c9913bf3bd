# coding=utf-8
"""
DataFrame processing pipeline for queue processors.

This module provides a comprehensive pipeline for processing DataFrames
in queue processing operations, including preprocessing, validation,
and transformation steps.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple, Callable
from dataclasses import dataclass
from enum import Enum

import pandas as pd


class ProcessingStage(Enum):
    """Enumeration of DataFrame processing stages."""
    PREPROCESSING = "preprocessing"
    VALIDATION = "validation"
    TRANSFORMATION = "transformation"
    CONSOLIDATION = "consolidation"
    FINALIZATION = "finalization"


@dataclass
class ProcessingResult:
    """Result of a DataFrame processing operation."""
    success: bool
    dataframe: Optional[pd.DataFrame] = None
    error: Optional[Exception] = None
    warnings: List[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.warnings is None:
            self.warnings = []
        if self.metadata is None:
            self.metadata = {}


class DataFrameProcessor(ABC):
    """Abstract base class for DataFrame processors."""
    
    def __init__(self, stage: ProcessingStage, logger: Optional[logging.Logger] = None):
        """Initialize the DataFrame processor."""
        self.stage = stage
        self.logger = logger or logging.getLogger(__name__)
    
    @abstractmethod
    async def process(self, df: pd.DataFrame, context: Dict[str, Any] = None) -> ProcessingResult:
        """
        Process the DataFrame.
        
        Args:
            df: DataFrame to process
            context: Processing context with additional information
            
        Returns:
            ProcessingResult with the processed DataFrame and metadata
        """
        pass
    
    def get_stage(self) -> ProcessingStage:
        """Get the processing stage."""
        return self.stage


class PreprocessingProcessor(DataFrameProcessor):
    """Processor for DataFrame preprocessing operations."""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """Initialize the preprocessing processor."""
        super().__init__(ProcessingStage.PREPROCESSING, logger)
    
    async def process(self, df: pd.DataFrame, context: Dict[str, Any] = None) -> ProcessingResult:
        """Apply preprocessing steps to the DataFrame."""
        try:
            self.logger.debug(f"Preprocessing DataFrame with shape {df.shape}")
            
            # Apply preprocessing steps
            df = self._convert_category_columns(df)
            df = self._handle_empty_containers(df)
            df = self._normalize_data_types(df)
            
            return ProcessingResult(
                success=True,
                dataframe=df,
                metadata={"original_shape": df.shape, "stage": self.stage.value}
            )
            
        except Exception as e:
            self.logger.error(f"Error in preprocessing: {e}")
            return ProcessingResult(success=False, error=e)
    
    def _convert_category_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Convert category columns to object type."""
        category_cols = [col for col in df.columns if df[col].dtype.name == 'category']
        if category_cols:
            for col in category_cols:
                df[col] = df[col].astype(object)
            self.logger.debug(f"Converted {len(category_cols)} category columns to object")
        return df
    
    def _handle_empty_containers(self, df: pd.DataFrame) -> pd.DataFrame:
        """Replace empty dictionaries and lists with None."""
        for col in df.columns:
            # Handle empty dictionaries
            empty_dict_mask = df[col].apply(lambda x: isinstance(x, dict) and len(x) == 0)
            if empty_dict_mask.any():
                df.loc[empty_dict_mask, col] = None
                self.logger.debug(f"Replaced {empty_dict_mask.sum()} empty dictionaries in column '{col}'")
            
            # Handle empty lists
            empty_list_mask = df[col].apply(lambda x: isinstance(x, list) and len(x) == 0)
            if empty_list_mask.any():
                df.loc[empty_list_mask, col] = None
                self.logger.debug(f"Replaced {empty_list_mask.sum()} empty lists in column '{col}'")
                
        return df
    
    def _normalize_data_types(self, df: pd.DataFrame) -> pd.DataFrame:
        """Normalize data types for consistency."""
        # This can be extended with more sophisticated type normalization
        return df


class ValidationProcessor(DataFrameProcessor):
    """Processor for DataFrame validation operations."""
    
    def __init__(self, validation_rules: Dict[str, Callable] = None, logger: Optional[logging.Logger] = None):
        """
        Initialize the validation processor.
        
        Args:
            validation_rules: Dictionary of column names to validation functions
            logger: Logger instance
        """
        super().__init__(ProcessingStage.VALIDATION, logger)
        self.validation_rules = validation_rules or {}
    
    async def process(self, df: pd.DataFrame, context: Dict[str, Any] = None) -> ProcessingResult:
        """Validate the DataFrame."""
        try:
            warnings = []
            
            # Basic validation
            if df.empty:
                warnings.append("DataFrame is empty")
            
            # Apply custom validation rules
            for column, rule in self.validation_rules.items():
                if column in df.columns:
                    try:
                        if not rule(df[column]):
                            warnings.append(f"Validation failed for column '{column}'")
                    except Exception as e:
                        warnings.append(f"Validation error for column '{column}': {e}")
            
            return ProcessingResult(
                success=True,
                dataframe=df,
                warnings=warnings,
                metadata={"validation_rules_applied": len(self.validation_rules)}
            )
            
        except Exception as e:
            self.logger.error(f"Error in validation: {e}")
            return ProcessingResult(success=False, error=e)


class ConsolidationProcessor(DataFrameProcessor):
    """Processor for consolidating multiple DataFrames."""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """Initialize the consolidation processor."""
        super().__init__(ProcessingStage.CONSOLIDATION, logger)
    
    async def process_multiple(
        self,
        dataframes: List[pd.DataFrame],
        context: Dict[str, Any] = None
    ) -> ProcessingResult:
        """
        Consolidate multiple DataFrames into one.
        
        Args:
            dataframes: List of DataFrames to consolidate
            context: Processing context
            
        Returns:
            ProcessingResult with consolidated DataFrame
        """
        try:
            if not dataframes:
                return ProcessingResult(
                    success=True,
                    dataframe=pd.DataFrame(),
                    metadata={"consolidated_count": 0}
                )
            
            if len(dataframes) == 1:
                return ProcessingResult(
                    success=True,
                    dataframe=dataframes[0],
                    metadata={"consolidated_count": 1}
                )
            
            # Check for column type consistency
            self._validate_column_consistency(dataframes)
            
            # Concatenate DataFrames
            consolidated_df = pd.concat(dataframes, ignore_index=True, sort=False)
            
            self.logger.debug(f"Consolidated {len(dataframes)} DataFrames into shape {consolidated_df.shape}")
            
            return ProcessingResult(
                success=True,
                dataframe=consolidated_df,
                metadata={
                    "consolidated_count": len(dataframes),
                    "final_shape": consolidated_df.shape
                }
            )
            
        except Exception as e:
            self.logger.error(f"Error in consolidation: {e}")
            return ProcessingResult(success=False, error=e)
    
    async def process(self, df: pd.DataFrame, context: Dict[str, Any] = None) -> ProcessingResult:
        """Process single DataFrame (pass-through for consolidation processor)."""
        return ProcessingResult(success=True, dataframe=df)
    
    def _validate_column_consistency(self, dataframes: List[pd.DataFrame]) -> None:
        """Validate that DataFrames have consistent column types."""
        if len(dataframes) <= 1:
            return
            
        # Check for mixed column types
        column_types = {}
        for i, df in enumerate(dataframes):
            for col in df.columns:
                if col not in column_types:
                    column_types[col] = set()
                column_types[col].update(df[col].map(type).unique())
        
        # Log warnings for mixed types
        for col, types in column_types.items():
            if len(types) > 1:
                self.logger.warning(f"Column '{col}' has mixed types: {types}")


class DataFrameProcessingPipeline:
    """Pipeline for processing DataFrames through multiple stages."""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """Initialize the processing pipeline."""
        self.logger = logger or logging.getLogger(__name__)
        self.processors: Dict[ProcessingStage, List[DataFrameProcessor]] = {}
        
    def add_processor(self, processor: DataFrameProcessor) -> None:
        """Add a processor to the pipeline."""
        stage = processor.get_stage()
        if stage not in self.processors:
            self.processors[stage] = []
        self.processors[stage].append(processor)
        
    async def process(
        self,
        df: pd.DataFrame,
        context: Dict[str, Any] = None,
        stages: List[ProcessingStage] = None
    ) -> ProcessingResult:
        """
        Process DataFrame through the pipeline.
        
        Args:
            df: DataFrame to process
            context: Processing context
            stages: Specific stages to run (if None, runs all stages)
            
        Returns:
            Final ProcessingResult
        """
        if context is None:
            context = {}
            
        if stages is None:
            stages = [ProcessingStage.PREPROCESSING, ProcessingStage.VALIDATION, ProcessingStage.TRANSFORMATION]
        
        current_df = df
        all_warnings = []
        all_metadata = {}
        
        for stage in stages:
            if stage not in self.processors:
                continue
                
            for processor in self.processors[stage]:
                result = await processor.process(current_df, context)
                
                if not result.success:
                    self.logger.error(f"Processing failed at stage {stage.value}: {result.error}")
                    return result
                
                current_df = result.dataframe
                all_warnings.extend(result.warnings)
                all_metadata.update(result.metadata)
        
        return ProcessingResult(
            success=True,
            dataframe=current_df,
            warnings=all_warnings,
            metadata=all_metadata
        )


# Factory functions for creating common pipelines
def create_standard_pipeline(logger: Optional[logging.Logger] = None) -> DataFrameProcessingPipeline:
    """Create a standard DataFrame processing pipeline."""
    pipeline = DataFrameProcessingPipeline(logger)
    pipeline.add_processor(PreprocessingProcessor(logger))
    pipeline.add_processor(ValidationProcessor(logger=logger))
    return pipeline


def create_consolidation_pipeline(logger: Optional[logging.Logger] = None) -> DataFrameProcessingPipeline:
    """Create a pipeline focused on DataFrame consolidation."""
    pipeline = DataFrameProcessingPipeline(logger)
    pipeline.add_processor(PreprocessingProcessor(logger))
    pipeline.add_processor(ConsolidationProcessor(logger))
    return pipeline
