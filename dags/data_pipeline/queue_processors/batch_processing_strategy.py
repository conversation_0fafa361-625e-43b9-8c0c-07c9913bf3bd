# coding=utf-8
"""
Batch processing strategies for queue processors.

This module provides configurable batch processing strategies that can be
used by different queue processors to determine when to process accumulated data.
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

import pandas as pd


class BatchTrigger(Enum):
    """Enumeration of batch processing triggers."""
    MESSAGE_COUNT = "message_count"
    RECORD_COUNT = "record_count"
    TIME_BASED = "time_based"
    EVENT_BASED = "event_based"
    MEMORY_THRESHOLD = "memory_threshold"


@dataclass
class BatchProcessingMetrics:
    """Metrics for batch processing decisions."""
    message_count: int = 0
    total_records: int = 0
    memory_usage_mb: float = 0.0
    time_since_last_batch: float = 0.0
    queue_size: int = 0


class BatchProcessingStrategy(ABC):
    """Abstract base class for batch processing strategies."""
    
    def __init__(self, logger: logging.Logger):
        """Initialize the batch processing strategy."""
        self.logger = logger
        
    @abstractmethod
    async def should_process_batch(
        self,
        metrics: BatchProcessingMetrics,
        consolidated_dataframes: Dict[str, List[pd.DataFrame]],
        coordination_manager: Any = None
    ) -> bool:
        """
        Determine if a batch should be processed.
        
        Args:
            metrics: Current processing metrics
            consolidated_dataframes: Current consolidated DataFrames
            coordination_manager: Optional coordination manager for event-based triggers
            
        Returns:
            True if batch should be processed, False otherwise
        """
        pass
    
    @abstractmethod
    def get_strategy_name(self) -> str:
        """Get the name of this strategy."""
        pass


class MessageCountStrategy(BatchProcessingStrategy):
    """Strategy that triggers batch processing based on message count."""
    
    def __init__(self, batch_size: int = 100, logger: Optional[logging.Logger] = None):
        """
        Initialize message count strategy.
        
        Args:
            batch_size: Number of messages to accumulate before processing
            logger: Logger instance
        """
        super().__init__(logger or logging.getLogger(__name__))
        self.batch_size = batch_size
        
    async def should_process_batch(
        self,
        metrics: BatchProcessingMetrics,
        consolidated_dataframes: Dict[str, List[pd.DataFrame]],
        coordination_manager: Any = None
    ) -> bool:
        """Check if message count threshold is reached."""
        should_process = metrics.message_count >= self.batch_size
        
        if should_process:
            self.logger.debug(f"Message count trigger: {metrics.message_count} >= {self.batch_size}")
            
        return should_process
    
    def get_strategy_name(self) -> str:
        return f"MessageCount(batch_size={self.batch_size})"


class RecordCountStrategy(BatchProcessingStrategy):
    """Strategy that triggers batch processing based on total record count."""
    
    def __init__(self, record_threshold: int = 10000, logger: Optional[logging.Logger] = None):
        """
        Initialize record count strategy.
        
        Args:
            record_threshold: Number of records to accumulate before processing
            logger: Logger instance
        """
        super().__init__(logger or logging.getLogger(__name__))
        self.record_threshold = record_threshold
        
    async def should_process_batch(
        self,
        metrics: BatchProcessingMetrics,
        consolidated_dataframes: Dict[str, List[pd.DataFrame]],
        coordination_manager: Any = None
    ) -> bool:
        """Check if record count threshold is reached."""
        should_process = metrics.total_records >= self.record_threshold
        
        if should_process:
            self.logger.debug(f"Record count trigger: {metrics.total_records} >= {self.record_threshold}")
            
        return should_process
    
    def get_strategy_name(self) -> str:
        return f"RecordCount(threshold={self.record_threshold})"


class EventBasedStrategy(BatchProcessingStrategy):
    """Strategy that triggers batch processing based on coordination events."""
    
    def __init__(self, event_name: str, logger: Optional[logging.Logger] = None):
        """
        Initialize event-based strategy.
        
        Args:
            event_name: Name of the event to monitor
            logger: Logger instance
        """
        super().__init__(logger or logging.getLogger(__name__))
        self.event_name = event_name
        
    async def should_process_batch(
        self,
        metrics: BatchProcessingMetrics,
        consolidated_dataframes: Dict[str, List[pd.DataFrame]],
        coordination_manager: Any = None
    ) -> bool:
        """Check if coordination event is set."""
        if coordination_manager is None:
            return False
            
        # Check for immediate processing event
        if hasattr(coordination_manager, 'immediate_processing'):
            should_process = coordination_manager.immediate_processing.is_set()
            if should_process:
                self.logger.debug(f"Event-based trigger: {self.event_name} event is set")
                return True
                
        return False
    
    def get_strategy_name(self) -> str:
        return f"EventBased(event={self.event_name})"


class CompositeStrategy(BatchProcessingStrategy):
    """Strategy that combines multiple strategies with OR logic."""
    
    def __init__(self, strategies: List[BatchProcessingStrategy], logger: Optional[logging.Logger] = None):
        """
        Initialize composite strategy.
        
        Args:
            strategies: List of strategies to combine
            logger: Logger instance
        """
        super().__init__(logger or logging.getLogger(__name__))
        self.strategies = strategies
        
    async def should_process_batch(
        self,
        metrics: BatchProcessingMetrics,
        consolidated_dataframes: Dict[str, List[pd.DataFrame]],
        coordination_manager: Any = None
    ) -> bool:
        """Check if any of the constituent strategies trigger processing."""
        for strategy in self.strategies:
            if await strategy.should_process_batch(metrics, consolidated_dataframes, coordination_manager):
                self.logger.debug(f"Composite trigger activated by: {strategy.get_strategy_name()}")
                return True
                
        return False
    
    def get_strategy_name(self) -> str:
        strategy_names = [s.get_strategy_name() for s in self.strategies]
        return f"Composite({' OR '.join(strategy_names)})"


class AdaptiveStrategy(BatchProcessingStrategy):
    """Strategy that adapts batch size based on processing performance."""
    
    def __init__(
        self,
        initial_batch_size: int = 100,
        min_batch_size: int = 10,
        max_batch_size: int = 1000,
        adaptation_factor: float = 1.2,
        logger: Optional[logging.Logger] = None
    ):
        """
        Initialize adaptive strategy.
        
        Args:
            initial_batch_size: Starting batch size
            min_batch_size: Minimum allowed batch size
            max_batch_size: Maximum allowed batch size
            adaptation_factor: Factor for adjusting batch size
            logger: Logger instance
        """
        super().__init__(logger or logging.getLogger(__name__))
        self.current_batch_size = initial_batch_size
        self.min_batch_size = min_batch_size
        self.max_batch_size = max_batch_size
        self.adaptation_factor = adaptation_factor
        self.last_processing_time = 0.0
        
    async def should_process_batch(
        self,
        metrics: BatchProcessingMetrics,
        consolidated_dataframes: Dict[str, List[pd.DataFrame]],
        coordination_manager: Any = None
    ) -> bool:
        """Check if adaptive batch size threshold is reached."""
        should_process = metrics.message_count >= self.current_batch_size
        
        if should_process:
            self.logger.debug(f"Adaptive trigger: {metrics.message_count} >= {self.current_batch_size}")
            
        return should_process
    
    def adapt_batch_size(self, processing_time: float, success: bool) -> None:
        """
        Adapt batch size based on processing performance.
        
        Args:
            processing_time: Time taken to process the last batch
            success: Whether the processing was successful
        """
        if not success:
            # Reduce batch size on failure
            self.current_batch_size = max(
                self.min_batch_size,
                int(self.current_batch_size / self.adaptation_factor)
            )
            self.logger.info(f"Reduced batch size to {self.current_batch_size} due to processing failure")
        elif processing_time < self.last_processing_time * 0.8:
            # Increase batch size if processing is getting faster
            self.current_batch_size = min(
                self.max_batch_size,
                int(self.current_batch_size * self.adaptation_factor)
            )
            self.logger.info(f"Increased batch size to {self.current_batch_size} due to improved performance")
            
        self.last_processing_time = processing_time
    
    def get_strategy_name(self) -> str:
        return f"Adaptive(current_size={self.current_batch_size})"


# Factory function for creating common strategies
def create_upsert_queue_strategy(logger: Optional[logging.Logger] = None) -> BatchProcessingStrategy:
    """Create the strategy used by process_upsert_queue."""
    return CompositeStrategy([
        MessageCountStrategy(batch_size=100, logger=logger),
        EventBasedStrategy("immediate_processing", logger=logger)
    ], logger=logger)


def create_upsert_others_strategy(logger: Optional[logging.Logger] = None) -> BatchProcessingStrategy:
    """Create the strategy used by process_upsert_others."""
    return EventBasedStrategy("immediate_processing", logger=logger)
