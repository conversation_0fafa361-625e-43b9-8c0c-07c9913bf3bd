# coding=utf-8
"""
Simplified queue processors using Protocol-based approach.

This module provides concrete implementations of queue processors that replace
the complex ABC-based system with a simpler, more maintainable Protocol-based approach.
"""
import asyncio
import time
from collections import defaultdict
from typing import Dict, List, Any, Optional
from logging import Logger
import pandas as pd
from asyncio import Queue

from dags.data_pipeline.queue_processors.protocols import (
    QueueProcessorProtocol,
    ProcessingResult,
    ReferentialIntegrityProtocol,
    CoordinationProtocol,
    referential_integrity_manager,
    simple_coordinator
)
from dags.data_pipeline.queue_processors.dataframe_processor import dataframe_processor
from dags.data_pipeline.priority_queue_system import priority_queue_manager, MessageType


class IssueQueueProcessor:
    """
    Simplified processor for the main issue queue (queue_upsert_issue).
    
    This processor handles Issue table processing with referential integrity
    coordination, ensuring Issues are committed before child tables are processed.
    """
    
    def __init__(
        self,
        referential_integrity: Optional[ReferentialIntegrityProtocol] = None,
        coordinator: Optional[CoordinationProtocol] = None,
        batch_size: int = 100,
        timeout_seconds: float = 5.0
    ):
        """
        Initialize the issue queue processor.
        
        Args:
            referential_integrity: Referential integrity coordinator
            coordinator: Event coordinator
            batch_size: Number of messages to process in a batch
            timeout_seconds: Timeout for queue operations
        """
        self.referential_integrity = referential_integrity or referential_integrity_manager
        self.coordinator = coordinator or simple_coordinator
        self.batch_size = batch_size
        self.timeout_seconds = timeout_seconds
        
        # Processing state
        self.consolidated_dataframes: Dict[str, List[pd.DataFrame]] = defaultdict(list)
        self.consolidated_configs: Dict[str, Dict[str, Any]] = {}
        self.message_count = 0
    
    async def process_queue(
        self,
        project_key: str,
        queue: Queue,
        coordination_manager: Any,
        logger: Logger
    ) -> Dict[str, Any]:
        """
        Process items from the issue queue.
        
        This method processes messages from queue_upsert_issue, consolidating
        DataFrames and processing them in batches while maintaining referential integrity.
        """
        start_time = time.time()
        messages_processed = 0
        batches_processed = 0
        errors_encountered = 0
        
        logger.info(f"Starting IssueQueueProcessor for project {project_key}")
        
        try:
            while True:
                # Check for early processing trigger
                should_process_early = await self.coordinator.should_process_immediately()
                
                # Check termination condition
                should_terminate = await coordination_manager.should_terminate_consumer("queue_upsert_issue")
                
                if should_terminate and queue.empty():
                    logger.info("Termination condition met - processing remaining data")
                    break
                
                # Process batch if conditions are met
                if (should_process_early or 
                    self._get_total_messages() >= self.batch_size or
                    (should_terminate and self.consolidated_dataframes)):
                    
                    await self._process_batch(project_key, logger)
                    batches_processed += 1
                    continue
                
                # Get message from queue
                try:
                    message = await asyncio.wait_for(queue.get(), timeout=self.timeout_seconds)
                    
                    if message is None:
                        continue
                    
                    # Process the message
                    await self._process_message(message, logger)
                    messages_processed += 1
                    self.message_count += 1
                    
                    queue.task_done()
                    
                except asyncio.TimeoutError:
                    # Timeout - check if we should process accumulated data
                    if self.consolidated_dataframes:
                        await self._process_batch(project_key, logger)
                        batches_processed += 1
                    continue
                except Exception as e:
                    logger.error(f"Error processing message: {e}")
                    errors_encountered += 1
                    continue
            
            # Process any remaining data
            if self.consolidated_dataframes:
                await self._process_batch(project_key, logger)
                batches_processed += 1
            
            # Signal completion
            await self.coordinator.signal_processing_complete("process_upsert_queue")
            
            processing_time = time.time() - start_time
            logger.info(f"IssueQueueProcessor completed: {messages_processed} messages, {batches_processed} batches in {processing_time:.2f}s")
            
            return ProcessingResult(
                messages_processed=messages_processed,
                batches_processed=batches_processed,
                errors_encountered=errors_encountered,
                processing_time=processing_time,
                success=errors_encountered == 0
            ).to_dict()
            
        except Exception as e:
            logger.error(f"Fatal error in IssueQueueProcessor: {e}")
            processing_time = time.time() - start_time
            return ProcessingResult(
                messages_processed=messages_processed,
                batches_processed=batches_processed,
                errors_encountered=errors_encountered + 1,
                processing_time=processing_time,
                success=False
            ).to_dict()
    
    async def _process_message(self, message: Any, logger: Logger) -> None:
        """Process a single message from the queue."""
        try:
            # Extract DataFrame and configuration
            df = message.get("df")
            if df is None or df.empty:
                return
            
            model_name = message.get("model", {}).get("__name__", "Unknown")
            
            # Store DataFrame and configuration
            self.consolidated_dataframes[model_name].append(df)
            self.consolidated_configs[model_name] = message
            
            logger.debug(f"Queued {model_name} with {len(df)} records")
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            raise
    
    async def _process_batch(self, project_key: str, logger: Logger) -> None:
        """Process accumulated DataFrames as a batch."""
        if not self.consolidated_dataframes:
            return
        
        try:
            logger.info(f"Processing batch with {len(self.consolidated_dataframes)} model types")
            
            # Process consolidated DataFrames with referential integrity
            await dataframe_processor.process_consolidated_dataframes(
                project_key=project_key,
                consolidated_dataframes=dict(self.consolidated_dataframes),
                consolidated_configs=self.consolidated_configs,
                logger=logger,
                message_count=self.message_count
            )
            
            # Clear processed data
            self.consolidated_dataframes.clear()
            self.consolidated_configs.clear()
            
            logger.debug("Batch processing completed successfully")
            
        except Exception as e:
            logger.error(f"Error processing batch: {e}")
            # Clear data even on error to prevent infinite retry
            self.consolidated_dataframes.clear()
            self.consolidated_configs.clear()
            raise
    
    def _get_total_messages(self) -> int:
        """Get total number of messages in consolidated DataFrames."""
        return sum(len(dfs) for dfs in self.consolidated_dataframes.values())


class OthersQueueProcessor:
    """
    Simplified processor for specialized consumer queues (queue_upsert_others).
    
    This processor handles child table processing, waiting for Issue table
    commitment to maintain referential integrity.
    """
    
    def __init__(
        self,
        referential_integrity: Optional[ReferentialIntegrityProtocol] = None,
        coordinator: Optional[CoordinationProtocol] = None,
        batch_size: int = 50,
        timeout_seconds: float = 2.0
    ):
        """
        Initialize the others queue processor.
        
        Args:
            referential_integrity: Referential integrity coordinator
            coordinator: Event coordinator
            batch_size: Number of messages to process in a batch
            timeout_seconds: Timeout for queue operations
        """
        self.referential_integrity = referential_integrity or referential_integrity_manager
        self.coordinator = coordinator or simple_coordinator
        self.batch_size = batch_size
        self.timeout_seconds = timeout_seconds
        
        # Processing state
        self.consolidated_dataframes: Dict[str, List[pd.DataFrame]] = defaultdict(list)
        self.consolidated_configs: Dict[str, Dict[str, Any]] = {}
        self.message_count = 0
    
    async def process_queue(
        self,
        project_key: str,
        queue: Queue,
        coordination_manager: Any,
        logger: Logger
    ) -> Dict[str, Any]:
        """
        Process items from the others queue.
        
        This method processes messages from queue_upsert_others, ensuring
        referential integrity by waiting for Issue table commitment.
        """
        start_time = time.time()
        messages_processed = 0
        batches_processed = 0
        errors_encountered = 0
        
        logger.info(f"Starting OthersQueueProcessor for project {project_key}")
        
        try:
            while True:
                # Check termination condition
                should_terminate = await coordination_manager.should_terminate_consumer("queue_upsert_others")
                
                if should_terminate and queue.empty():
                    logger.info("Termination condition met - processing remaining data")
                    break
                
                # Process batch if conditions are met
                if (self._get_total_messages() >= self.batch_size or
                    (should_terminate and self.consolidated_dataframes)):
                    
                    await self._process_batch(project_key, logger)
                    batches_processed += 1
                    continue
                
                # Get message from queue
                try:
                    message = await asyncio.wait_for(queue.get(), timeout=self.timeout_seconds)
                    
                    if message is None:
                        continue
                    
                    # Process the message
                    await self._process_message(message, logger)
                    messages_processed += 1
                    self.message_count += 1
                    
                    queue.task_done()
                    
                except asyncio.TimeoutError:
                    # Timeout - check if we should process accumulated data
                    if self.consolidated_dataframes:
                        await self._process_batch(project_key, logger)
                        batches_processed += 1
                    continue
                except Exception as e:
                    logger.error(f"Error processing message: {e}")
                    errors_encountered += 1
                    continue
            
            # Process any remaining data
            if self.consolidated_dataframes:
                await self._process_batch(project_key, logger)
                batches_processed += 1
            
            # Signal completion
            await self.coordinator.signal_processing_complete("process_upsert_others")
            
            processing_time = time.time() - start_time
            logger.info(f"OthersQueueProcessor completed: {messages_processed} messages, {batches_processed} batches in {processing_time:.2f}s")
            
            return ProcessingResult(
                messages_processed=messages_processed,
                batches_processed=batches_processed,
                errors_encountered=errors_encountered,
                processing_time=processing_time,
                success=errors_encountered == 0
            ).to_dict()
            
        except Exception as e:
            logger.error(f"Fatal error in OthersQueueProcessor: {e}")
            processing_time = time.time() - start_time
            return ProcessingResult(
                messages_processed=messages_processed,
                batches_processed=batches_processed,
                errors_encountered=errors_encountered + 1,
                processing_time=processing_time,
                success=False
            ).to_dict()
    
    async def _process_message(self, message: Any, logger: Logger) -> None:
        """Process a single message from the queue."""
        try:
            # Extract DataFrame and configuration
            df = message.get("df")
            if df is None or df.empty:
                return
            
            model_name = message.get("model", {}).get("__name__", "Unknown")
            
            # Store DataFrame and configuration
            self.consolidated_dataframes[model_name].append(df)
            self.consolidated_configs[model_name] = message
            
            logger.debug(f"Queued {model_name} with {len(df)} records")
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            raise
    
    async def _process_batch(self, project_key: str, logger: Logger) -> None:
        """Process accumulated DataFrames as a batch."""
        if not self.consolidated_dataframes:
            return
        
        try:
            logger.info(f"Processing batch with {len(self.consolidated_dataframes)} model types")
            
            # Wait for parent tables to be committed before processing
            logger.info("Waiting for parent tables commitment before processing")
            parent_committed = await self.referential_integrity.wait_for_parent_commitment(timeout=30.0)
            
            if not parent_committed:
                logger.warning("Timeout waiting for parent commitment - processing anyway")
            
            # Process consolidated DataFrames
            await dataframe_processor.process_consolidated_dataframes(
                project_key=project_key,
                consolidated_dataframes=dict(self.consolidated_dataframes),
                consolidated_configs=self.consolidated_configs,
                logger=logger,
                message_count=self.message_count
            )
            
            # Clear processed data
            self.consolidated_dataframes.clear()
            self.consolidated_configs.clear()
            
            logger.debug("Batch processing completed successfully")
            
        except Exception as e:
            logger.error(f"Error processing batch: {e}")
            # Clear data even on error to prevent infinite retry
            self.consolidated_dataframes.clear()
            self.consolidated_configs.clear()
            raise
    
    def _get_total_messages(self) -> int:
        """Get total number of messages in consolidated DataFrames."""
        return sum(len(dfs) for dfs in self.consolidated_dataframes.values())


# Factory functions for creating processors
def create_issue_queue_processor(
    batch_size: int = 100,
    timeout_seconds: float = 5.0
) -> IssueQueueProcessor:
    """Create a configured IssueQueueProcessor."""
    return IssueQueueProcessor(
        batch_size=batch_size,
        timeout_seconds=timeout_seconds
    )


def create_others_queue_processor(
    batch_size: int = 50,
    timeout_seconds: float = 2.0
) -> OthersQueueProcessor:
    """Create a configured OthersQueueProcessor."""
    return OthersQueueProcessor(
        batch_size=batch_size,
        timeout_seconds=timeout_seconds
    )


# Simplified entry point functions
async def process_upsert_queue_simple(
    project_key: str,
    q_container: Any,
    logger: Logger
) -> Dict[str, Any]:
    """
    Simplified replacement for process_upsert_queue_refactored.

    This function processes the main issue queue using the simplified
    Protocol-based approach while maintaining referential integrity.
    """
    from dags.data_pipeline.scalable_coordination import coordination_manager

    # Create processor
    processor = create_issue_queue_processor()

    # Get queue
    queue_upsert_issue = q_container.queue_selector()["queue_upsert_issue"]

    # Process queue
    result = await processor.process_queue(
        project_key=project_key,
        queue=queue_upsert_issue,
        coordination_manager=coordination_manager,
        logger=logger
    )

    logger.info(f"process_upsert_queue_simple completed: {result}")
    return result


async def process_upsert_others_simple(
    project_key: str,
    q_container: Any,
    logger: Logger
) -> Dict[str, Any]:
    """
    Simplified replacement for process_upsert_others_refactored.

    This function processes the specialized consumer queues using the simplified
    Protocol-based approach while maintaining referential integrity.
    """
    from dags.data_pipeline.scalable_coordination import coordination_manager

    # Create processor
    processor = create_others_queue_processor()

    # Get queue
    queue_upsert_others = q_container.queue_selector()["queue_upsert_others"]

    # Process queue
    result = await processor.process_queue(
        project_key=project_key,
        queue=queue_upsert_others,
        coordination_manager=coordination_manager,
        logger=logger
    )

    logger.info(f"process_upsert_others_simple completed: {result}")
    return result
