# coding=utf-8
"""
Enhanced event coordination for queue processors.

This module provides improved event coordination mechanisms to ensure
proper sequencing between process_upsert_queue and process_upsert_others
while maintaining referential integrity.
"""

import asyncio
import logging
from typing import Dict, Set, Optional, Any
from dataclasses import dataclass
from enum import Enum


class ProcessingPhase(Enum):
    """Enumeration of processing phases for coordination."""
    INITIALIZATION = "initialization"
    ISSUE_PROCESSING = "issue_processing"
    SPECIALIZED_PROCESSING = "specialized_processing"
    FINALIZATION = "finalization"
    COMPLETED = "completed"


@dataclass
class CoordinationState:
    """State information for coordination management."""
    current_phase: ProcessingPhase = ProcessingPhase.INITIALIZATION
    active_processors: Set[str] = None
    completed_processors: Set[str] = None
    processing_metrics: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.active_processors is None:
            self.active_processors = set()
        if self.completed_processors is None:
            self.completed_processors = set()
        if self.processing_metrics is None:
            self.processing_metrics = {}


class EnhancedEventCoordinator:
    """
    Enhanced event coordinator for queue processors.
    
    This coordinator ensures proper sequencing between different queue processors
    while maintaining referential integrity and providing clear coordination signals.
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """Initialize the enhanced event coordinator."""
        self.logger = logger or logging.getLogger(__name__)
        
        # Phase management
        self.state = CoordinationState()
        self._phase_lock = asyncio.Lock()
        
        # Core coordination events
        self.issue_processing_complete = asyncio.Event()
        self.specialized_processing_ready = asyncio.Event()
        self.all_processing_complete = asyncio.Event()
        
        # Processor-specific events
        self.processor_events: Dict[str, asyncio.Event] = {}
        
        # Batch processing triggers
        self.immediate_processing_trigger = asyncio.Event()
        self.batch_processing_ready = asyncio.Event()
        
        # Referential integrity events
        self.parent_tables_committed = asyncio.Event()
        self.child_tables_ready = asyncio.Event()
        
        self.logger.info("Enhanced Event Coordinator initialized")
    
    async def register_processor(self, processor_name: str) -> None:
        """
        Register a processor with the coordinator.
        
        Args:
            processor_name: Name of the processor to register
        """
        async with self._phase_lock:
            self.state.active_processors.add(processor_name)
            self.processor_events[processor_name] = asyncio.Event()
            
        self.logger.info(f"Registered processor: {processor_name}")
    
    async def signal_processor_complete(self, processor_name: str) -> None:
        """
        Signal that a processor has completed.
        
        Args:
            processor_name: Name of the completed processor
        """
        async with self._phase_lock:
            if processor_name in self.state.active_processors:
                self.state.active_processors.remove(processor_name)
                self.state.completed_processors.add(processor_name)
                
                # Set processor-specific event
                if processor_name in self.processor_events:
                    self.processor_events[processor_name].set()
                
                # Check for phase transitions
                await self._check_phase_transitions()
                
        self.logger.info(f"Processor completed: {processor_name}")
    
    async def signal_issue_processing_complete(self) -> None:
        """Signal that issue processing is complete and ready for specialized processing."""
        async with self._phase_lock:
            if self.state.current_phase == ProcessingPhase.ISSUE_PROCESSING:
                self.issue_processing_complete.set()
                self.immediate_processing_trigger.set()
                self.parent_tables_committed.set()  # Issues are parent tables
                
                self.logger.info("Issue processing complete - triggering immediate processing")
    
    async def wait_for_issue_processing_complete(self, timeout: Optional[float] = None) -> bool:
        """
        Wait for issue processing to complete.
        
        Args:
            timeout: Optional timeout in seconds
            
        Returns:
            True if issue processing completed, False if timeout
        """
        try:
            await asyncio.wait_for(self.issue_processing_complete.wait(), timeout=timeout)
            return True
        except asyncio.TimeoutError:
            self.logger.warning("Timeout waiting for issue processing completion")
            return False
    
    async def wait_for_parent_tables_committed(self, timeout: Optional[float] = None) -> bool:
        """
        Wait for parent tables to be committed (for referential integrity).
        
        Args:
            timeout: Optional timeout in seconds
            
        Returns:
            True if parent tables committed, False if timeout
        """
        try:
            await asyncio.wait_for(self.parent_tables_committed.wait(), timeout=timeout)
            return True
        except asyncio.TimeoutError:
            self.logger.warning("Timeout waiting for parent tables commitment")
            return False
    
    async def should_process_immediately(self) -> bool:
        """Check if immediate processing should be triggered."""
        return self.immediate_processing_trigger.is_set()
    
    async def clear_immediate_processing_trigger(self) -> None:
        """Clear the immediate processing trigger."""
        self.immediate_processing_trigger.clear()
        self.logger.debug("Cleared immediate processing trigger")
    
    async def signal_batch_ready(self) -> None:
        """Signal that a batch is ready for processing."""
        self.batch_processing_ready.set()
        self.logger.debug("Batch processing ready signal set")
    
    async def wait_for_batch_ready(self, timeout: float = 1.0) -> bool:
        """
        Wait for batch ready signal.
        
        Args:
            timeout: Timeout in seconds
            
        Returns:
            True if batch ready, False if timeout
        """
        try:
            await asyncio.wait_for(self.batch_processing_ready.wait(), timeout=timeout)
            self.batch_processing_ready.clear()  # Auto-clear after use
            return True
        except asyncio.TimeoutError:
            return False
    
    async def get_coordination_state(self) -> CoordinationState:
        """Get current coordination state."""
        async with self._phase_lock:
            return CoordinationState(
                current_phase=self.state.current_phase,
                active_processors=self.state.active_processors.copy(),
                completed_processors=self.state.completed_processors.copy(),
                processing_metrics=self.state.processing_metrics.copy()
            )
    
    async def _check_phase_transitions(self) -> None:
        """Check and handle phase transitions based on current state."""
        current_phase = self.state.current_phase
        
        if current_phase == ProcessingPhase.ISSUE_PROCESSING:
            # Check if issue processing is complete
            issue_processors = {"process_upsert_queue"}
            if issue_processors.issubset(self.state.completed_processors):
                self.state.current_phase = ProcessingPhase.SPECIALIZED_PROCESSING
                self.specialized_processing_ready.set()
                self.logger.info("Transitioned to SPECIALIZED_PROCESSING phase")
                
        elif current_phase == ProcessingPhase.SPECIALIZED_PROCESSING:
            # Check if all specialized processing is complete
            specialized_processors = {"process_upsert_others"}
            if specialized_processors.issubset(self.state.completed_processors):
                self.state.current_phase = ProcessingPhase.FINALIZATION
                self.logger.info("Transitioned to FINALIZATION phase")
                
        elif current_phase == ProcessingPhase.FINALIZATION:
            # Check if all processing is complete
            if not self.state.active_processors:
                self.state.current_phase = ProcessingPhase.COMPLETED
                self.all_processing_complete.set()
                self.logger.info("All processing completed")
    
    async def reset(self) -> None:
        """Reset the coordinator for a new processing cycle."""
        async with self._phase_lock:
            self.state = CoordinationState()
            
            # Clear all events
            self.issue_processing_complete.clear()
            self.specialized_processing_ready.clear()
            self.all_processing_complete.clear()
            self.immediate_processing_trigger.clear()
            self.batch_processing_ready.clear()
            self.parent_tables_committed.clear()
            self.child_tables_ready.clear()
            
            # Clear processor events
            for event in self.processor_events.values():
                event.clear()
            self.processor_events.clear()
            
        self.logger.info("Event coordinator reset")


class ReferentialIntegrityCoordinator:
    """
    Specialized coordinator for maintaining referential integrity.
    
    This coordinator ensures that parent tables (like Issue) are committed
    before child tables are processed, maintaining database referential integrity.
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """Initialize the referential integrity coordinator."""
        self.logger = logger or logging.getLogger(__name__)
        
        # Parent-child relationship tracking
        self.parent_tables_committed = asyncio.Event()
        self.child_processing_allowed = asyncio.Event()
        
        # Table hierarchy definition
        self.parent_tables = {"Issue"}
        self.child_tables = {"ChangelogJSON", "WorklogJSON", "CommentJSON", "IssueLinkJSON"}
        
        self.logger.info("Referential Integrity Coordinator initialized")
    
    async def signal_parent_tables_committed(self) -> None:
        """Signal that parent tables have been committed."""
        self.parent_tables_committed.set()
        self.child_processing_allowed.set()
        self.logger.info("Parent tables committed - child processing allowed")
    
    async def wait_for_parent_commitment(self, timeout: Optional[float] = None) -> bool:
        """
        Wait for parent tables to be committed.
        
        Args:
            timeout: Optional timeout in seconds
            
        Returns:
            True if parent tables committed, False if timeout
        """
        try:
            await asyncio.wait_for(self.parent_tables_committed.wait(), timeout=timeout)
            return True
        except asyncio.TimeoutError:
            self.logger.warning("Timeout waiting for parent table commitment")
            return False
    
    def is_parent_table(self, table_name: str) -> bool:
        """Check if a table is a parent table."""
        return table_name in self.parent_tables
    
    def is_child_table(self, table_name: str) -> bool:
        """Check if a table is a child table."""
        return table_name in self.child_tables


# Global instances for use across the application
enhanced_event_coordinator = EnhancedEventCoordinator()
referential_integrity_coordinator = ReferentialIntegrityCoordinator()
