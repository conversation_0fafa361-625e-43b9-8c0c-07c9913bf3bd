# coding=utf-8
"""
Protocol-based interfaces for queue processing.

This module defines simplified Protocol interfaces to replace the complex ABC implementation,
focusing on referential integrity and clean separation of concerns.
"""
import asyncio
from typing import Protocol, Dict, List, Any, Optional, runtime_checkable
from logging import Logger
import pandas as pd
from asyncio import Queue


@runtime_checkable
class QueueProcessorProtocol(Protocol):
    """
    Protocol for queue processors with simplified interface.
    
    This protocol defines the essential methods needed for queue processing
    without the complexity of abstract base classes.
    """
    
    async def process_queue(
        self,
        project_key: str,
        queue: Queue,
        coordination_manager: Any,
        logger: Logger
    ) -> Dict[str, Any]:
        """
        Process items from a queue.
        
        Args:
            project_key: Project key for database operations
            queue: Queue to process items from
            coordination_manager: Coordination manager for event handling
            logger: Logger instance
            
        Returns:
            Processing result dictionary with statistics
        """
        ...


@runtime_checkable
class DataFrameProcessorProtocol(Protocol):
    """
    Protocol for processing consolidated DataFrames.
    
    This protocol handles the actual database operations for consolidated DataFrames,
    maintaining referential integrity between parent and child tables.
    """
    
    async def process_consolidated_dataframes(
        self,
        project_key: str,
        consolidated_dataframes: Dict[str, List[pd.DataFrame]],
        consolidated_configs: Dict[str, Dict[str, Any]],
        logger: Logger,
        message_count: int
    ) -> None:
        """
        Process consolidated DataFrames with referential integrity.
        
        Args:
            project_key: Project key for database operations
            consolidated_dataframes: Dictionary mapping model names to DataFrames
            consolidated_configs: Dictionary mapping model names to configurations
            logger: Logger instance
            message_count: Current message count for logging
        """
        ...


@runtime_checkable
class ReferentialIntegrityProtocol(Protocol):
    """
    Protocol for maintaining referential integrity between tables.
    
    This protocol ensures that parent tables (Issue) are processed before
    child tables (ChangelogJSON, IssueComments, IssueLinks, Worklog).
    """
    
    async def wait_for_parent_commitment(self, timeout: float = 30.0) -> bool:
        """
        Wait for parent tables to be committed.
        
        Args:
            timeout: Maximum time to wait in seconds
            
        Returns:
            True if parent tables were committed, False if timeout
        """
        ...
    
    async def signal_parent_tables_committed(self) -> None:
        """Signal that parent tables have been committed."""
        ...
    
    def is_parent_table(self, table_name: str) -> bool:
        """Check if a table is a parent table."""
        ...
    
    def is_child_table(self, table_name: str) -> bool:
        """Check if a table is a child table."""
        ...


@runtime_checkable
class CoordinationProtocol(Protocol):
    """
    Protocol for coordinating between different queue processors.
    
    This protocol handles event-based coordination between processors
    to ensure proper sequencing and termination.
    """
    
    async def should_process_immediately(self) -> bool:
        """Check if immediate processing should be triggered."""
        ...
    
    async def signal_processing_complete(self, processor_name: str) -> None:
        """Signal that a processor has completed processing."""
        ...
    
    async def wait_for_completion(self, processor_name: str, timeout: float = 30.0) -> bool:
        """
        Wait for a specific processor to complete.
        
        Args:
            processor_name: Name of the processor to wait for
            timeout: Maximum time to wait in seconds
            
        Returns:
            True if processor completed, False if timeout
        """
        ...


class ProcessingResult:
    """
    Simple data class for processing results.
    
    This replaces complex result objects with a simple, focused data structure.
    """
    
    def __init__(
        self,
        messages_processed: int = 0,
        batches_processed: int = 0,
        errors_encountered: int = 0,
        processing_time: float = 0.0,
        success: bool = True
    ):
        self.messages_processed = messages_processed
        self.batches_processed = batches_processed
        self.errors_encountered = errors_encountered
        self.processing_time = processing_time
        self.success = success
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for logging/reporting."""
        return {
            "messages_processed": self.messages_processed,
            "batches_processed": self.batches_processed,
            "errors_encountered": self.errors_encountered,
            "processing_time": self.processing_time,
            "success": self.success
        }


class ReferentialIntegrityManager:
    """
    Simple implementation of referential integrity coordination.
    
    This class provides a concrete implementation of the ReferentialIntegrityProtocol
    with focus on simplicity and reliability.
    """
    
    def __init__(self, logger: Optional[Logger] = None):
        """Initialize the referential integrity manager."""
        self.logger = logger
        self.parent_tables_committed = asyncio.Event()
        
        # Define table hierarchy
        self.parent_tables = {"Issue"}
        self.child_tables = {"ChangelogJSON", "IssueComments", "IssueLinks", "Worklog"}
    
    async def wait_for_parent_commitment(self, timeout: float = 30.0) -> bool:
        """Wait for parent tables to be committed."""
        try:
            await asyncio.wait_for(self.parent_tables_committed.wait(), timeout=timeout)
            return True
        except asyncio.TimeoutError:
            if self.logger:
                self.logger.warning(f"Timeout waiting for parent commitment after {timeout}s")
            return False
    
    async def signal_parent_tables_committed(self) -> None:
        """Signal that parent tables have been committed."""
        self.parent_tables_committed.set()
        if self.logger:
            self.logger.info("Parent tables commitment signaled")
    
    def is_parent_table(self, table_name: str) -> bool:
        """Check if a table is a parent table."""
        return table_name in self.parent_tables
    
    def is_child_table(self, table_name: str) -> bool:
        """Check if a table is a child table."""
        return table_name in self.child_tables


class SimpleCoordinator:
    """
    Simple implementation of coordination protocol.
    
    This class provides basic coordination functionality without the complexity
    of the current event coordination system.
    """
    
    def __init__(self, logger: Optional[Logger] = None):
        """Initialize the simple coordinator."""
        self.logger = logger
        self.immediate_processing_trigger = asyncio.Event()
        self.processor_completion_events: Dict[str, asyncio.Event] = {}
    
    async def should_process_immediately(self) -> bool:
        """Check if immediate processing should be triggered."""
        if self.immediate_processing_trigger.is_set():
            self.immediate_processing_trigger.clear()
            return True
        return False
    
    async def signal_processing_complete(self, processor_name: str) -> None:
        """Signal that a processor has completed processing."""
        if processor_name not in self.processor_completion_events:
            self.processor_completion_events[processor_name] = asyncio.Event()
        
        self.processor_completion_events[processor_name].set()
        
        if self.logger:
            self.logger.info(f"Processor {processor_name} completion signaled")
    
    async def wait_for_completion(self, processor_name: str, timeout: float = 30.0) -> bool:
        """Wait for a specific processor to complete."""
        if processor_name not in self.processor_completion_events:
            self.processor_completion_events[processor_name] = asyncio.Event()
        
        try:
            await asyncio.wait_for(
                self.processor_completion_events[processor_name].wait(),
                timeout=timeout
            )
            return True
        except asyncio.TimeoutError:
            if self.logger:
                self.logger.warning(f"Timeout waiting for {processor_name} completion after {timeout}s")
            return False
    
    def trigger_immediate_processing(self) -> None:
        """Trigger immediate processing."""
        self.immediate_processing_trigger.set()
        if self.logger:
            self.logger.info("Immediate processing triggered")


# Global instances for application use
referential_integrity_manager = ReferentialIntegrityManager()
simple_coordinator = SimpleCoordinator()
