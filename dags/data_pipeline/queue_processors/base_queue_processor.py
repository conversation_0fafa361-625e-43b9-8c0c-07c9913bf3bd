# coding=utf-8
"""
Abstract base class for queue processors following SOLID and DRY principles.

This module provides a common foundation for queue processing operations,
encapsulating shared patterns for DataFrame consolidation, batch processing,
and event coordination.
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from collections import defaultdict
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

import pandas as pd
from dependency_injector.wiring import inject, Provide
from dependency_injector import containers

from dags.data_pipeline.containers import Logger<PERSON>ontainer



@dataclass
class BatchProcessingConfig:
    """Configuration for batch processing behavior."""
    batch_size: int = 100
    enable_early_processing: bool = True
    timeout_seconds: float = 5.0
    max_retries: int = 3


@dataclass
class QueueProcessingResult:
    """Result of queue processing operation."""
    messages_processed: int
    records_processed: Dict[str, int]
    batches_processed: int
    success: bool
    error: Optional[Exception] = None


class DataFramePreprocessor:
    """Handles common DataFrame preprocessing operations."""

    def __init__(self, logger: Optional[logging.Logger] = None):
        """Initialize the DataFrame preprocessor."""
        self.logger = logger or logging.getLogger(__name__)

    def preprocess_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Apply common DataFrame preprocessing steps.

        Args:
            df: DataFrame to preprocess

        Returns:
            Preprocessed DataFrame
        """
        self.logger.debug(f"Preprocessing DataFrame with shape {df.shape}")

        # Apply preprocessing pipeline
        df = self._convert_category_columns(df)
        df = self._handle_empty_dictionaries(df)
        df = self._handle_empty_lists(df)

        self.logger.debug(f"DataFrame preprocessing completed")
        return df

    def _convert_category_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Convert category columns to object type."""
        for col in df.columns:
            if df[col].dtype.name == 'category':
                df[col] = df[col].astype(object)
                self.logger.debug(f"Converted column '{col}' from category to object")
        return df

    def _handle_empty_dictionaries(self, df: pd.DataFrame) -> pd.DataFrame:
        """Replace empty dictionaries with None."""
        for col in df.columns:
            if df[col].apply(lambda x: isinstance(x, dict) and len(x) == 0).any():
                df[col] = df[col].apply(lambda x: None if isinstance(x, dict) and len(x) == 0 else x)
                self.logger.debug(f"Handled empty dictionaries in column '{col}'")
        return df

    def _handle_empty_lists(self, df: pd.DataFrame) -> pd.DataFrame:
        """Replace empty lists with None."""
        for col in df.columns:
            if df[col].apply(lambda x: isinstance(x, list) and len(x) == 0).any():
                df[col] = df[col].apply(lambda x: None if isinstance(x, list) and len(x) == 0 else x)
                self.logger.debug(f"Handled empty lists in column '{col}'")
        return df


class AbstractQueueProcessor(ABC):
    """
    Abstract base class for queue processors.
    
    This class implements the Template Method pattern, providing a common
    structure for queue processing while allowing subclasses to customize
    specific behaviors.
    
    Key responsibilities:
    - DataFrame consolidation by model name
    - Batch processing with configurable triggers
    - Event coordination with coordination_manager
    - Common preprocessing pipeline
    - Error handling and logging
    """
    
    @inject
    def __init__(
        self,
        project_key: str,
        queue_name: str,
        config: BatchProcessingConfig,
        logger: logging.Logger = Provide[LoggerContainer.logger]
    ):
        """
        Initialize the abstract queue processor.
        
        Args:
            project_key: Project key for database operations
            queue_name: Name of the queue to process
            config: Batch processing configuration
            logger: Injected logger instance
        """
        self.project_key = project_key
        self.queue_name = queue_name
        self.config = config
        self.logger = logger
        
        # Processing state
        self.message_count = 0
        self.batch_count = 0
        self.record_count = defaultdict(int)
        
        # DataFrame consolidation
        self.consolidated_dataframes = defaultdict(list)
        self.consolidated_configs = defaultdict(dict)
        
        # Components
        self.preprocessor = DataFramePreprocessor()
        
    async def process_queue(
        self,
        queue: asyncio.Queue,
        coordination_manager: Any,
        q_container: containers.DynamicContainer
    ) -> QueueProcessingResult:
        """
        Main queue processing method implementing the Template Method pattern.
        
        Args:
            queue: Queue to process
            coordination_manager: Coordination manager for event handling
            q_container: Queue container for dependency injection
            
        Returns:
            QueueProcessingResult with processing statistics
        """
        try:
            # Register queue with coordination manager
            coordination_manager.register_queue(self.queue_name, queue)
            
            # Initialize processing
            await self._initialize_processing(coordination_manager)
            
            # Main processing loop
            while True:
                item_fetched = False

                # Check for early processing triggers
                # if await self._should_process_early(coordination_manager):
                #     await self._process_consolidated_data()

                # Check termination conditions
                if await self._should_terminate(coordination_manager):
                    self.logger.info(f"Terminating {self.queue_name} processor")
                    break

                # Process next queue item
                try:
                    item = await self._get_queue_item(queue)
                    item_fetched = True  # Mark that we successfully got an item

                    if item is not None:
                        await self._process_queue_item(item)

                    # Check batch processing triggers
                    # if await self._should_process_batch():
                    #     await self._process_consolidated_data()

                except asyncio.TimeoutError:
                    self.logger.debug(f"Queue get timeout in {self.queue_name}. Queue size: {queue.qsize()}")
                    continue
                except Exception as err:
                    self.logger.exception(f"Error processing queue item in {self.queue_name}: {err}")
                finally:
                    # Call task_done() only if we successfully fetched an item
                    if item_fetched:
                        try:
                            self.logger.debug(f"Marking task as done in {self.queue_name}. Queue size: {queue.qsize()}")
                            queue.task_done()
                        except ValueError as ve:
                            self.logger.warning(f"task_done() error in {self.queue_name}: {ve}. unfinished tasks = {queue._unfinished_tasks}")
                    
            # Process any remaining data
            await self._finalize_processing(coordination_manager)
            
            return QueueProcessingResult(
                messages_processed=self.message_count,
                records_processed=dict(self.record_count),
                batches_processed=self.batch_count,
                success=True
            )
            
        except Exception as e:
            self.logger.error(f"Error in {self.queue_name} processor: {e}")
            return QueueProcessingResult(
                messages_processed=self.message_count,
                records_processed=dict(self.record_count),
                batches_processed=self.batch_count,
                success=False,
                error=e
            )
    
    async def _get_queue_item(self, queue: asyncio.Queue) -> Optional[Dict[str, Any]]:
        """Get next item from queue with timeout."""

        from dags.data_pipeline.priority_queue_system import priority_queue_manager


        return await asyncio.wait_for(
            priority_queue_manager.get_priority_message(queue),
            timeout=self.config.timeout_seconds
        )
    
    async def _process_queue_item(self, item: Dict[str, Any]) -> None:
        """Process a single queue item."""
        if item is None:
            self.logger.debug(f"Received None message in {self.queue_name}")
            return
            
        self.message_count += 1
        self.batch_count += 1
        
        # Extract item data
        model = item["model"]
        df = item["df"]
        
        # Preprocess DataFrame
        df = self.preprocessor.preprocess_dataframe(df)
        
        # Store configuration for this model
        model_name = model.__name__
        if model_name not in self.consolidated_configs:
            self.consolidated_configs[model_name] = {
                "model": model,
                "no_update_cols": item.get("no_update_cols", ()),
                "on_conflict_update": item.get("on_conflict_update", True),
                "conflict_condition": item.get("conflict_condition", None)
            }
            
        # Accumulate DataFrame for this model
        self.consolidated_dataframes[model_name].append(df)
        self.record_count[model_name] += df.shape[0]
        
        self.logger.debug(f"Processed message {self.message_count} for model {model_name}")
    
    async def _process_consolidated_data(self) -> None:
        """Process consolidated DataFrames."""
        if not self.consolidated_dataframes:
            return
            
        await self._process_consolidated_dataframes_impl(
            self.project_key,
            self.consolidated_dataframes,
            self.consolidated_configs,
            self.logger,
            self.message_count
        )
        
        # Clear processed DataFrames
        self.consolidated_dataframes.clear()
        self.consolidated_configs.clear()
        self.batch_count = 0
    
    # Abstract methods for subclass customization
    @abstractmethod
    async def _initialize_processing(self, coordination_manager: Any) -> None:
        """Initialize processing-specific setup."""
        pass
    
    @abstractmethod
    async def _should_process_early(self, coordination_manager: Any) -> bool:
        """Check if early processing should be triggered."""
        pass
    
    @abstractmethod
    async def _should_terminate(self, coordination_manager: Any) -> bool:
        """Check if processing should terminate."""
        pass
    
    @abstractmethod
    async def _should_process_batch(self) -> bool:
        """Check if batch processing should be triggered."""
        pass
    
    @abstractmethod
    async def _finalize_processing(self, coordination_manager: Any) -> None:
        """Finalize processing and cleanup."""
        pass
    
    @abstractmethod
    async def _process_consolidated_dataframes_impl(
        self,
        project_key: str,
        consolidated_dataframes: Dict[str, List[pd.DataFrame]],
        consolidated_configs: Dict[str, Dict[str, Any]],
        logger: logging.Logger,
        message_count: int
    ) -> None:
        """Implementation-specific DataFrame processing."""
        pass
