# coding=utf-8
"""
DataFrame processing with referential integrity support.

This module contains the refactored processing functions moved from utility_code.py,
implementing the Protocol-based approach with focus on referential integrity.
"""
import asyncio
import sys
import traceback
from collections import defaultdict
from datetime import datetime
from typing import Dict, List, Any, Optional
from logging import Logger
import pandas as pd
from dependency_injector.wiring import inject, Provide
from sqlalchemy import text, inspect

from sqlalchemy.dialects.postgresql import dialect, BIGINT, JSONB
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.sql.sqltypes import String, ARRAY, DateTime, NUMERIC, Boolean, INTEGER, TEXT, Text

from dags.data_pipeline.containers import DatabaseContainer
from dags.data_pipeline.database.upsert_operations import upsert_async
from dags.data_pipeline.database.get_model_by_name import get_model_by_name
from dags.data_pipeline.dataframe_utils.dataframe_debugger import quick_save_dataframe_async
from dags.data_pipeline.queue_processors.protocols import (
    DataFrameProcessorProtocol,
    ReferentialIntegrityProtocol,
    referential_integrity_manager
)

import sqlalchemy.exc
from asyncpg.exceptions import ForeignKeyViolationError as AsyncpgForeignKeyViolationError
from psycopg2.errors import ForeignKeyViolation as Psycopg2ForeignKeyViolation


def _extract_key_info_for_retry(df: pd.DataFrame, model_name: str) -> str:
    """Extract key information from DataFrame for retry logging."""
    key_columns = ["issue_key", "key", "id"]
    available_keys = [col for col in key_columns if col in df.columns]
    
    if not available_keys:
        return f"No key columns found in {model_name}"
    
    key_col = available_keys[0]
    sample_keys = df[key_col].dropna().head(5).tolist()
    return f"{model_name} keys: {sample_keys}"


class DataFrameProcessor:
    """
    Concrete implementation of DataFrame processing with referential integrity.
    
    This class handles the processing of consolidated DataFrames, ensuring that
    parent tables (Issue) are processed before child tables to maintain
    referential integrity.
    """
    @inject
    def __init__(
        self,
        referential_integrity: Optional[ReferentialIntegrityProtocol] = None,
        logger: Optional[Logger] = None,
        db_rw_async = Provide[DatabaseContainer.async_session_managers],
        db_rw_sync = Provide[DatabaseContainer.sync_session_managers],
    ):
        """
        Initialize the DataFrame processor.

        Args:
            referential_integrity: Referential integrity coordinator
            logger: Logger instance
            db_rw_async: Async session managers for database operations
        """
        self.referential_integrity = referential_integrity or referential_integrity_manager
        self.logger = logger
        self.db_rw_async = db_rw_async
        self.db_rw_sync = db_rw_sync
    

    async def process_consolidated_dataframes(
        self,
        project_key: str,
        consolidated_dataframes: Dict[str, List[pd.DataFrame]],
        consolidated_configs: Dict[str, Dict[str, Any]],
        logger: Logger,
        message_count: int
    ) -> None:
        """
        Process consolidated DataFrames with referential integrity.
        
        This method ensures that Issue table is processed first, followed by
        child tables in a safe order to maintain referential integrity.
        """
        # Sort to ensure Issue is processed first, then other tables
        sorted_models = sorted(
            consolidated_dataframes.items(), 
            key=lambda x: (x[0] != "Issue", x[0])
        )
        
        for model_name, dataframes in sorted_models:
            logger.debug(f"Processing model: {model_name}")
            
            if not dataframes:
                continue
            
            config = consolidated_configs[model_name]
            model = config["model"]
            
            try:
                # Consolidate DataFrames
                final_df = await self._consolidate_dataframes(dataframes, model_name, logger)
                
                if final_df.empty:
                    continue
                
                logger.debug(f"Processing consolidated {model_name} with {final_df.shape[0]} records")
                
                # Process based on table type
                if model_name == "Issue":
                    await self._process_issue_hierarchy_levels(
                        project_key, final_df, model, config, logger, message_count
                    )
                    # Signal that parent tables are committed
                    await self.referential_integrity.signal_parent_tables_committed()
                    logger.debug("Parent tables commitment signaled")
                else:
                    # For child tables, ensure parent is committed first
                    if self.referential_integrity.is_child_table(model_name):
                        parent_committed = await self.referential_integrity.wait_for_parent_commitment(timeout=30.0)
                        if not parent_committed:
                            logger.warning(f"Proceeding with {model_name} processing despite parent commitment timeout")
                    
                    await self._process_single_model(
                        project_key, final_df, model, config, logger, message_count
                    )
                    logger.debug(f"Processed {model_name} successfully")
                    
            except Exception as e:
                logger.error(f"Error processing consolidated {model_name}: {e}")
                # Don't raise - allow other models to be processed
    
    async def _consolidate_dataframes(
        self, 
        dataframes: List[pd.DataFrame], 
        model_name: str, 
        logger: Logger
    ) -> pd.DataFrame:
        """
        Consolidate multiple DataFrames into a single DataFrame.
        
        Args:
            dataframes: List of DataFrames to consolidate
            model_name: Name of the model being processed
            logger: Logger instance
            
        Returns:
            Consolidated DataFrame
        """
        if not dataframes:
            return pd.DataFrame()
        
        try:
            # Check for column type consistency
            column_types = defaultdict(set)
            for df in dataframes:
                for col in df.columns:
                    column_types[col].update(df[col].map(type).unique())
            
            # Log columns with mixed types
            for col, types in column_types.items():
                if len(types) > 1:
                    logger.debug(f"Column '{col}' has mixed types: {types}")
            
            # Concatenate DataFrames
            final_df = pd.concat(dataframes, ignore_index=True, sort=False)
            return final_df
            
        except Exception as e:
            logger.error(f"Error consolidating DataFrames for {model_name}: {e}")
            return pd.DataFrame()
    

    async def _process_issue_hierarchy_levels(
        self,
        project_key: str,
        df: pd.DataFrame,
        model,
        config: Dict[str, Any],
        logger: Logger,
        message_count: int,
    ) -> None:
        """
        Process Issue DataFrame with hierarchy level handling.
        
        This method processes Issues in the correct hierarchy order:
        Initiative (2) -> Epic (1) -> Story (0) -> Subtask (-1)
        """
        # Sort by hierarchy level in descending order
        df = df.sort_values(by="issue_hierarchy_level", ascending=False)
        
        # Define hierarchy mappings
        hierarchy_mappings = {
            2: "Initiative",
            1: "Epic", 
            0: "Story",
            -1: "Subtask"
        }
        try:
            engine = self.db_rw_sync[project_key].engine
            logger.debug(f"columns = {df.columns}")
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            await quick_save_dataframe_async(df, f"df_issue_check_{timestamp}.xlsx", path=f"c:/vishal/log/issue")
            dtype_mapping = self._build_dtype_mapping(model, df)
            logger.debug(f"Built dtype mapping for {model.__tablename__}: {dtype_mapping}")

            # dtype_mapping_update = {'id': BIGINT(), 'key': String(), 'statuscategorychangedate': DateTime(timezone=True), 'components': ARRAY(String()),
            #                         'timespent': NUMERIC(), 'fixVersions': ARRAY(String()), 'aggregatetimespent': NUMERIC(), 'aggregatetimeestimate': NUMERIC(),
            #                         'summary': String(), 'created': DateTime(timezone=True), 'Rank': String(), 'timeestimate': NUMERIC(), 'versions': ARRAY(String()),
            #                         'updated': DateTime(timezone=True), 'issuetype': String(), 'isSubTask': Boolean(), 'issue_hierarchy_level': INTEGER(),
            #                         'parent_id': BIGINT(), 'parent_key': String(), 'reporter': String(), 'aggregateprogress_progress': NUMERIC(),
            #                         'aggregateprogress_total': NUMERIC(), 'aggregateprogress_percent': NUMERIC(), 'priority': String(), 'progress_progress': NUMERIC(),
            #                         'progress_total': NUMERIC(), 'progress_percent': NUMERIC(), 'assignee': String(), 'status': String(), 'statusCategory': String(),
            #                         'description_markdown': TEXT(), 'timetracking': JSONB(astext_type=Text())}

            df.to_sql(
                f"{model.__tablename__}_temp",
                engine,   # pass Connection, not Engine
                schema=project_key,
                if_exists="replace",
                index=False,
                method="multi",  # optional for batch inserts
                dtype=dtype_mapping
            )

        except Exception as e:
            exc_type, exc_value, exc_tb = sys.exc_info()
            line_num = exc_tb.tb_lineno
            tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
            logger.error(
                f"Line {line_num} Error encountered: {''.join(tb.format_exception_only())}",
                exc_info=True
            )
            logger.error(f"Error creating temp table for {model.__tablename__}: {e}")
            raise


        # logger.debug(f"Temp table created for {model.__tablename__}")
        # Process in specific order to maintain referential integrity
        processing_order = [2, 1, 0, -1]

        # for key in config.keys():
        #     logger.debug(f"Config key: {key}")
        #
        # for hierarchy_level in processing_order:
        #     level_df = df[df["issue_hierarchy_level"] == hierarchy_level]
        #
        #     if not level_df.empty:
        #         level_name = hierarchy_mappings[hierarchy_level]
        #         logger.debug(f"Processing {level_name} (level={hierarchy_level}) with {len(level_df)} records")
        #
        #         try:
        #             async with self.db_rw_async[project_key].async_session() as pg_session:
        #                 async with pg_session.begin():
        #                     success = await upsert_async(
        #                         pg_session,
        #                         model, level_df,
        #                         no_update_cols=config.get("no_update_cols", ()),
        #                         on_conflict_update=config.get("on_conflict_update", False),
        #                         conflict_condition=config.get("conflict_condition", None),
        #                         message_count=message_count,
        #                         my_logger=logger
        #                     )
        #
        #                     if not success:
        #                         logger.error(f"Upsert failed for {level_name} batch={message_count}, records={len(level_df)}")
        #                         raise Exception(f"Upsert failed for {level_name}")
        #
        #                     logger.debug(f"Successfully processed {level_name} with {len(level_df)} records")
        #
        #         except Exception as e:
        #             exc_type, exc_value, exc_tb = sys.exc_info()
        #             line_num = exc_tb.tb_lineno
        #             tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
        #             logger.error(
        #                 f"Line {line_num} Error encountered: {''.join(tb.format_exception_only())}",
        #                 exc_info=True
        #             )
        #             logger.error(f"Error processing {level_name}: {e}")
        #             raise
    

    async def _process_single_model(
        self,
        project_key: str,
        df: pd.DataFrame,
        model,
        config: Dict[str, Any],
        logger: Logger,
        message_count: int,
    ) -> None:
        """
        Process a single model DataFrame using temporary table approach.

        This method uses .to_sql() to create a temporary table, then merges
        the data to avoid clientRead wait events.
        """
        model_name = model.__name__

        # Apply model-specific sorting for child tables
        if model_name in ["ChangelogJSON", "Worklog", "IssueComments", "IssueLinks"]:
            if df.empty:
                return
            df = df.sort_values(by="issue_id", ascending=True)

        try:
            engine = self.db_rw_sync[project_key].engine
            df.to_sql(
                f"{model.__tablename__}_temp",
                engine,   # pass Connection, not Engine
                schema=project_key,
                if_exists="replace",
                index=False,
                method="multi"  # optional for batch inserts
            )
            # await self._process_with_temp_table(
            #     project_key, model_name, df, model, config, logger, message_count
            # )
            logger.debug(f"Successfully processed {model_name} with {len(df)} records")

        except sqlalchemy.exc.IntegrityError as e:
            # Handle foreign key violations with proper logging
            if hasattr(e, 'orig') and e.orig:
                if isinstance(e.orig, (AsyncpgForeignKeyViolationError, Psycopg2ForeignKeyViolation)):
                    key_info = _extract_key_info_for_retry(df, model_name)
                    logger.error(f"Foreign key violation in _process_single_model: {key_info}")
                    logger.debug(f"Foreign key violation details: {type(e.orig).__name__}: {e.orig}")
                    raise Exception(f"Foreign key violation for {model_name}")
            raise
        except Exception as e:
            exc_type, exc_value, exc_tb = sys.exc_info()
            line_num = exc_tb.tb_lineno
            tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
            logger.error(
                f"Line {line_num} Error encountered: {''.join(tb.format_exception_only())}",
                exc_info=True
            )
            logger.error(f"Error processing {model_name}: {e}")
            raise

    async def _process_with_temp_table(
        self,
        project_key: str,
        model_name: str,
        df: pd.DataFrame,
        model,
        config: Dict[str, Any],
        logger: Logger,
        message_count: int
    ) -> None:
        """
        Process DataFrame using temporary table approach with direct SQL.

        This method:
        1. Creates a temporary table using direct SQL CREATE TABLE
        2. Inserts data using bulk INSERT statements
        3. Merges data from temp table to main table using SQL
        4. Avoids clientRead wait events by using bulk operations
        """
        temp_table_name = f"{model.__tablename__}_temp"

        # Use async session for all operations
        async with self.db_rw_async[project_key].async_session() as pg_async_session:
            async with pg_async_session.begin():
                # Create temporary table and insert data
                await self._create_temp_table_and_insert(
                    pg_async_session, model, temp_table_name, df, logger
                )

                # Merge data from temp table to main table
                await self._merge_from_temp_table(
                    pg_async_session, model, temp_table_name, config, logger, message_count
                )

                logger.debug(f"Successfully processed {model_name} with temp table approach, {len(df)} records")

    def _build_dtype_mapping(self, model, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Build pandas dtype mapping from SQLAlchemy model for .to_sql().

        This dynamically creates the correct SQLAlchemy data types for the
        temporary table based on the ORM model definition.

        Args:
            model: SQLAlchemy model class
            df: DataFrame to get column list from

        Returns:
            Dictionary mapping column names to SQLAlchemy type objects
        """
        from sqlalchemy import Text

        dtype_mapping = {}

        # Get model columns that exist in the DataFrame
        model_columns = {col.name: col for col in model.__table__.columns}

        for col_name in df.columns:
            if col_name in model_columns:
                col = model_columns[col_name]
                # Use the actual SQLAlchemy type object, not string representation
                dtype_mapping[col_name] = col.type
            else:
                # Fallback for columns not in model (shouldn't happen normally)
                dtype_mapping[col_name] = Text()

        return dtype_mapping

    async def _create_temp_table_and_insert(
        self,
        session,
        model,
        temp_table_name: str,
        df: pd.DataFrame,
        logger: Logger
    ) -> None:
        """
        Create temporary table and insert DataFrame data using direct SQL.

        This avoids the pandas .to_sql() compatibility issues by using
        direct SQL statements for table creation and data insertion.
        """
        if df.empty:
            logger.debug(f"Empty DataFrame - skipping temp table creation for {temp_table_name}")
            return

        # Build dtype mapping from SQLAlchemy model
        dtype_mapping = self._build_dtype_mapping(model, df)
        logger.debug(f"Built dtype mapping for {temp_table_name}: {dtype_mapping}")

        # Get the raw connection for pandas .to_sql()
        # Use the session's connection and get the underlying DBAPI connection
        raw_conn = session.connection().connection

        # Create temporary table with correct data types
        df.to_sql(
            name=temp_table_name,
            con=raw_conn,
            if_exists='replace',
            index=False,
            method='multi',
            dtype=dtype_mapping
        )

        logger.debug(f"Created temp table {temp_table_name} with {len(df)} records using .to_sql()")

    async def _merge_from_temp_table(
        self,
        session,
        model,
        temp_table_name: str,
        config: Dict[str, Any],
        logger: Logger,
        message_count: int
    ) -> None:
        """
        Merge data from temporary table to main table using SQL.

        This implements upsert logic using SQL MERGE or INSERT...ON CONFLICT.
        """
        table_name = model.__tablename__
        primary_keys = [col.name for col in inspect(model).primary_key]

        # Get all columns except those in no_update_cols
        no_update_cols = set(config.get("no_update_cols", []))
        all_columns = [col.name for col in model.__table__.columns]
        update_columns = [col for col in all_columns if col not in no_update_cols and col not in primary_keys]

        # Build conflict target (primary keys)
        conflict_target = ", ".join(primary_keys)

        # Build update clause
        update_clause = ", ".join([f"{col} = EXCLUDED.{col}" for col in update_columns])

        # Build the upsert query
        if config.get("on_conflict_update", True) and update_columns:
            merge_sql = f"""
            INSERT INTO {table_name}
            SELECT * FROM {temp_table_name}
            ON CONFLICT ({conflict_target})
            DO UPDATE SET {update_clause}
            """
        else:
            # Insert only, ignore conflicts
            merge_sql = f"""
            INSERT INTO {table_name}
            SELECT * FROM {temp_table_name}
            ON CONFLICT ({conflict_target}) DO NOTHING
            """

        try:
            result = await session.execute(text(merge_sql))
            logger.debug(f"Merge completed for {table_name}, affected rows: {result.rowcount}")

            # Clean up temp table
            await session.execute(text(f"DROP TABLE IF EXISTS {temp_table_name}"))

        except Exception as e:
            logger.error(f"Error in merge operation for {table_name}: {e}")
            # Try to clean up temp table even on error
            try:
                await session.execute(text(f"DROP TABLE IF EXISTS {temp_table_name}"))
            except:
                pass  # Ignore cleanup errors
            raise


db_container = DatabaseContainer()
db_container.wire(modules=["dags.data_pipeline.queue_processors.dataframe_processor", __name__])

# Global instance for application use
dataframe_processor = DataFrameProcessor(logger=None)
