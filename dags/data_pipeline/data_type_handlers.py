# coding=utf-8
"""
Data type handling and validation utilities for JIRA data processing.

This module provides robust data type conversion with error handling,
retry mechanisms, and graceful fallbacks to prevent processing failures.
"""
import warnings
from datetime import datetime

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Union, Tuple
from dataclasses import dataclass
from dependency_injector.wiring import inject, Provide

from .dataframe_utils.dataframe_debugger import quick_save_dataframe

# from .dataframe_utils.dataframe_debug_sync import quick_save_dataframe

try:
    from containers import LoggerContainer
    from field_mappers import get_field_mapper, get_data_type_mapper
except ModuleNotFoundError:
    from .containers import LoggerContainer
    from .field_mappers import get_field_mapper, get_data_type_mapper


@dataclass
class TypeConversionResult:
    """Result of a type conversion operation."""
    success: bool
    column: str
    original_type: str
    target_type: str
    error_message: Optional[str] = None
    rows_affected: int = 0
    rows_failed: int = 0


class RobustDataTypeHandler:
    """Handles robust data type conversion with error handling and fallbacks."""
    
    def __init__(self):
        self.field_mapper = get_field_mapper()
        self.data_type_mapper = get_data_type_mapper()
        self.conversion_results: List[TypeConversionResult] = []
    
    def safe_astype_conversion(
        self,
        df: pd.DataFrame,
        type_mapping: Dict[str, Any],
        my_logger=None
    ) -> Tuple[pd.DataFrame, List[TypeConversionResult]]:
        """
        Safely convert DataFrame columns with comprehensive error handling.
        
        Args:
            df: DataFrame to convert
            type_mapping: Dictionary mapping column names to target types
            my_logger: Logger instance
            
        Returns:
            Tuple of (converted DataFrame, list of conversion results)
        """
        df = df.copy()
        results = []
        
        for column, target_type in type_mapping.items():
            if column not in df.columns:
                continue

            result = self._convert_single_column(df, column, target_type, my_logger)
            results.append(result)
            
            if not result.success:
                if my_logger:
                    my_logger.warning(
                        f"Failed to convert column '{column}' to {target_type}: {result.error_message}"
                    )
        
        return df, results
    
    def _convert_single_column(
        self,
        df: pd.DataFrame,
        column: str,
        target_type: Any,
        my_logger
    ) -> TypeConversionResult:
        """Convert a single column with error handling."""
        if column not in df.columns:
            return TypeConversionResult(
                success=False,
                column=column,
                original_type="",
                target_type=str(target_type),
                error_message="Column not found",
                rows_affected=0,
                rows_failed=0
            )

        try:
            original_type = str(df[column].dtype)
        except AttributeError as e:
            if my_logger:
                my_logger.error(f"Failed to get original type for column '{column}': {e}")
            original_type = "Unknown"
        rows_total = len(df)


        try:
            # Pre-conversion data cleaning
            if column == "parent_key":
                df[column] = df[column].astype(str)
            elif column == "isSubTask":
                df[column] = df[column].astype(bool)
            elif isinstance(target_type, str) and target_type in ['Int64', 'Int32', 'Int16', 'Int8', 'Float64', 'Float32']:
                df = self._clean_numeric_column(df, column, my_logger)

            # Attempt conversion
            if column in ["parent_key"]:
                pass
            elif column in ["description_markdown"]:
                df[column] = df[column].astype("string[pyarrow]")
            elif target_type == "json":
                pass
            elif target_type == 'datetime':
                # df[column] = df[column].apply(pd.to_datetime, errors='coerce', utc=True)
                # df[column] = df[column].apply(lambda x: x.to_pydatetime() if pd.notnull(x) else None)
                df[column] = (
                    pd.to_datetime(df[column], errors='coerce', utc=True)
                    .apply(lambda x: x.to_pydatetime() if pd.notnull(x) else None)
                )
            elif target_type == "date":
                # Convert to datetime first, then extract only the date part
                df[column] = pd.to_datetime(df[column], errors='coerce').dt.date
            else:
                try:
                    df[column] = df[column].astype(target_type)
                except Exception as e:
                    if my_logger:
                        my_logger.error(f"Failed to convert column '{column}' to {target_type}: {e}")
                    _ = quick_save_dataframe(df, f"failed_rows_{column}.xlsx", path=f"c:/vishal/log/failed_conversion")

            
            # Count successful conversions
            rows_failed = df[column].isna().sum() if hasattr(df[column], 'isna') else 0
            rows_affected = rows_total - rows_failed
            
            return TypeConversionResult(
                success=True,
                column=column,
                original_type=original_type,
                target_type=str(target_type),
                rows_affected=rows_affected,
                rows_failed=rows_failed
            )
            
        except Exception as e:
            error_msg = f"Type conversion failed: {str(e)}"
            if my_logger:
                my_logger.error(f"Column '{column}' conversion error: {error_msg}")

                # Enhanced error reporting for debugging
                if column in ['storypoints', 'id', 'parent_id']:
                    my_logger.error(f"  Column: {column}")
                    my_logger.error(f"  Target type: {target_type}")
                    my_logger.error(f"  Original type: {original_type}")
                    my_logger.error(f"  Total rows: {rows_total}")

                    # Show sample of data that failed conversion
                    if not df[column].empty:
                        sample_data = df[column].dropna().head(3).tolist()
                        my_logger.error(f"  Sample data: {sample_data}")

                        # If we have issue keys, show them for context
                        if 'key' in df.columns and len(df) > 0:
                            sample_keys = df['key'].head(3).tolist()
                            my_logger.error(f"  Sample issue keys: {sample_keys}")

            return TypeConversionResult(
                success=False,
                column=column,
                original_type=original_type,
                target_type=str(target_type),
                error_message=error_msg,
                rows_affected=0,
                rows_failed=rows_total
            )
    
    def _clean_numeric_column(self, df: pd.DataFrame, column: str, my_logger) -> pd.DataFrame:
        """Clean numeric column before conversion."""
        try:
            if df[column].dtype == 'object':
                # Convert to string and clean
                df[column] = df[column].astype(str).str.strip()
                
                # Replace various null representations
                null_values = ['', 'nan', 'null', 'None', 'NaN', 'NULL']
                df[column] = df[column].replace(null_values, pd.NA)
                
                # Remove trailing .0 from strings that represent integers
                df[column] = df[column].replace(r'\.0+$', '', regex=True)
                
                # Convert to numeric first
                df[column] = pd.to_numeric(df[column], errors='coerce')
                
            # if my_logger:
            #     my_logger.debug(f"Cleaned numeric column '{column}'")

        except Exception as e:
            if my_logger:
                my_logger.warning(f"Failed to clean column '{column}': {e}")
        
        return df
    
    def apply_field_based_type_conversion(
        self,
        df: pd.DataFrame,
        my_logger=None
    ) -> Tuple[pd.DataFrame, List[TypeConversionResult]]:
        """
        Apply type conversion based on field mapper configuration.
        
        Args:
            df: DataFrame to convert
            my_logger: Logger instance
            
        Returns:
            Tuple of (converted DataFrame, list of conversion results)
        """
        type_mapping = self.field_mapper.get_type_mapping()
        
        # Filter to only existing columns
        existing_type_mapping = {
            col: self.data_type_mapper.get_pandas_dtype(type_name)
            for col, type_name in type_mapping.items()
            if col in df.columns
        }
        
        if existing_type_mapping:
            # if my_logger:
            #     my_logger.debug(f"Applying type conversion to {len(existing_type_mapping)} columns")
            return self.safe_astype_conversion(df, existing_type_mapping, my_logger)
        else:
            # if my_logger:
            #     my_logger.debug("No columns found for type conversion")
            return df, []
    
    def handle_special_conversions(
        self,
        df: pd.DataFrame,
        my_logger=None
    ) -> pd.DataFrame:
        """
        Handle special data type conversions that require custom logic.
        
        Args:
            df: DataFrame to process
            my_logger: Logger instance
            
        Returns:
            Processed DataFrame
        """
        df = df.copy()
        
        # Handle datetime columns
        datetime_columns = self.field_mapper.get_columns_by_type('datetime')
        existing_datetime_cols = [col for col in datetime_columns if col in df.columns]
        
        if existing_datetime_cols:
            for col in existing_datetime_cols:
                try:
                    with warnings.catch_warnings(record=True) as w:
                        warnings.simplefilter("always")

                        converted  = pd.to_datetime(df[col], errors='coerce', utc=True, format='%Y-%m-%dT%H:%M:%S.%f%z')
                        # Identify rows that failed conversion
                        failed_mask = converted.isna() & df[col].notna()

                        # Replace column only after analysis
                        df[col] = converted

                        for warn in w:
                            if (
                                    issubclass(warn.category, UserWarning)
                                    and "Could not infer format" in str(warn.message)
                            ):
                                if my_logger:
                                    my_logger.warning(
                                        f"Ambiguous datetime format in column '{col}', fallback to element-wise parsing."
                                    )
                                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

                                _ = quick_save_dataframe(
                                    df, f"failed_rows_{col}_{timestamp}.xlsx", path=f"c:/vishal/log/failed_ts_rows",

                                )
                                # Save only the problematic rows for inspection
                                failed_rows = df.loc[failed_mask, [col]]
                                if not failed_rows.empty:
                                    _ = quick_save_dataframe(
                                        failed_rows,
                                        f"failed_rows_{col}_{timestamp}_filtered.csv",
                                        path="c:/vishal/log/failed_ts_rows",
                                        file_format="csv"
                                    )


                except Exception as e:
                    if my_logger:
                        my_logger.error(f"Failed to convert '{col}' to datetime: {e}")
        
        # Handle date columns
        date_columns = self.field_mapper.get_columns_by_type('date')
        existing_date_cols = [col for col in date_columns if col in df.columns]
        
        if existing_date_cols:
            for col in existing_date_cols:
                try:
                    df[col] = pd.to_datetime(df[col], errors='coerce').dt.date
                    # if my_logger:
                    #     my_logger.debug(f"Converted '{col}' to date")
                except Exception as e:
                    if my_logger:
                        my_logger.error(f"Failed to convert '{col}' to date: {e}")
        
        # Handle array columns (components, fixVersions, versions)
        array_columns = ['components', 'fixVersions', 'versions', 'ClientJira']
        for col in array_columns:
            if col in df.columns:
                try:
                    df[col] = df[col].apply(
                        # lambda x: [value['name'] for value in x] if isinstance(x, list) else None
                        lambda x: [value['name'] for value in x] if isinstance(x, list)
                        else ([x] if isinstance(x, str) and x else [])
                    )
                    bad_mask = df[col].apply(lambda x: not (isinstance(x, list) or x is None))
                    if bad_mask.any():
                        bad_rows = df.loc[bad_mask, ['issue_key', col]]
                        bad_rows.to_csv(f"c:/vishal/log/bad_rows_{col}.csv", index=False)
                        if my_logger:
                            my_logger.warning(f"Found {bad_mask.sum()} bad rows in {col}, saved to bad_rows_{col}.csv")
                except Exception as e:
                    if my_logger:
                        my_logger.error(f"Failed to process array column '{col}': {e}")
        
        return df
    
    def get_conversion_summary(self) -> Dict[str, Any]:
        """Get summary of all conversion operations."""
        if not self.conversion_results:
            return {"total_conversions": 0, "successful": 0, "failed": 0}
        
        successful = sum(1 for r in self.conversion_results if r.success)
        failed = len(self.conversion_results) - successful
        
        return {
            "total_conversions": len(self.conversion_results),
            "successful": successful,
            "failed": failed,
            "success_rate": successful / len(self.conversion_results) if self.conversion_results else 0,
            "details": self.conversion_results
        }
    
    def reset_results(self):
        """Reset conversion results."""
        self.conversion_results.clear()


def create_robust_type_handler() -> RobustDataTypeHandler:
    """Factory function to create a new RobustDataTypeHandler instance."""
    return RobustDataTypeHandler()


# Utility functions for backward compatibility
def safe_cast_columns(
    df: pd.DataFrame,
    columns: List[str],
    dtype: Union[str, pd.Int64Dtype, pd.Float64Dtype],
    my_logger=None
) -> pd.DataFrame:
    """
    Enhanced version of the original cast_columns function with better error handling.
    
    Args:
        df: DataFrame to modify
        columns: List of column names to cast
        dtype: Target data type
        my_logger: Logger instance
        
    Returns:
        Modified DataFrame
    """
    handler = create_robust_type_handler()
    
    # Select the columns that exist in the DataFrame
    existing_columns = [col for col in columns if col in df.columns]
    
    if not existing_columns:
        if my_logger:
            my_logger.debug(f"None of the specified columns {columns} exist in DataFrame")
        return df
    
    # Create type mapping
    type_mapping = {col: dtype for col in existing_columns}
    
    # Apply conversion
    df_result, results = handler.safe_astype_conversion(df, type_mapping, my_logger)
    
    # Log results
    failed_conversions = [r for r in results if not r.success]
    if failed_conversions and my_logger:
        my_logger.warning(f"Failed to convert {len(failed_conversions)} columns: "
                         f"{[r.column for r in failed_conversions]}")

    return df_result
