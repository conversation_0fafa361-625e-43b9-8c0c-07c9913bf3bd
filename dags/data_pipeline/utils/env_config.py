"""
Environment Configuration Utility Module

This module provides utilities for loading, validating, and managing environment variables
with proper type conversion, validation, and environment-specific configuration loading.

Features:
- Environment-specific .env file loading
- Type conversion and validation
- Default value handling
- Configuration validation
- Environment detection
"""

import os
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Type, Callable
from dataclasses import dataclass, field
from enum import Enum

from dotenv import load_dotenv


logger = logging.getLogger(__name__)


class Environment(Enum):
    """Supported environment types."""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TESTING = "testing"


@dataclass
class EnvVarConfig:
    """Configuration for an environment variable."""
    name: str
    var_type: Type = str
    default: Any = None
    required: bool = False
    validator: Optional[Callable[[Any], bool]] = None
    description: str = ""


@dataclass
class DatabaseConfig:
    """Database configuration from environment variables."""
    host: str = "localhost"
    port_rw: int = 5432
    port_ro: int = 5433
    name: str = "alembic_migration"
    driver: str = "postgresql+psycopg2"
    public_schema: str = "public"
    custom_schemas: List[str] = field(default_factory=lambda: ["plat", "plp", "acq"])
    default_schema: str = "plat"
    
    # Pool settings
    pool_size: int = 20
    max_overflow: int = 10
    pool_recycle: int = 3600
    pool_timeout: int = 60
    pool_pre_ping: bool = True
    
    # Connection settings
    connect_timeout: int = 30
    keepalives: int = 1
    keepalives_idle: int = 30
    keepalives_interval: int = 10
    keepalives_count: int = 5


@dataclass
class JiraConfig:
    """JIRA API configuration from environment variables."""
    base_url: str = "https://corecard.atlassian.net"
    accept_header: str = "application/json"
    content_type: str = "application/json"
    max_retries: int = 5
    initial_retry_delay: int = 5000
    max_retry_delay: int = 10000
    jitter_min: float = 0.5
    jitter_max: float = 1.5


@dataclass
class CircuitBreakerConfig:
    """Circuit breaker configuration from environment variables."""
    failure_threshold: int = 5
    recovery_timeout: float = 30.0
    half_open_max_calls: int = 3
    max_consecutive_rate_limits: int = 3
    connection_pool_retry_multiplier: float = 2.0
    network_error_retry_multiplier: float = 1.5
    max_concurrent_connections: int = 50
    health_check_interval: float = 10.0


@dataclass
class LoggingConfig:
    """Logging configuration from environment variables."""
    config_path: str = "logging_config.yaml"
    level: str = "INFO"
    file_level: str = "DEBUG"
    console_level: str = "WARNING"
    db_log_batch_size: int = 50
    db_log_flush_interval: int = 30
    db_log_enabled: bool = True
    enable_debug_monitor: bool = True
    debug_monitor_interval: int = 30


class EnvironmentConfigLoader:
    """
    Utility class for loading and managing environment-specific configurations.
    
    This class handles:
    - Loading environment-specific .env files
    - Type conversion and validation
    - Configuration object creation
    - Environment detection
    """
    
    def __init__(self, project_root: Optional[Path] = None):
        """
        Initialize the environment configuration loader.
        
        Args:
            project_root: Path to the project root directory. If None, auto-detected.
        """
        self.project_root = project_root or self._detect_project_root()
        self.current_env = self._detect_environment()
        self._load_environment_file()
        
    def _detect_project_root(self) -> Path:
        """Detect the project root directory."""
        current_path = Path(__file__).resolve()
        
        # Look for pyproject.toml or .git to identify project root
        for parent in current_path.parents:
            if (parent / "pyproject.toml").exists() or (parent / ".git").exists():
                return parent
                
        # Fallback to current working directory
        return Path.cwd()
    
    def _detect_environment(self) -> Environment:
        """Detect the current environment."""
        env_name = os.getenv("ENVIRONMENT", "development").lower()
        
        try:
            return Environment(env_name)
        except ValueError:
            logger.warning(f"Unknown environment '{env_name}', defaulting to development")
            return Environment.DEVELOPMENT
    
    def _load_environment_file(self) -> None:
        """Load the appropriate .env file based on the current environment."""
        env_file = self.project_root / f".env.{self.current_env.value}"
        
        if env_file.exists():
            logger.info(f"Loading environment file: {env_file}")
            load_dotenv(env_file, override=True)
        else:
            logger.warning(f"Environment file not found: {env_file}")
            
        # Also load base .env file if it exists
        base_env_file = self.project_root / ".env"
        if base_env_file.exists():
            logger.info(f"Loading base environment file: {base_env_file}")
            load_dotenv(base_env_file, override=False)  # Don't override environment-specific values
    
    def get_env_var(self, 
                   name: str, 
                   var_type: Type = str, 
                   default: Any = None, 
                   required: bool = False,
                   validator: Optional[Callable[[Any], bool]] = None) -> Any:
        """
        Get an environment variable with type conversion and validation.
        
        Args:
            name: Environment variable name
            var_type: Expected type for conversion
            default: Default value if not found
            required: Whether the variable is required
            validator: Optional validation function
            
        Returns:
            The environment variable value with proper type conversion
            
        Raises:
            ValueError: If required variable is missing or validation fails
        """
        raw_value = os.getenv(name)
        
        if raw_value is None:
            if required:
                raise ValueError(f"Required environment variable '{name}' is not set")
            return default
        
        # Type conversion
        try:
            if var_type == bool:
                converted_value = raw_value.lower() in ('true', '1', 'yes', 'on')
            elif var_type == list:
                converted_value = [item.strip() for item in raw_value.split(',') if item.strip()]
            elif var_type in (int, float):
                converted_value = var_type(raw_value)
            else:
                converted_value = var_type(raw_value)
        except (ValueError, TypeError) as e:
            logger.error(f"Failed to convert environment variable '{name}' to {var_type.__name__}: {e}")
            if required:
                raise ValueError(f"Invalid value for required environment variable '{name}': {raw_value}")
            return default
        
        # Validation
        if validator and not validator(converted_value):
            error_msg = f"Validation failed for environment variable '{name}': {converted_value}"
            logger.error(error_msg)
            if required:
                raise ValueError(error_msg)
            return default
        
        return converted_value
    
    def get_database_config(self) -> DatabaseConfig:
        """Get database configuration from environment variables."""
        return DatabaseConfig(
            host=self.get_env_var("DB_HOST", str, "localhost"),
            port_rw=self.get_env_var("DB_PORT_RW", int, 5432),
            port_ro=self.get_env_var("DB_PORT_RO", int, 5433),
            name=self.get_env_var("DB_NAME", str, "alembic_migration"),
            driver=self.get_env_var("DB_DRIVER", str, "postgresql+psycopg2"),
            public_schema=self.get_env_var("DB_PUBLIC_SCHEMA", str, "public"),
            custom_schemas=self.get_env_var("DB_CUSTOM_SCHEMAS", list, ["plat", "plp", "acq"]),
            default_schema=self.get_env_var("DB_DEFAULT_SCHEMA", str, "plat"),
            pool_size=self.get_env_var("DB_POOL_SIZE", int, 20),
            max_overflow=self.get_env_var("DB_MAX_OVERFLOW", int, 10),
            pool_recycle=self.get_env_var("DB_POOL_RECYCLE", int, 3600),
            pool_timeout=self.get_env_var("DB_POOL_TIMEOUT", int, 60),
            pool_pre_ping=self.get_env_var("DB_POOL_PRE_PING", bool, True),
            connect_timeout=self.get_env_var("DB_CONNECT_TIMEOUT", int, 30),
            keepalives=self.get_env_var("DB_KEEPALIVES", int, 1),
            keepalives_idle=self.get_env_var("DB_KEEPALIVES_IDLE", int, 30),
            keepalives_interval=self.get_env_var("DB_KEEPALIVES_INTERVAL", int, 10),
            keepalives_count=self.get_env_var("DB_KEEPALIVES_COUNT", int, 5),
        )
    
    def get_jira_config(self) -> JiraConfig:
        """Get JIRA configuration from environment variables."""
        return JiraConfig(
            base_url=self.get_env_var("JIRA_BASE_URL", str, "https://corecard.atlassian.net"),
            accept_header=self.get_env_var("JIRA_ACCEPT_HEADER", str, "application/json"),
            content_type=self.get_env_var("JIRA_CONTENT_TYPE", str, "application/json"),
            max_retries=self.get_env_var("JIRA_MAX_RETRIES", int, 5),
            initial_retry_delay=self.get_env_var("JIRA_INITIAL_RETRY_DELAY", int, 5000),
            max_retry_delay=self.get_env_var("JIRA_MAX_RETRY_DELAY", int, 10000),
            jitter_min=self.get_env_var("JIRA_JITTER_MIN", float, 0.5),
            jitter_max=self.get_env_var("JIRA_JITTER_MAX", float, 1.5),
        )
    
    def get_circuit_breaker_config(self) -> CircuitBreakerConfig:
        """Get circuit breaker configuration from environment variables."""
        return CircuitBreakerConfig(
            failure_threshold=self.get_env_var("CB_FAILURE_THRESHOLD", int, 5),
            recovery_timeout=self.get_env_var("CB_RECOVERY_TIMEOUT", float, 30.0),
            half_open_max_calls=self.get_env_var("CB_HALF_OPEN_MAX_CALLS", int, 3),
            max_consecutive_rate_limits=self.get_env_var("CB_MAX_CONSECUTIVE_RATE_LIMITS", int, 3),
            connection_pool_retry_multiplier=self.get_env_var("CB_CONNECTION_POOL_RETRY_MULTIPLIER", float, 2.0),
            network_error_retry_multiplier=self.get_env_var("CB_NETWORK_ERROR_RETRY_MULTIPLIER", float, 1.5),
            max_concurrent_connections=self.get_env_var("CB_MAX_CONCURRENT_CONNECTIONS", int, 50),
            health_check_interval=self.get_env_var("CB_HEALTH_CHECK_INTERVAL", float, 10.0),
        )
    
    def get_logging_config(self) -> LoggingConfig:
        """Get logging configuration from environment variables."""
        return LoggingConfig(
            config_path=self.get_env_var("LOGGING_CONFIG_PATH", str, "logging_config.yaml"),
            level=self.get_env_var("LOG_LEVEL", str, "INFO"),
            file_level=self.get_env_var("LOG_FILE_LEVEL", str, "DEBUG"),
            console_level=self.get_env_var("LOG_CONSOLE_LEVEL", str, "WARNING"),
            db_log_batch_size=self.get_env_var("DB_LOG_BATCH_SIZE", int, 50),
            db_log_flush_interval=self.get_env_var("DB_LOG_FLUSH_INTERVAL", int, 30),
            db_log_enabled=self.get_env_var("DB_LOG_ENABLED", bool, True),
            enable_debug_monitor=self.get_env_var("ENABLE_DEBUG_MONITOR", bool, True),
            debug_monitor_interval=self.get_env_var("DEBUG_MONITOR_INTERVAL", int, 30),
        )


# Global instance for easy access
env_config = EnvironmentConfigLoader()


def get_env_config() -> EnvironmentConfigLoader:
    """Get the global environment configuration loader instance."""
    return env_config


def reload_env_config(project_root: Optional[Path] = None) -> EnvironmentConfigLoader:
    """Reload the global environment configuration."""
    global env_config
    env_config = EnvironmentConfigLoader(project_root)
    return env_config


class HybridConfigProvider:
    """
    Hybrid configuration provider that combines environment variables with YAML configuration.

    This class provides a bridge between environment variables and dependency injection
    containers, allowing for proper integration without breaking the provider patterns.
    """

    def __init__(self, env_config_loader: Optional[EnvironmentConfigLoader] = None):
        """Initialize with optional environment config loader."""
        self.env_config = env_config_loader or get_env_config()

    def get_keepass_filename(self, yaml_fallback: str) -> str:
        """Get KeePass filename with environment variable override."""
        return self.env_config.get_env_var("KEEPASS_DB_PATH", str, yaml_fallback)

    def get_keepass_keyfile(self, yaml_fallback: str) -> str:
        """Get KeePass keyfile with environment variable override."""
        return self.env_config.get_env_var("KEEPASS_KEY_PATH", str, yaml_fallback)

    def get_logging_config_path(self, yaml_fallback: str = "logging_config.yaml") -> str:
        """Get logging configuration path with environment variable override."""
        return self.env_config.get_env_var("LOGGING_CONFIG_PATH", str, yaml_fallback)

    def get_database_host(self, yaml_fallback: str = "localhost") -> str:
        """Get database host with environment variable override."""
        return self.env_config.get_env_var("DB_HOST", str, yaml_fallback)

    def get_database_port_rw(self, yaml_fallback: int = 5432) -> int:
        """Get database RW port with environment variable override."""
        return self.env_config.get_env_var("DB_PORT_RW", int, yaml_fallback)

    def get_database_port_ro(self, yaml_fallback: int = 5433) -> int:
        """Get database RO port with environment variable override."""
        return self.env_config.get_env_var("DB_PORT_RO", int, yaml_fallback)

    def get_database_name(self, yaml_fallback: str = "alembic_migration") -> str:
        """Get database name with environment variable override."""
        return self.env_config.get_env_var("DB_NAME", str, yaml_fallback)

    def get_default_schema(self, yaml_fallback: str = "public") -> str:
        """Get default schema with environment variable override."""
        return self.env_config.get_env_var("DB_DEFAULT_SCHEMA", str, yaml_fallback)


# Global hybrid config provider instance
hybrid_config = HybridConfigProvider()
