import asyncio
import logging
import time
from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any

class CircuitState(Enum):
    CLOSED = "closed"  # Normal operation
    OPEN = "open"  # Circuit breaker tripped, blocking requests
    HALF_OPEN = "half_open"  # Testing if service recovered


class ErrorType(Enum):
    """Classification of different error types for appropriate handling."""
    RATE_LIMIT = "rate_limit"  # Rate limiting - wait and resume
    CONNECTION_POOL = "connection_pool"  # Connection pool exhaustion - retry with backoff
    NETWORK = "network"  # Network/connectivity issues - retry with increased attempts
    SERVICE = "service"  # Service errors - standard circuit breaker logic
    UNRECOVERABLE = "unrecoverable"  # Unrecoverable errors - graceful shutdown


class SimplifiedCircuitBreaker:
    """
    Simplified circuit breaker using asyncio.Condition for coordination.
    All threads wait on a single condition that gets notified when backoffs clear.
    """

    def __init__(self, config=None, logger=None):
        self.config = config or self._default_config()
        self.logger = logger or logging.getLogger(__name__)

        # Circuit breaker state
        self._state = CircuitState.CLOSED
        self._failure_count = 0
        self._last_failure_time = 0
        self._half_open_calls = 0

        # Error tracking
        self._consecutive_rate_limits = 0
        self._connection_pool_errors = 0
        self._network_errors = 0
        self._network_retry_multiplier = 1.0

        # Backoff timers - when each type of backoff expires
        self._rate_limit_until = 0
        self._connection_pool_until = 0
        self._network_until = 0
        self._global_warning_until = 0

        # Activity tracking
        self._active_requests = 0
        self._active_connections = 0

        # Single condition for all coordination
        self._condition = asyncio.Condition()

        # Background task for cleanup
        self._cleanup_task = None
        self._shutdown_event = asyncio.Event()

        self._start_background_cleanup()

    def _default_config(self):
        """Default configuration with environment variable support"""
        try:
            from dags.data_pipeline.utils.env_config import get_env_config
            env_config = get_env_config()
            cb_config = env_config.get_circuit_breaker_config()

            class Config:
                failure_threshold = cb_config.failure_threshold
                recovery_timeout = cb_config.recovery_timeout
                half_open_max_calls = cb_config.half_open_max_calls
                max_consecutive_rate_limits = cb_config.max_consecutive_rate_limits
                connection_pool_retry_multiplier = cb_config.connection_pool_retry_multiplier
                network_error_retry_multiplier = cb_config.network_error_retry_multiplier
                max_concurrent_connections = cb_config.max_concurrent_connections
                health_check_interval = cb_config.health_check_interval

        except ImportError:
            # Fallback to hardcoded values if environment config is not available
            class Config:
                failure_threshold = 5
                recovery_timeout = 30.0
                half_open_max_calls = 3
                max_consecutive_rate_limits = 3
                connection_pool_retry_multiplier = 2.0
                network_error_retry_multiplier = 1.5
                max_concurrent_connections = 50
                health_check_interval = 10.0

        return Config()

    def _start_background_cleanup(self):
        """Start background task to clean up expired backoffs"""
        if self._cleanup_task is None or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._background_cleanup())

    async def _background_cleanup(self):
        """Background task that notifies waiting threads when backoffs expire"""
        asyncio.current_task().set_name("SimplifiedCircuitBreaker._background_cleanup")
        try:
            while not self._shutdown_event.is_set():
                try:
                    await asyncio.wait_for(
                        asyncio.sleep(1.0),
                        timeout=0.1
                    )
                except asyncio.TimeoutError:
                    pass

                current_time = time.time()
                should_notify = False

                async with self._condition:
                    # Check if any backoffs have expired
                    if (current_time >= self._rate_limit_until and self._rate_limit_until > 0) or \
                            (current_time >= self._connection_pool_until and self._connection_pool_until > 0) or \
                            (current_time >= self._network_until and self._network_until > 0) or \
                            (current_time >= self._global_warning_until and self._global_warning_until > 0):
                        should_notify = True

                    # Check if circuit should transition from OPEN to HALF_OPEN
                    if (self._state == CircuitState.OPEN and
                            current_time - self._last_failure_time > self.config.recovery_timeout):
                        self._state = CircuitState.HALF_OPEN
                        self._half_open_calls = 0
                        should_notify = True
                        self.logger.debug("Circuit transitioned to HALF_OPEN")

                if should_notify:
                    async with self._condition:
                        self._condition.notify_all()

        except asyncio.CancelledError:
            self.logger.debug("Background cleanup cancelled")
        except Exception as e:
            self.logger.error(f"Background cleanup error: {e}")

    def classify_error(self, error: Exception) -> ErrorType:
        """Classify error type for appropriate handling"""
        error_str = str(error).lower()
        error_type = type(error).__name__.lower()

        if "rate limit" in error_str or "429" in error_str or "retry-after" in error_str:
            return ErrorType.RATE_LIMIT

        if any(keyword in error_str for keyword in [
            "connection pool", "too many connections", "pool exhausted"
        ]):
            return ErrorType.CONNECTION_POOL

        if any(keyword in error_str for keyword in [
            "connection timeout", "connection reset", "connection refused",
            "network", "dns", "ssl", "certificate"
        ]):
            return ErrorType.NETWORK

        return ErrorType.SERVICE

    def _can_execute_unlocked(self) -> bool:
        """Internal version - assumes caller holds the lock"""
        asyncio.current_task().set_name("SimplifiedCircuitBreaker._can_execute_unlocked")
        current_time = time.time()
        # Check circuit state
        if self._state == CircuitState.OPEN:
            return False
        if self._state == CircuitState.HALF_OPEN:
            if self._half_open_calls >= self.config.half_open_max_calls:
                return False
        # Check all backoff conditions
        if (current_time < self._rate_limit_until or
                current_time < self._connection_pool_until or
                current_time < self._network_until or
                current_time < self._global_warning_until):
            return False
        return True

    async def can_execute(self) -> bool:
        """Check if request can be executed"""
        asyncio.current_task().set_name("SimplifiedCircuitBreaker.can_execute")
        async with self._condition:
            return self._can_execute_unlocked()


    async def wait_for_clearance(self, timeout: Optional[float] = None):
        """
        Wait until circuit breaker allows execution.
        All threads wait on the same condition - much simpler than multiple events.
        """
        asyncio.current_task().set_name("SimplifiedCircuitBreaker.wait_for_clearance")
        start_time = time.time()
        last_log_time = start_time  # Initialize last_log_time

        max_wait_log_interval = 30  # seconds
        wake_interval = 1.0  # seconds
        self.logger.debug(f"wait_for_clearance: Waiting for circuit clearance. lock status {self._condition.locked()}")

        async with self._condition:
            while True:
                if self._can_execute_unlocked():
                    return True

                # Check timeout
                elapsed = time.time() - start_time
                if timeout and elapsed >= timeout:
                    if self.logger:
                        self.logger.debug(f"wait_for_clearance timeout after {elapsed:.1f} seconds")
                    return False

                # Periodic logging if stuck
                current_time = time.time()
                if current_time - last_log_time >= max_wait_log_interval:
                    if self.logger:
                        self.logger.warning("Still waiting for circuit to clear after %.1f seconds", elapsed)
                    last_log_time = current_time

                # Sleep for a small interval or remaining timeout slice
                remaining = timeout - elapsed if timeout else None
                sleep_time = min(wake_interval, remaining) if remaining is not None else wake_interval

                # Wait for notification (when backoffs expire or circuit changes)
                try:
                    await asyncio.wait_for(
                        self._condition.wait(),
                        timeout=sleep_time
                    )
                except asyncio.TimeoutError:
                    # Continue the loop - this is expected behavior for periodic checks
                    continue

    async def record_success(self):
        """Record successful request"""
        asyncio.current_task().set_name("SimplifiedCircuitBreaker.record_success")
        async with self._condition:
            asyncio.current_task().set_name("SimplifiedCircuitBreaker.record_success._condition")
            if self._state == CircuitState.HALF_OPEN:
                self._failure_count = 0
                self._consecutive_rate_limits = 0
                self._state = CircuitState.CLOSED
                self.logger.debug("Circuit recovered to CLOSED")
            elif self._state == CircuitState.OPEN:
                # Allow immediate recovery on success
                self._failure_count = 0
                self._consecutive_rate_limits = 0
                self._state = CircuitState.CLOSED
                self.logger.debug("Circuit recovered from OPEN to CLOSED")
            else:  # CLOSED
                self._failure_count = max(0, self._failure_count - 1)
                self._consecutive_rate_limits = 0

            # Reset error counters and clear backoffs on success
            self._connection_pool_errors = max(0, self._connection_pool_errors - 1)
            self._network_errors = max(0, self._network_errors - 1)
            self._network_retry_multiplier = max(1, self._network_retry_multiplier - 0.5)

            # Clear all backoffs on success
            self._rate_limit_until = 0
            self._connection_pool_until = 0
            self._network_until = 0
            self._global_warning_until = 0

            # Notify all waiting threads
            self._condition.notify_all()

    async def record_error(self, error: Exception, retry_delay: float = 0):
        """Record an error with appropriate backoff"""
        asyncio.current_task().set_name("SimplifiedCircuitBreaker.record_error")
        error_type = self.classify_error(error)
        current_time = time.time()
        self.logger.debug(f"record_error: recording error {error_type}")

        async with self._condition:
            if error_type == ErrorType.RATE_LIMIT:
                self._handle_rate_limit_error(retry_delay, current_time)
            elif error_type == ErrorType.CONNECTION_POOL:
                self._handle_connection_pool_error(current_time)
            elif error_type == ErrorType.NETWORK:
                self._handle_network_error(current_time)
            else:  # SERVICE error
                self._handle_service_error(current_time)
        self.logger.debug("record_error: exiting")

    def _handle_rate_limit_error(self, retry_delay: float, current_time: float):
        """Handle rate limit - set backoff and possibly open circuit"""
        backoff_seconds = retry_delay / 1000 if retry_delay > 0 else 2.0
        self._rate_limit_until = current_time + backoff_seconds
        self._consecutive_rate_limits += 1

        if self._consecutive_rate_limits >= self.config.max_consecutive_rate_limits:
            self._state = CircuitState.OPEN
            self._last_failure_time = current_time
            self.logger.warning(f"Circuit OPEN due to {self._consecutive_rate_limits} consecutive rate limits")
        else:
            self.logger.debug(
                f"Rate limit backoff until {datetime.fromtimestamp(self._rate_limit_until).strftime('%H:%M:%S')}")

    def _handle_connection_pool_error(self, current_time: float):
        """Handle connection pool exhaustion"""
        self._connection_pool_errors += 1
        backoff_delay = min(30.0, self.config.connection_pool_retry_multiplier ** self._connection_pool_errors)
        self._connection_pool_until = current_time + backoff_delay

        self.logger.warning(f"Connection pool error #{self._connection_pool_errors}. Backoff for {backoff_delay:.1f}s")

    def _handle_network_error(self, current_time: float):
        """Handle network errors with exponential backoff"""
        self._network_errors += 1
        self._network_retry_multiplier = min(4,
                                             self._network_retry_multiplier * self.config.network_error_retry_multiplier)
        backoff_delay = min(60.0, 5.0 * self._network_retry_multiplier)
        self._network_until = current_time + backoff_delay

        self.logger.warning(f"Network error #{self._network_errors}. Backoff for {backoff_delay:.1f}s")

    def _handle_service_error(self, current_time: float):
        """Handle service errors - standard circuit breaker logic"""
        self._failure_count += 1
        self._consecutive_rate_limits = 0  # Reset since we got a response

        if self._failure_count >= self.config.failure_threshold:
            self._state = CircuitState.OPEN
            self._last_failure_time = current_time
            self.logger.warning(f"Circuit OPEN due to {self._failure_count} service failures")

    async def record_rate_limit_warning(self, warning_duration: float = 2.0):
        """Record global rate limit warning - all threads pause"""
        asyncio.current_task().set_name("record_rate_limit_warning")
        current_time = time.time()

        self.logger.debug(f"record_rate_limit_warning: lock status {self._condition.locked()}")

        async with self._condition:
            if current_time < self._global_warning_until:
                # Warning already active
                return

            self._global_warning_until = current_time + warning_duration
            self.logger.warning(f"Global rate limit warning. All threads pause for {warning_duration}s")
        self.logger.debug(f"record_rate_limit_warning: lock status {self._condition.locked()} exiting!!!")

    async def enter_request(self):
        """Enter request context"""
        asyncio.current_task().set_name("enter_request")
        async with self._condition:
            self._active_requests += 1
            self._active_connections += 1
            if self._state == CircuitState.HALF_OPEN:
                self._half_open_calls += 1

    async def exit_request(self):
        """Exit request context"""
        asyncio.current_task().set_name("exit_request")
        async with self._condition:
            self._active_requests = max(0, self._active_requests - 1)
            self._active_connections = max(0, self._active_connections - 1)

    async def get_status(self) -> Dict[str, Any]:
        """Get circuit breaker status"""
        asyncio.current_task().set_name("get_status")
        async with self._condition:
            current_time = time.time()
            return {
                "state": self._state.value,
                "failure_count": self._failure_count,
                "consecutive_rate_limits": self._consecutive_rate_limits,
                "active_requests": self._active_requests,
                "active_connections": self._active_connections,
                "backoffs": {
                    "rate_limit_until": self._rate_limit_until,
                    "connection_pool_until": self._connection_pool_until,
                    "network_until": self._network_until,
                    "global_warning_until": self._global_warning_until,
                    "rate_limit_active": current_time < self._rate_limit_until,
                    "connection_pool_active": current_time < self._connection_pool_until,
                    "network_active": current_time < self._network_until,
                    "global_warning_active": current_time < self._global_warning_until,
                },
                "current_time": current_time
            }

    async def cleanup(self):
        """Cleanup resources"""
        asyncio.current_task().set_name("cleanup")
        self.logger.debug("Cleaning up circuit breaker")

        # Signal shutdown
        self._shutdown_event.set()

        # Cancel background task
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass

        # Wake up all waiting threads
        async with self._condition:
            self._condition.notify_all()

        self.logger.debug("Circuit breaker cleanup completed")

    async def shutdown(self):
        """Call this to gracefully shutdown the circuit breaker"""
        self._shutdown_event.set()
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass


