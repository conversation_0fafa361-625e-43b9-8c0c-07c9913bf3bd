logging:
  version: 1
  disable_existing_loggers: false

  # Masked the filters as it causing slowness
  formatters:
    custom:
      (): dags.data_pipeline.custom_logger.CustomFormatter
      format: '%(asctime)s.%(msecs)03d %(levelname)-8s [%(correlation_id)s] [%(task_id)s:%(coroutine_name)s] %(funcName)-25s %(task_name)-25s %(lineno_str)s | %(message)s'
      datefmt: "%Y-%m-%dT%H:%M:%S"
    detailed:
      (): dags.data_pipeline.custom_logger.CustomFormatter
      format: '%(asctime)s.%(msecs)03d %(name)-10s %(levelname)-8s [%(correlation_id)s] [%(task_id)s:%(coroutine_name)s] %(funcName)-31s %(task_name)-50s %(lineno)05d ~~ %(message)s'
      datefmt: "%Y-%m-%dT%H:%M:%S"
    brief:
      format: '%(asctime)s %(levelname)s [%(correlation_id)s] %(message)s'
      datefmt: '%Y-%m-%d %H:%M:%S'
    database:
      (): dags.data_pipeline.custom_logger.CustomFormatter
      format: '%(asctime)s.%(msecs)03d %(levelname)-8s [%(correlation_id)s] [%(task_id)s:%(coroutine_name)s] %(funcName)-25s %(lineno_str)s | %(message)s'
      datefmt: "%Y-%m-%dT%H:%M:%S"
  filters:
    mask_authorization:
      (): dags.data_pipeline.custom_logger.MaskAuthorizationFilter
    enhanced_task_name_filter:
      (): dags.data_pipeline.custom_logger.EnhancedTaskNameFilter
    unified_unicode_handler:
      (): dags.data_pipeline.custom_logger.UnifiedUnicodeHandler
    # Backward compatibility
    task_name_filter:
      (): dags.data_pipeline.custom_logger.EnhancedTaskNameFilter

  handlers:
    warnings:
      class: logging.StreamHandler
      level: WARNING
      formatter: custom

    custom_console:
      class: dags.data_pipeline.custom_logger.CustomConsoleHandler
      level: DEBUG
      formatter: custom
      rich_tracebacks: true
      show_time: true

    console:
      class: rich.logging.RichHandler
      level: DEBUG
      formatter: custom
      rich_tracebacks: true
      show_time: true

    file:
      class: logging.handlers.TimedRotatingFileHandler
      level: DEBUG
      formatter: custom
      filename: ${LOG_FILE_PATH}/main.log
      when: W0
      interval: 1
      backupCount: 4
      encoding: utf-8
      filters:
        - enhanced_task_name_filter

    profiled_file:
      class: logging.handlers.TimedRotatingFileHandler
      level: DEBUG
      formatter: custom
      filename: ${LOG_FILE_PATH}/profiled.log
      when: W0
      interval: 1
      backupCount: 4
      filters:
        - enhanced_task_name_filter

    # Database handler is configured programmatically via containers
    # to avoid circular dependencies

    queue_listener:
      class: logging.handlers.QueueHandler
      listener: dags.data_pipeline.custom_logger.AutoStartQueueListener
      queue:
        (): queue.Queue
        maxsize: 0
      level: DEBUG
      handlers:
        - custom_console
        - file
      filters:
        - enhanced_task_name_filter
  #        - mask_authorization

  loggers:
    profiled_logger:
      level: DEBUG
      handlers:
        - profiled_file
      propagate: no

    debugging_monitor:
      level: DEBUG
      handlers:
        - file
        - console
      propagate: no

    debug_utils:
      level: DEBUG
      handlers:
        - file
        - console
      propagate: no

    development:
      level: DEBUG
      handlers:
        - queue_listener
      propagate: no

    staging:
      level: INFO
      handlers: [ console, file ]
      propagate: no

    production:
      level: WARNING
      handlers: [ queue_listener ]
      propagate: no

    root:
      handlers: [ file ]
      level: INFO

KeePass:
  KEYPASS_DB: ${DATABASE_PATH}
  KEYPASS_KEY_FILE: ${MASTER_KEYFILE}
  JIRA_ENTRY: corecard Jira
  #  JIRA_ENTRY: personal Jira
  PG_RW: JIRA_RW
  PG_RO: JIRA_RO
Database:
  SCHEMA: public

KeePassDir:
  DB_NAME: ${KEEPASS_HOME}/Database.kdbx
  KEY_FILE: ${KEEPASS_HOME}/Database.key

Environment:
  Env: development

QueueConfig:
  queue_issues: 1000
  queue_stats: -1

schema_name: plat

schema:
  - plat
  - plp
  - cpp