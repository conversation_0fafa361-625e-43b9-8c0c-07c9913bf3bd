import asyncio
import functools
import json
import sys
import time
import traceback
from datetime import datetime, date, timezone
from decimal import Decimal
from itertools import islice
from logging import Logger
from typing import Type, Any, Dict, List

import numpy as np
import pandas as pd

from sqlalchemy.exc import IntegrityError, PendingRollbackError
from sqlalchemy.pool import base
from asyncpg import DeadlockDetectedError
from asyncpg.exceptions import ForeignKeyViolationError as AsyncpgForeignKeyViolationError
from psycopg2.errors import DeadlockDetected, ForeignKeyViolation as Psycopg2ForeignKeyViolation
from sqlalchemy import inspect, and_, UniqueConstraint, text
from sqlalchemy.dialects.postgresql import insert, dialect

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session
from sqlalchemy.sql.elements import BinaryExpression, BooleanClauseList
from sqlalchemy.testing.exclusions import succeeds_if

from dags.data_pipeline.dataframe_utils.dataframe_debugger import quick_save_async
from dags.data_pipeline.dbmodels.base import Base


def _extract_key_info_for_retry(df: pd.DataFrame, model_name: str = None) -> str:
    """
    Extract key information from DataFrame for retry logging.

    Args:
        df: DataFrame containing the data
        model_name: Name of the model being processed

    Returns:
        String containing key information for retry logging
    """
    if df.empty:
        return "RETRY_NEEDED: Empty DataFrame"

    # Try to find key columns in order of preference
    key_columns = ['key', 'issue_key', 'id', 'issue_id']
    found_key_column = None

    for col in key_columns:
        if col in df.columns:
            found_key_column = col
            break

    if found_key_column:
        # Get unique values from the key column, limit to first 10 for readability
        key_values = df[found_key_column].dropna().unique()[:10]
        key_list = ', '.join(str(k) for k in key_values)
        if len(df[found_key_column].dropna().unique()) > 10:
            key_list += f" ... (+{len(df[found_key_column].dropna().unique()) - 10} more)"

        return f"RETRY_NEEDED: {model_name or 'Unknown'} - {found_key_column}: [{key_list}] - {len(df)} records"
    else:
        # Fallback: show available columns and record count
        return f"RETRY_NEEDED: {model_name or 'Unknown'} - No key column found, columns: {list(df.columns)[:5]} - {len(df)} records"



async def get_pg_stat_activity(
        session: AsyncSession, my_logger: Logger|None=None
) -> list[dict]:
    try:
        query = text("""
            SELECT pid, usename, query, state, wait_event_type, wait_event, now() - query_start AS duration
            FROM pg_stat_activity
            WHERE state != 'idle'
            ORDER BY duration DESC
            LIMIT 10;
        """)
        result = await session.execute(query)
        return [dict(row._mapping) for row in result.fetchall()]
    except Exception as e:
        exc_type, exc_value, exc_tb = sys.exc_info()
        line_num = exc_tb.tb_lineno
        tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
        if my_logger:
            my_logger.exception(
                f"Line {line_num} Error encountered: {''.join(tb.format_exception_only())}",
                exc_info=True
            )
        else:
            print("get_pg_stat_activity ERROR:\n", traceback.format_exc())
        return []


async def get_blocking_info(session: AsyncSession) -> list[dict]:
    query = text("""
    SELECT
      blocked_locks.pid         AS blocked_pid,
    blocked_activity.query    AS blocked_query,
    blocked_locks.locktype    AS blocked_locktype,
    blocked_locks.mode        AS blocked_mode,
    blocked_locks.relation    AS blocked_relid,
    c_blocked.relname         AS blocked_relation,

    blocking_locks.pid        AS blocking_pid,
    blocking_activity.query   AS blocking_query,
    blocking_locks.locktype   AS blocking_locktype,
    blocking_locks.mode       AS blocking_mode,
    blocking_locks.relation   AS blocking_relid,
    c_blocking.relname        AS blocking_relation
    FROM pg_locks blocked_locks
    JOIN pg_stat_activity blocked_activity
      ON blocked_activity.pid = blocked_locks.pid
    JOIN pg_locks blocking_locks
      ON blocking_locks.locktype = blocked_locks.locktype
     AND blocking_locks.database IS NOT DISTINCT FROM blocked_locks.database
     AND blocking_locks.relation IS NOT DISTINCT FROM blocked_locks.relation
     AND blocking_locks.page IS NOT DISTINCT FROM blocked_locks.page
     AND blocking_locks.tuple IS NOT DISTINCT FROM blocked_locks.tuple
     AND blocking_locks.virtualxid IS NOT DISTINCT FROM blocked_locks.virtualxid
     AND blocking_locks.transactionid IS NOT DISTINCT FROM blocked_locks.transactionid
     AND blocking_locks.classid IS NOT DISTINCT FROM blocked_locks.classid
     AND blocking_locks.objid IS NOT DISTINCT FROM blocked_locks.objid
     AND blocking_locks.objsubid IS NOT DISTINCT FROM blocked_locks.objsubid
     AND blocking_locks.pid != blocked_locks.pid
    JOIN pg_stat_activity blocking_activity
      ON blocking_activity.pid = blocking_locks.pid;
    """)
    result = await session.execute(query)
    return [dict(row._mapping) for row in result.fetchall()]


def _format_array_for_postgres(array_value):
    """Convert Python list to PostgreSQL array format."""
    if not array_value:
        return "ARRAY[]"

    def format_element(val):
        if val is None:
            return "NULL"
        elif isinstance(val, bool):
            return "true" if val else "false"
        elif isinstance(val, str):
            escaped = val.replace("'", "''")
            return f"'{escaped}'"
        elif isinstance(val, (int, float)):
            return str(val)
        elif isinstance(val, Decimal):
            return str(val)
        elif isinstance(val, (date, datetime)):
            return f"'{val.isoformat()}'"
        elif isinstance(val, dict):
            escaped_json = json.dumps(val).replace("'", "''")
            return f"'{escaped_json}'::jsonb"
        elif isinstance(val, list):
            # Handle nested array
            return _format_array_for_postgres(val)
        else:
            escaped = str(val).replace("'", "''")
            return f"'{escaped}'"

    # If it's a 2D array or more deeply nested, treat outer layer recursively
    if any(isinstance(el, list) for el in array_value):
        inner = ", ".join(_format_array_for_postgres(el) for el in array_value)
        return f"ARRAY[{inner}]"

    formatted = ", ".join(format_element(item) for item in array_value)
    return f"ARRAY[{formatted}]"



def _format_value_for_sql(value: Any) -> str:
    """
    Format a Python value as a SQL literal string for PostgreSQL.

    Args:
        value: The value to format

    Returns:
        SQL literal string representation of the value
    """
    if value is None:
        return "NULL"
    elif isinstance(value, bool):
        return "true" if value else "false"
    elif isinstance(value, (int, float, Decimal)):
        return str(value)
    elif isinstance(value, str):
        # Escape single quotes and wrap in quotes
        escaped = value.replace("'", "''")
        return f"'{escaped}'"
    elif isinstance(value, datetime):
        if value.tzinfo is not None:
            # Convert to UTC and format as timestamptz
            value_utc = value.astimezone(timezone.utc)
            return f"'{value_utc.isoformat()}'::timestamptz"
        else:
            # Naive datetime - format as timestamp
            return f"'{value.isoformat()}'::timestamp"
    elif isinstance(value, date):
        return f"'{value.isoformat()}'::date"
    elif isinstance(value, pd.Timestamp):
        # Convert pandas Timestamp to Python datetime
        return _format_value_for_sql(value.to_pydatetime())
    elif isinstance(value, dict):
        # JSON/JSONB data
        json_str = json.dumps(value).replace("'", "''")
        return f"'{json_str}'::jsonb"
    elif isinstance(value, list):
        # Array data
        return _format_array_for_postgres(value)
    elif pd.isnull(value):
        # Handle pandas NaT/NaN values
        return "NULL"
    elif hasattr(value, 'to_pydatetime') and callable(getattr(value, 'to_pydatetime')):
        # Convert pandas datetime-like objects to Python datetime
        try:
            return _format_value_for_sql(value.to_pydatetime())
        except Exception:
            # Fallback to string representation
            escaped = str(value).replace("'", "''")
            return f"'{escaped}'"
    else:
        # Fallback - convert to string and escape
        return f"'{str(value).replace(chr(39), chr(39)+chr(39))}'"


def compile_upsert_with_values(stmt, engine, sample_batch: List[Dict[str, Any]], max_rows: int = 5) -> str:
    """
    Compiles the upsert statement with actual values for EXPLAIN debugging.

    Args:
        stmt: SQLAlchemy insert statement
        engine: Database engine
        sample_batch: List of dictionaries containing row data
        max_rows: Maximum number of rows to include in the compiled statement

    Returns:
        SQL statement string with literal values, or error message
    """
    try:
        if not sample_batch:
            return "No data in sample_batch"

        # Limit the number of rows to avoid overly long SQL
        rows_to_process = sample_batch[:max_rows]

        # Process each row to handle different data types
        processed_rows = []
        for row_idx, row in enumerate(rows_to_process):
            processed_row = {}
            for key, value in row.items():
                try:
                    # Use our robust value formatter
                    processed_row[key] = _format_value_for_sql(value)
                except Exception as format_err:
                    # If formatting fails, use a safe fallback
                    processed_row[key] = f"'<FORMAT_ERROR: {str(format_err)}'"
            processed_rows.append(processed_row)

        # Build the VALUES clause manually since literal_binds has issues with complex types
        table_name = stmt.table.name
        if hasattr(stmt.table, 'schema') and stmt.table.schema:
            table_name = f"{stmt.table.schema}.{table_name}"

        # Get column names from the first row
        if not processed_rows:
            return "No processed rows available"

        columns = list(processed_rows[0].keys())
        column_list = ", ".join(f'"{col}"' for col in columns)

        # Build VALUES clauses
        values_clauses = []
        for row in processed_rows:
            values = [row.get(col, "NULL") for col in columns]
            values_clauses.append(f"({', '.join(values)})")

        values_clause = ",\n    ".join(values_clauses)

        # Get the base INSERT statement structure
        base_sql = str(stmt.compile(dialect=dialect()))

        # Extract the conflict handling part if it exists
        conflict_part = ""
        if "ON CONFLICT" in base_sql:
            conflict_start = base_sql.find("ON CONFLICT")
            conflict_part = base_sql[conflict_start:]

        # Construct the final SQL
        final_sql = f"""INSERT INTO {table_name} ({column_list})
        VALUES
        {values_clause}
        """

        if conflict_part:
            final_sql += f"\n{conflict_part}"

        return final_sql

    except Exception as e:
        # Provide detailed error information for debugging
        error_details = [f"Main error: {str(e)}", f"Error type: {type(e).__name__}"]

        if sample_batch:
            error_details.append(f"Sample batch size: {len(sample_batch)}")
            if sample_batch[0]:
                error_details.append(f"First row keys: {list(sample_batch[0].keys())}")
                # Show problematic values
                for key, value in list(sample_batch[0].items())[:5]:  # First 5 items
                    error_details.append(f"  {key}: {type(value).__name__} = {repr(value)[:100]}")

        return f"Failed to compile statement:\n" + "\n".join(error_details)



def chunk_rows(rows, batch_size):
    iterator = iter(rows)
    while chunk := list(islice(iterator, batch_size)):
        yield chunk


def handle_foreignkeys_constraints(row, foreign_keys):
    for c_name, c_value in foreign_keys.items():
        foreign_obj = row.pop(c_value.table.name, None)
        if foreign_obj:
            row[c_name] = getattr(foreign_obj, c_value.name)
        else:
            row[c_name] = row.get(c_name)
    return row


def validate_unique_constraints(row, unique_constraints, seen, conflict_keys):
    """
    Validate and track unique constraints for a row.

    Parameters:
        row: Dictionary representing a row of data.
        unique_constraints: List of unique constraints for the table.
        seen: Set of already processed unique keys.
        conflict_keys: Set to track rows with unique constraint conflicts.

    Returns:
        Processed row or None if invalid.
    """
    for const in unique_constraints:
        unique_key = tuple((col.name, row[col.name]) for col in const.columns)
        if unique_key in seen:
            conflict_keys.add(unique_key)
        seen.add(unique_key)
    return row


def smart_retry(
        min_wait=5, max_wait=10, max_delay=60, max_retries=5,
        my_logger: Logger | None=None
):
    # Validate input arguments
    if min_wait <= 0 or max_wait <= 0 or max_delay <= 0:
        raise ValueError("Wait times and max_delay must be positive numbers.")
    if min_wait > max_wait:
        raise ValueError("min_wait cannot be greater than max_wait.")
    if max_retries <= 0:
        raise ValueError("max_retries must be a positive integer.")
    if max_delay < min_wait:
        raise ValueError("max_delay must be greater than or equal to min_wait.")

    def decorator(func_local):
        @functools.wraps(func_local)
        def wrapper(*args, **kwargs):
            start_time = time.time()

            async def async_retry_logic():
                retries = 0
                while retries < max_retries:
                    try:
                        return await func_local(*args, **kwargs)  # Await for async functions
                    except DeadlockDetectedError as e:
                        retries += 1
                        elapsed_time = (time.time() - start_time)
                        if elapsed_time >= max_delay:
                            raise TimeoutError(
                                f"Retry limit of {max_delay} seconds reached after {retries} attempts."
                            ) from e
                        wait_time = min(max_wait, min_wait * (2 ** retries))
                        if isinstance(my_logger, Logger):
                            my_logger.warning(
                                f"Async retry {retries}/{max_retries} in {wait_time:.2f} seconds due to: {e}"
                            )
                        await asyncio.sleep(wait_time)
                raise TimeoutError("Max retries reached without success.")

            def sync_retry_logic():
                retries = 0
                while retries < max_retries:
                    try:
                        return func_local(*args, **kwargs)  # Direct call for sync functions
                    except DeadlockDetected as e:
                        retries += 1
                        elapsed_time = (time.time() - start_time)
                        if elapsed_time >= max_delay:
                            raise TimeoutError(
                                f"Retry limit of {max_delay} seconds reached after {retries} attempts."
                            ) from e
                        wait_time = min(max_wait, min_wait * (2 ** retries))
                        if isinstance(my_logger, Logger):
                            my_logger.warning(
                                f"Sync retry {retries}/{max_retries} in {wait_time:.2f} seconds due to: {e}"
                            )
                        time.sleep(wait_time)
                raise TimeoutError("Max retries reached without success.")

            # Determine whether the function is async or sync
            if asyncio.iscoroutinefunction(func_local):
                return async_retry_logic()
            else:
                return sync_retry_logic()

        return wrapper

    return decorator

def build_update_values(model, excluded, partial_update: bool = True):
    """
    Builds a dictionary of columns to update with optional selective updating.

    Args:
        model: The SQLAlchemy model class.
        excluded: A dictionary or alias of excluded values from `insert().excluded`.
        partial_update: If True, only update columns when values differ from existing ones;
                       if False, update all provided columns unconditionally.

    Returns:
        Dictionary mapping column names to update expressions.

    Note:
        When partial_update=True, generates conditional update expressions using PostgreSQL's
        IS DISTINCT FROM operator for proper NULL handling. For JSON/JSONB columns, converts
        to text for comparison since JSON types don't support direct equality operators.
        This reduces unnecessary updates and can improve performance for tables with many columns.
    """
    from sqlalchemy import case, func, cast, Text
    from sqlalchemy.dialects.postgresql import JSON, JSONB

    values = {}
    mapper = inspect(model)
    table = model.__table__

    for column in mapper.columns:
        col_name = column.key
        if col_name in excluded:  # Only consider provided fields
            if partial_update:
                # Only update column if the new value is different from the existing one
                table_column = getattr(table.c, col_name)
                excluded_value = excluded[col_name]

                # Handle JSON/JSONB columns specially since they don't support IS DISTINCT FROM directly
                if isinstance(column.type, (JSON, JSONB)):
                    # Convert both sides to text for comparison
                    # Use COALESCE to handle NULL values properly
                    table_column_text = func.coalesce(cast(table_column, Text), '')
                    excluded_value_text = func.coalesce(cast(excluded_value, Text), '')

                    # Compare as text strings
                    values[col_name] = case(
                        (table_column_text != excluded_value_text, excluded_value),
                        else_=table_column
                    )
                else:
                    # For non-JSON columns, use IS DISTINCT FROM for proper NULL handling
                    # PostgreSQL's IS DISTINCT FROM handles NULL comparisons correctly:
                    # - NULL IS DISTINCT FROM NULL -> FALSE
                    # - NULL IS DISTINCT FROM 'value' -> TRUE
                    # - 'value' IS DISTINCT FROM NULL -> TRUE
                    # - 'value1' IS DISTINCT FROM 'value2' -> TRUE/FALSE based on equality
                    values[col_name] = case(
                        (func.coalesce(table_column.is_distinct_from(excluded_value), True), excluded_value),
                        else_=table_column
                    )
            else:
                # Always update provided columns (original behavior)
                values[col_name] = excluded[col_name]

    return values


@smart_retry(min_wait=2, max_wait=5, max_retries=3)
def upsert(
        session: Session,
        model: Type[Base],
        rows: pd.DataFrame,
        no_update_cols: tuple[str, ...] = (),
        on_conflict_update: bool = True,
        conflict_condition: list | BinaryExpression | BooleanClauseList | None = None,
        batch_size: int | None = None,
        selective_update: bool = False,
        my_logger: Logger|None = None,
) -> None:
    """
    Perform an upsert (insert or update on conflict) operation on the given SQLAlchemy model.

    Parameters:
        session: SQLAlchemy session.
        model: SQLAlchemy model class.
        rows: DataFrame containing rows to upsert.
        no_update_cols: Columns that should not be updated on conflict.
        on_conflict_update: Whether to perform updates on conflict.
        conflict_condition: Additional conditions for conflict resolution.
        batch_size: Maximum number of rows per batch.
        selective_update: If True, only update columns when values differ from existing ones.
                         If False, update all columns (original behavior). Default: False.
        my_logger: Logger instance for error logging.

    Returns:
        None
    """
    # Source: https://stackoverflow.com/questions/7165998/how-to-do-an-upsert-with-sqlalchemy/44395983#44395983
    # https://gist.github.com/bhtucker/c40578a2fb3ca50b324e42ef9dce58e1
    if rows.shape[0] == 0:
        return

    rows = rows.copy()
    rows.replace({np.nan: None}, inplace=True)
    table = model.__table__
    stmt = insert(table)
    primary_keys = [key.name for key in inspect(table).primary_key]

    if selective_update and on_conflict_update:
        # Use enhanced build_update_values for selective updates
        update_cols = [
            c.name for c in table.c
            if c not in list(table.primary_key.columns) and c.name not in no_update_cols
        ]
        # Create a dictionary-like object that supports column access for build_update_values
        excluded_dict = {col: getattr(stmt.excluded, col) for col in update_cols}
        update_dict = build_update_values(model, excluded_dict, partial_update=True)
    else:
        # Original behavior: update all columns
        update_cols = [
            c.name for c in table.c
            if c not in list(table.primary_key.columns) and c.name not in no_update_cols
        ]
        update_dict = {k: getattr(stmt.excluded, k) for k in update_cols}
    # index_where = and_(*conflict_condition) if conflict_condition else None
    if conflict_condition is not None:
        if isinstance(conflict_condition, list):
            index_where = and_(
                *(getattr(model, col) < getattr(stmt.excluded, col) for col in conflict_condition)
            )
        else:
            index_where = conflict_condition
    else:
        index_where = None

    if on_conflict_update:
        stmt = stmt.on_conflict_do_update(
            index_elements=primary_keys,
            set_=update_dict,
            # index_where=index_where,
            where=index_where,
        )
    else:
        stmt = stmt.on_conflict_do_nothing(index_elements=primary_keys)

    foreign_keys = {col.name: list(col.foreign_keys)[0].column for col in table.columns if col.foreign_keys}
    unique_constraints = [c for c in table.constraints if isinstance(c, UniqueConstraint)]


    seen = set()
    conflict_keys = set()
    processed_rows = []
    for row in rows.to_dict(orient="records"):
        row = handle_foreignkeys_constraints(row, foreign_keys)
        row = validate_unique_constraints(row, unique_constraints, seen, conflict_keys)
        if row:
            processed_rows.append(row)

    if not processed_rows:
        return

    # Calculate max rows per batch based on PostgresSQL parameter limit
    num_columns = len(processed_rows[0]) if processed_rows else 0
    max_params = 65535
    max_rows_per_batch = max_params // num_columns if num_columns else 1

    if batch_size is None:
        batch_size = max_rows_per_batch
    elif batch_size > max_rows_per_batch:
        batch_size = max_rows_per_batch

    try:
        for batch in chunk_rows(processed_rows, batch_size):
            session.execute(stmt, batch)

    except Exception as e:
        if isinstance(my_logger, Logger):
            my_logger.error(f"Upsert failed with error: {e}.", exc_info=True)
            exc_type, exc_value, exc_tb = sys.exc_info()
            line_num = exc_tb.tb_lineno
            tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
            my_logger.error(
                f"Line {line_num} Error encountered: {''.join(tb.format_exception_only())}",
                exc_info=True
            )
        raise e



async def upsert_async(
        session: AsyncSession,
        model: Type[Base],
        rows: pd.DataFrame,
        no_update_cols: tuple[str, ...] = (),
        on_conflict_update: bool = True,
        conflict_condition: list | BinaryExpression | BooleanClauseList | None = None,
        message_count: int = 0,
        batch_size: int | None = None,
        max_retries=5,
        retry_delay=1.0,
        selective_update: bool = True,
        my_logger: Logger|None=None,
) -> bool:
    """
    Perform an async upsert (insert or update on conflict) operation on the given SQLAlchemy model.

    Parameters:
        session: Async SQLAlchemy session.
        model: SQLAlchemy model class.
        rows: DataFrame containing rows to upsert.
        no_update_cols: Columns that should not be updated on conflict.
        on_conflict_update: Whether to perform updates on conflict.
        conflict_condition: Additional conditions for conflict resolution.
        message_count: Count of messages processed so far.
        batch_size: Maximum number of rows per batch.
        max_retries: Maximum number of retries for failed operations.
        retry_delay: Initial delay between retries (exponential backoff applied).
        selective_update: If True, only update columns when values differ from existing ones.
                         If False, update all columns (original behavior). Default: False.
        my_logger: Logger instance for error logging.

    Returns:
        bool: True if successful, False if failed.
    """
    if rows.shape[0] == 0:
        return True


    rows = rows.copy()
    rows.replace({np.nan: None}, inplace=True)
    table = model.__table__
    stmt = insert(table)
    primary_keys = [key.name for key in inspect(table).primary_key]

    if selective_update and on_conflict_update:
        # Use enhanced build_update_values for selective updates
        update_cols = [
            c.name for c in table.c
            if c not in list(table.primary_key.columns) and c.name not in no_update_cols
        ]
        # Create a dictionary-like object that supports column access for build_update_values
        excluded_dict = {col: getattr(stmt.excluded, col) for col in update_cols}
        update_dict = build_update_values(model, excluded_dict, partial_update=True)
    else:
        # Original behavior: update all columns
        update_cols = [
            c.name for c in table.c
            if c not in list(table.primary_key.columns) and c.name not in no_update_cols
        ]
        update_dict = {k: getattr(stmt.excluded, k) for k in update_cols}
    # index_where = and_(*conflict_condition) if conflict_condition else None

    if conflict_condition is not None:
        if isinstance(conflict_condition, list):
            index_where = and_(
                *(getattr(model, col) < getattr(stmt.excluded, col) for col in conflict_condition)
            )
        else:
            index_where = conflict_condition
    else:
        index_where = None

    if on_conflict_update:
        stmt = stmt.on_conflict_do_update(
            index_elements=primary_keys,
            set_=update_dict,
            where=index_where,
        )
    else:
        stmt = stmt.on_conflict_do_nothing(index_elements=primary_keys)

    foreign_keys = {col.name: list(col.foreign_keys)[0].column for col in table.columns if col.foreign_keys}
    unique_constraints = [c for c in table.constraints if isinstance(c, UniqueConstraint)]

    seen = set()
    conflict_keys = set()
    processed_rows = []
    for row in rows.to_dict(orient="records"):
        row = handle_foreignkeys_constraints(row, foreign_keys)
        row = validate_unique_constraints(row, unique_constraints, seen, conflict_keys)
        if row:
            processed_rows.append(row)

    if not processed_rows:
        return True

    # Calculate max rows per batch based on PostgreSQL parameter limit
    num_columns = len(processed_rows[0]) if processed_rows else 0
    max_params = 65535
    max_rows_per_batch = max_params // num_columns if num_columns else 1

    if batch_size is None:
        batch_size = max_rows_per_batch
    elif batch_size > max_rows_per_batch:
        batch_size = max_rows_per_batch

    # last_error = None
    for batch in chunk_rows(processed_rows, batch_size):
        for attempt in range(max_retries):
            try:
                # explain_sql = compile_upsert_with_values(stmt, session.bind, batch)
                # my_logger.debug(f"EXPLAIN-ready SQL (with actual values) for {message_count}: {explain_sql}")


                # await asyncio.wait_for(session.execute(stmt, batch), timeout=30)
                my_logger.debug(f"Executing upsert for {message_count} with {len(batch)} rows. session: active = {session.is_active}, in_transaction = {session.in_transaction()}")
                pool = getattr(session.bind, "pool", None)
                from sqlalchemy.pool import base
                if pool and isinstance(pool, base.Pool):
                    pool_size = getattr(pool, "size", lambda: "N/A")()
                    pool_checkedout = getattr(pool, "checkedout", lambda: "N/A")()
                    pool_overflow = getattr(pool, "overflow", lambda: "N/A")()

                    my_logger.debug(
                        f"[POOL STATS] Size: {pool_size}, Checked out: {pool_checkedout}, Overflow: {pool_overflow}"
                    )

                await session.execute(stmt, batch)
                my_logger.debug(f"Upsert executed successfully for {message_count} with {len(batch)} rows. session: active = {session.is_active}, in_transaction = {session.in_transaction()}")
                break
            except DeadlockDetectedError as deadlock_err:
                if attempt < max_retries - 1:
                    wait_time = min(5, 1 * (2 ** attempt))
                    if isinstance(my_logger, Logger):
                        my_logger.warning(
                            f"Deadlock detected on attempt {attempt + 1}, retrying in {wait_time}s: {deadlock_err}")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    if isinstance(my_logger, Logger):
                        my_logger.error(f"Deadlock persisted after {max_retries} attempts: {deadlock_err}")
                return False
            except (AsyncpgForeignKeyViolationError, Psycopg2ForeignKeyViolation) as fk_err:
                # Handle foreign key violations specifically
                if isinstance(my_logger, Logger):
                    key_info = _extract_key_info_for_retry(rows, model.__name__ if model else None)
                    my_logger.error(f"Foreign key violation in upsert_async: {key_info}")
                    my_logger.debug(f"Foreign key violation details: {type(fk_err).__name__}: {fk_err}")
                return False
            except (IntegrityError, PendingRollbackError) as e:
                # Check if this is a foreign key violation wrapped in IntegrityError
                if hasattr(e, 'orig') and e.orig:
                    if isinstance(e.orig, (AsyncpgForeignKeyViolationError, Psycopg2ForeignKeyViolation)):
                        if isinstance(my_logger, Logger):
                            key_info = _extract_key_info_for_retry(rows, model.__name__ if model else None)
                            my_logger.error(f"Foreign key violation (wrapped) in upsert_async: {key_info}")
                            my_logger.debug(f"Foreign key violation details: {type(e.orig).__name__}: {e.orig}")
                        return False

                if isinstance(my_logger, Logger):
                    my_logger.error(f"Non-recoverable error in upsert_async: {type(e).__name__}: {e}")
                return False
            except asyncio.TimeoutError as timeout_err:
                if attempt < max_retries - 1:
                    wait_time = min(5, 1 * (2 ** attempt))
                    if isinstance(my_logger, Logger):
                        my_logger.warning(f"TimeoutError {type(timeout_err).__name__} on attempt {attempt + 1} for message {message_count}, retrying in {wait_time}s: {timeout_err}")
                        for line in traceback.format_tb(timeout_err.__traceback__):
                            my_logger.warning(line)
                        from sqlalchemy.pool import base
                        pool = getattr(session.bind, "pool", None)
                        if pool and isinstance(pool, base.Pool):
                            pool_size = getattr(pool, "size", lambda: "N/A")()
                            pool_checkedout = getattr(pool, "checkedout", lambda: "N/A")()
                            pool_overflow = getattr(pool, "overflow", lambda: "N/A")()

                            my_logger.warning(
                                f"[POOL STATS] Size: {pool_size}, Checked out: {pool_checkedout}, Overflow: {pool_overflow}"
                            )
                        # Log actual SQL with values
                        # ✅ Run diagnostics

                    await asyncio.sleep(wait_time)
                    continue
                return False
            except Exception as e:
                exc_type, exc_value, exc_tb = sys.exc_info()
                line_num = exc_tb.tb_lineno
                tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
                if isinstance(my_logger, Logger):
                    my_logger.error(f"Unexpected error during batch insert: {e}", exc_info=True)
                    my_logger.exception(
                        f"Line {line_num} Error encountered: {''.join(tb.format_exception_only())}",
                        exc_info=True
                    )
                return False
    return True



async def _save_error_batch(batch, model, error_type, my_logger):
    """Save problematic batch to file for analysis"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        df = pd.DataFrame(batch)
        filename = f"{error_type}_batch_{model.__table__.name}_{timestamp}.xlsx"
        await quick_save_async(df, filename, path=f"c:/vishal/log/batch_error")
        my_logger.debug(f"Saved error batch to {filename}")
    except Exception as save_err:
        my_logger.error(f"Failed to save error batch: {save_err}")

async def _rollback_savepoint_safely(savepoint, session, my_logger):
    """Safely rollback a savepoint, fallback to session rollback if needed"""
    rollback_successful = False
    if savepoint:
        try:
            await savepoint.rollback()
            my_logger.debug("Successfully rolled back savepoint")
            rollback_successful = True
        except Exception as rollback_err:
            my_logger.error(f"Failed to rollback savepoint: {rollback_err}")

        try:
            # Force rollback of any remaining transaction state
            if session.in_transaction():
                await session.rollback()
                my_logger.warning("Rolled back entire session as fallback")
            await session.begin()
            my_logger.debug("Started fresh transaction")
            # Test if session is now healthy
            await session.execute(text("SELECT 1"))
            my_logger.debug("Session state verified as healthy")

        except Exception as session_err:
            my_logger.error(f"Session still unhealthy after rollbacks: {session_err}")
            # Last resort: close and invalidate the connection
            try:
                await session.close()
                my_logger.warning("Closed session due to persistent invalid state")
                raise RuntimeError("Session closed due to invalid savepoint state - needs recreation")
            except Exception as close_err:
                my_logger.error(f"Failed to close session: {close_err}")
                raise RuntimeError("Cannot recover session state") from session_err
