fields:
  - id: id
    name: "Changelog ID"
    source_column: id
    target_column: id
    datatype: string
    target_type: int64

  - id: author
    name: "Author"
    source_column: author.accountId
    target_column: author
    datatype: string
    target_type: string

  - id: created
    name: "Created"
    custom: false
    datatype: string
    source_column: created
    target_column: created
    target_type: datetime

  - id: items
    name: "Items"
    source_column: items
    target_column: items
    target_type: json
    datatype: string


  - id: issue_key
    name: "Issue Key"
    source_column: issue_key
    target_column: issue_key
    target_type: string

  - id: issue_id
    name: "Issue ID"
    source_column: issue_id
    target_column: issue_id
    target_type: int64
    datatype: string

database_config:
  model: ChangelogJSON
  on_conflict_update: False,
  no_update_cols: ("items",)

drop-column-prefixes:
  - "author."
  - "created."
  - "items."
  - "issue_key."
  - "issue_id."

drop-column-exceptions:
  - author.accountId
