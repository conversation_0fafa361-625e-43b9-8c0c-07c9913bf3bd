import subprocess
import sys
import tempfile
import shutil
import os

def run_importtime(module_name):
    """Run python -X importtime on a module and open in tuna."""
    # Create a temp file for tuna input
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".txt")
    temp_file.close()

    # Run the command and capture stderr (importtime writes to stderr)
    proc = subprocess.run(
        [sys.executable, "-X", "importtime", "-m", module_name],
        stderr=subprocess.PIPE,
        stdout=subprocess.DEVNULL,
        text=True
    )

    # Filter only the lines that <PERSON><PERSON> can parse
    cleaned_lines = [
        line for line in proc.stderr.splitlines()
        if line.startswith("import time:")
    ]

    with open(temp_file.name, "w", encoding="utf-8") as f:
        f.write("\n".join(cleaned_lines))

    # Open Tuna
    tuna_path = shutil.which("tuna")
    if not tuna_path:
        print("Tuna is not installed. Install with: pip install tuna")
        return

    print(f"Launching tuna with {temp_file.name} ...")
    os.system(f"{tuna_path} {temp_file.name}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python run_tuna.py <module_name>")
        sys.exit(1)
    run_importtime(sys.argv[1])
