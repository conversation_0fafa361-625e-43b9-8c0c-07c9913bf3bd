import asyncio
import csv
import os
from dataclasses import dataclass, asdict
from datetime import datetime
from typing import List, Optional
from enum import Enum

import yaml
from rich.console import Console
from rich.live import Live
from rich.spinner import Spinner
from rich.table import Table
from sqlalchemy import text, exc
from dependency_injector.wiring import inject, Provide
from containers import DatabaseContainer, KeePassContainer

console = Console()


# --- Enums ---
class KillRecommendation(Enum):
    CANDIDATE_KILL_IDLE = "CANDIDATE_KILL_IDLE"
    CANDIDATE_KILL_SLOW = "CANDIDATE_KILL_SLOW"
    CANDIDATE_KILL_STALE = "CANDIDATE_KILL_STALE"
    CANDIDATE_KILL_LONG_TXN = "CANDIDATE_KILL_LONG_TXN"
    KEEP = "KEEP"


class TerminationResult(Enum):
    SUCCESS_CANCELLED = "SUCCESS_CANCELLED"
    SUCCESS_TERMINATED = "SUCCESS_TERMINATED"
    FAILED_CANCEL = "FAILED_CANCEL"
    FAILED_TERMINATE = "FAILED_TERMINATE"
    SKIPPED_SYSTEM_USER = "SKIPPED_SYSTEM_USER"


# --- Dataclasses ---
@dataclass
class ClientReadWaiter:
    pid: int
    usename: str
    application_name: str
    state: str
    wait_event: str
    query: str
    query_start: Optional[datetime]
    backend_start: datetime
    query_duration_seconds: Optional[float]
    connection_age_seconds: float
    blocking_pids: List[int]
    has_transaction: bool
    transaction_duration_seconds: Optional[float]
    kill_recommendation: str
    timestamp_detected: datetime = None

    def __post_init__(self):
        if self.timestamp_detected is None:
            self.timestamp_detected = datetime.now()


@dataclass
class IdleInTransaction:
    pid: int
    usename: str
    application_name: str
    state: str
    xact_start: datetime
    query: str
    timestamp_detected: datetime = None

    def __post_init__(self):
        if self.timestamp_detected is None:
            self.timestamp_detected = datetime.now()


@dataclass
class DeadlockEvent:
    pid: int
    timestamp_detected: datetime = None

    def __post_init__(self):
        if self.timestamp_detected is None:
            self.timestamp_detected = datetime.now()


@dataclass
class TerminationEvent:
    pid: int
    usename: str
    application_name: str
    kill_recommendation: str
    result: str
    error_message: Optional[str]
    timestamp: datetime = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


# --- Configuration ---
SYSTEM_USERS = {
    'postgres',
    'replication',
    'rdsadmin',
    'rdsrepladmin',
    'rds_superuser',
    'azure_superuser',
    'cloudsqladmin'
}

PROTECTED_APPLICATIONS = {
    'pgAdmin 4',
    'pg_dump',
    'pg_restore',
    'psql'
}


# --- Helpers ---
async def write_csv(file: str, rows: List[dataclass]):
    """Append rows to CSV, add headers if file is empty."""
    if not rows:
        return
    row_dicts = [asdict(r) for r in rows]
    fieldnames = row_dicts[0].keys()
    try:
        with open(file, "a", newline="") as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            if f.tell() == 0:  # File is empty → write header
                writer.writeheader()
            writer.writerows(row_dicts)
    except Exception as e:
        console.print(f"[red]CSV write failed: {e}[/red]")


def should_protect_session(usename: str, application_name: str) -> bool:
    """Check if session should be protected from termination."""
    if usename in SYSTEM_USERS:
        return True

    if application_name:
        for protected_app in PROTECTED_APPLICATIONS:
            if protected_app in application_name:
                return True

    return False


# --- Detection Queries ---
@inject
async def detect_client_read_waiters(db_rw_async=Provide[DatabaseContainer.async_session_managers]) -> List[
    ClientReadWaiter]:
    query = text("""
        WITH client_read_sessions AS (
          SELECT 
            pid, 
            usename, 
            COALESCE(application_name, '') as application_name, 
            state, 
            wait_event, 
            COALESCE(query, '') as query, 
            query_start,
            backend_start,
            EXTRACT(EPOCH FROM (now() - query_start)) as query_duration_seconds,
            EXTRACT(EPOCH FROM (now() - backend_start)) as connection_age_seconds,
            pg_blocking_pids(pid) as blocking_pids,
            -- Check if this session has any transactions in progress
            CASE WHEN xact_start IS NOT NULL THEN true ELSE false END as has_transaction,
            EXTRACT(EPOCH FROM (now() - xact_start)) as transaction_duration_seconds
          FROM pg_stat_activity
          WHERE wait_event_type = 'Client'
            AND wait_event = 'ClientRead'
            AND pid != pg_backend_pid() -- Exclude current session
        )
        SELECT *,
          -- Flag potential candidates for termination
          CASE 
            WHEN state = 'idle' 
                 AND connection_age_seconds > 3600  -- Idle for over 1 hour
                 AND (application_name IS NULL OR application_name = '') -- No specific app
                 AND NOT has_transaction -- No active transaction
                 THEN 'CANDIDATE_KILL_IDLE'

            WHEN state = 'active' 
                 AND query_duration_seconds > 300  -- Active query running over 5 minutes
                 AND wait_event = 'ClientRead' -- Still waiting for client
                 THEN 'CANDIDATE_KILL_SLOW'

            WHEN connection_age_seconds > 28800 -- Over 8 hours old
                 AND state = 'idle'
                 AND NOT has_transaction
                 THEN 'CANDIDATE_KILL_STALE'

            WHEN has_transaction 
                 AND transaction_duration_seconds > 1800 -- Transaction over 30 minutes
                 AND state = 'idle'
                 THEN 'CANDIDATE_KILL_LONG_TXN'

            ELSE 'KEEP'
          END as kill_recommendation
        FROM client_read_sessions
        ORDER BY 
          CASE WHEN state = 'active' THEN 1 ELSE 2 END, -- Active sessions first
          query_duration_seconds DESC NULLS LAST;
    """)
    async with db_rw_async["public"].async_session() as session:
        result = await session.execute(query)
        waiters = []
        for row in result.fetchall():
            waiter = ClientReadWaiter(
                pid=row.pid,
                usename=row.usename,
                application_name=row.application_name,
                state=row.state,
                wait_event=row.wait_event,
                query=row.query,
                query_start=row.query_start,
                backend_start=row.backend_start,
                query_duration_seconds=float(row.query_duration_seconds) if row.query_duration_seconds else None,
                connection_age_seconds=float(row.connection_age_seconds),
                blocking_pids=row.blocking_pids,
                has_transaction=row.has_transaction,
                transaction_duration_seconds=float(
                    row.transaction_duration_seconds) if row.transaction_duration_seconds else None,
                kill_recommendation=row.kill_recommendation
            )
            waiters.append(waiter)
        return waiters


@inject
async def detect_idle_in_txn(db_rw_async=Provide[DatabaseContainer.async_session_managers]) -> List[IdleInTransaction]:
    query = text("""
        SELECT pid, usename, COALESCE(application_name, '') as application_name, state, xact_start, COALESCE(query, '') as query
        FROM pg_stat_activity
        WHERE state = 'idle in transaction'
          AND pid != pg_backend_pid();
    """)
    async with db_rw_async["public"].async_session() as session:
        result = await session.execute(query)
        return [IdleInTransaction(*row) for row in result.fetchall()]


@inject
async def detect_deadlocks(db_rw_async=Provide[DatabaseContainer.async_session_managers]) -> List[DeadlockEvent]:
    query = text("""
        SELECT DISTINCT pid
        FROM pg_locks
        WHERE granted = false
          AND pid IS NOT NULL;
    """)
    async with db_rw_async["public"].async_session() as session:
        result = await session.execute(query)
        return [DeadlockEvent(row[0]) for row in result.fetchall()]


# --- Termination Functions ---
@inject
async def cancel_request(
        pid: int,
        db_rw_async=Provide[DatabaseContainer.async_session_managers],
) -> bool:
    """
    Attempt to cancel a query using pg_cancel_request.
    Returns True if successful, False otherwise.
    """
    try:
        query = text("SELECT pg_cancel_backend(:pid)")
        async with db_rw_async["public"].async_session() as session:
            result = await session.execute(query, {'pid': pid})
            # pg_cancel_request returns true if signal was sent successfully
            return result.scalar()
    except exc.SQLAlchemyError as e:
        console.print(f"[yellow]Cancel request failed for PID {pid}: {e}[/yellow]")
        return False


@inject
async def terminate_backend(
        pid: int,
        db_rw_async=Provide[DatabaseContainer.async_session_managers],
) -> bool:
    """
    Terminate a backend using pg_terminate_backend.
    Returns True if successful, False otherwise.
    """
    try:
        query = text("SELECT pg_terminate_backend(:pid)")
        async with db_rw_async["public"].async_session() as session:
            result = await session.execute(query, {'pid': pid})
            # pg_terminate_backend returns true if signal was sent successfully
            return result.scalar()
    except exc.SQLAlchemyError as e:
        console.print(f"[red]Terminate backend failed for PID {pid}: {e}[/red]")
        return False


async def safe_terminate_session(waiter: ClientReadWaiter) -> TerminationEvent:
    """
    Safely terminate a session using cancel -> terminate approach.
    """
    # Check if session should be protected
    if should_protect_session(waiter.usename, waiter.application_name):
        console.print(f"[yellow]Skipping protected session PID {waiter.pid} (user: {waiter.usename})[/yellow]")
        return TerminationEvent(
            pid=waiter.pid,
            usename=waiter.usename,
            application_name=waiter.application_name,
            kill_recommendation=waiter.kill_recommendation,
            result=TerminationResult.SKIPPED_SYSTEM_USER.value,
            error_message=f"Protected user: {waiter.usename}"
        )

    console.print(f"[orange1]Attempting to terminate PID {waiter.pid} ({waiter.kill_recommendation})[/orange1]")

    # Step 1: Try to cancel the request first (gentler approach)
    cancel_success = await cancel_request(waiter.pid)
    if cancel_success:
        # Wait a moment to see if cancellation was enough
        await asyncio.sleep(2)
        console.print(f"[green]Successfully cancelled request for PID {waiter.pid}[/green]")
        return TerminationEvent(
            pid=waiter.pid,
            usename=waiter.usename,
            application_name=waiter.application_name,
            kill_recommendation=waiter.kill_recommendation,
            result=TerminationResult.SUCCESS_CANCELLED.value,
            error_message=None
        )

    # Step 2: If cancel failed or wasn't effective, terminate the backend
    console.print(f"[yellow]Cancel request failed for PID {waiter.pid}, attempting termination...[/yellow]")
    terminate_success = await terminate_backend(waiter.pid)

    if terminate_success:
        console.print(f"[green]Successfully terminated backend PID {waiter.pid}[/green]")
        return TerminationEvent(
            pid=waiter.pid,
            usename=waiter.usename,
            application_name=waiter.application_name,
            kill_recommendation=waiter.kill_recommendation,
            result=TerminationResult.SUCCESS_TERMINATED.value,
            error_message=None
        )
    else:
        console.print(f"[red]Failed to terminate backend PID {waiter.pid}[/red]")
        return TerminationEvent(
            pid=waiter.pid,
            usename=waiter.usename,
            application_name=waiter.application_name,
            kill_recommendation=waiter.kill_recommendation,
            result=TerminationResult.FAILED_TERMINATE.value,
            error_message="Both cancel and terminate failed"
        )


# --- Display Functions ---
def create_status_table(client_read: List[ClientReadWaiter], idle_txn: List[IdleInTransaction],
                        deadlocks: List[DeadlockEvent], terminations: List[TerminationEvent]) -> Table:
    """Create a rich table showing current status."""
    table = Table(title="PostgreSQL Monitor Status")
    table.add_column("Metric", style="cyan")
    table.add_column("Count", style="magenta")
    table.add_column("Details", style="white")

    # Client read waiters
    kill_candidates = [w for w in client_read if w.kill_recommendation != 'KEEP']
    table.add_row("ClientRead Waiters", str(len(client_read)),
                  f"{len(kill_candidates)} candidates for termination")

    # Idle in transaction
    table.add_row("Idle in Transaction", str(len(idle_txn)), "")

    # Deadlocks
    table.add_row("Potential Deadlocks", str(len(deadlocks)), "")

    # Recent terminations
    if terminations:
        successful = len([t for t in terminations if 'SUCCESS' in t.result])
        table.add_row("Recent Terminations", str(len(terminations)),
                      f"{successful} successful")

    return table


# --- Main Monitor Loop ---
async def monitor(interval: int = 30, csv_file: str = "pg_monitor.csv",
                  enable_auto_kill: bool = True, max_kills_per_cycle: int = 5):
    """
    Main monitoring loop with optional auto-kill functionality.

    Args:
        interval: Sleep interval between checks (seconds)
        csv_file: CSV file for logging events
        enable_auto_kill: Whether to automatically terminate problematic sessions
        max_kills_per_cycle: Maximum number of sessions to kill per monitoring cycle
    """
    recent_terminations = []

    with Live(console=console, refresh_per_second=1) as live:
        while True:
            try:
                # Clear recent terminations older than 5 minutes
                cutoff_time = datetime.now()
                recent_terminations = [t for t in recent_terminations
                                       if (cutoff_time - t.timestamp).seconds < 300]

                # Detect issues
                client_read = await detect_client_read_waiters()
                idle_txn = await detect_idle_in_txn()
                deadlocks = await detect_deadlocks()

                # Log to CSV
                await write_csv(csv_file, client_read)
                await write_csv(csv_file, idle_txn)
                await write_csv(csv_file, deadlocks)

                # Handle auto-kill if enabled
                if enable_auto_kill:
                    kill_candidates = [w for w in client_read if w.kill_recommendation != 'KEEP']

                    if kill_candidates:
                        console.print(
                            f"[bold red]Found {len(kill_candidates)} sessions marked for termination[/bold red]")

                        # Limit kills per cycle to avoid overwhelming the system
                        candidates_to_kill = kill_candidates[:max_kills_per_cycle]

                        for candidate in candidates_to_kill:
                            termination_event = await safe_terminate_session(candidate)
                            recent_terminations.append(termination_event)

                            # Log termination event
                            await write_csv(csv_file.replace('.csv', '_terminations.csv'), [termination_event])

                            # Small delay between kills
                            await asyncio.sleep(1)

                # Update display
                status_table = create_status_table(client_read, idle_txn, deadlocks, recent_terminations)
                live.update(status_table)

            except exc.SQLAlchemyError as e:
                console.print(f"[red]DB error: {e}[/red]")
            except Exception as e:
                console.print(f"[red]Unexpected error: {e}[/red]")

            await asyncio.sleep(interval)


# --- Legacy kill function for backward compatibility ---
@inject
async def kill_transaction(
        pid: int,
        db_rw_async=Provide[DatabaseContainer.async_session_managers],
):
    """
    Legacy function - use safe_terminate_session instead.
    """
    return await terminate_backend(pid)


if __name__ == "__main__":
    config_path = os.path.join(os.path.dirname(__file__), "config.yaml")
    with open(config_path, 'r') as f:
        config_data = yaml.safe_load(f)

    keepass_container = KeePassContainer()
    keepass_container.wire(modules=[__name__])

    container = DatabaseContainer()
    container.wire(modules=[__name__])

    # Configuration - modify these as needed
    ENABLE_AUTO_KILL = True  # Set to False to disable automatic termination
    MONITOR_INTERVAL = 30  # Check every 30 seconds
    MAX_KILLS_PER_CYCLE = 3  # Maximum kills per monitoring cycle

    console.print("[bold green]Starting PostgreSQL Monitor with Auto-Kill Protection[/bold green]")
    console.print(f"Auto-kill enabled: {ENABLE_AUTO_KILL}")
    console.print(f"Monitor interval: {MONITOR_INTERVAL}s")
    console.print(f"Max kills per cycle: {MAX_KILLS_PER_CYCLE}")
    try:
        asyncio.run(monitor(
            interval=MONITOR_INTERVAL,
            enable_auto_kill=ENABLE_AUTO_KILL,
            max_kills_per_cycle=MAX_KILLS_PER_CYCLE
        ))
    except KeyboardInterrupt:
        console.print("\nMonitor stopped by user")
