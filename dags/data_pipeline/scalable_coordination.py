# coding=utf-8
"""
Scalable Coordination System for JIRA Data Pipeline.

This module provides event-based coordination and dynamic scaling for producers
and consumers based on workload size using Fibonacci series scaling rules.
"""

import asyncio
import logging
from collections import defaultdict
from dataclasses import dataclass, field
from typing import Dict, Set, Callable, Optional, Tuple, List
from enum import Enum


class QueueType(Enum):
    """Queue types for different processing stages"""
    ISSUES = "queue_issues"
    CHANGELOG = "queue_changelog"
    WORKLOG = "queue_worklog"
    COMMENT = "queue_comment"
    ISSUE_LINKS = "queue_issue_links"
    ISSUE = "queue_issue"
    UPSERT_ISSUE = "queue_upsert_issue"
    UPSERT_OTHERS = "queue_upsert_others"
    STATS = "queue_stats"


@dataclass
class ScalingConfig:
    """Configuration for dynamic scaling behavior"""
    max_producers: int = 15
    consumer_scale_threshold: int = 50
    min_consumers_per_queue: int = 1
    max_consumers_per_queue: int = 3
    fibonacci_thresholds: List[Tuple[int, int]] = field(default_factory=lambda: [
        (1597, 1), (2584, 2), (4181, 3), (6765, 4), (10946, 5),
        (17711, 6), (28657, 7), (46368, 8), (75025, 9), (121393, 10),
        (196418, 11), (317811, 12), (514229, 13), (832040, 14), (1346269, 15)
    ])


class FibonacciProducerScaler:
    """Handles Fibonacci-based producer scaling logic"""
    
    def __init__(self, config: ScalingConfig = None, logger=None):
        self.config = config or ScalingConfig()
        self.logger = logger or logging.getLogger(__name__)


    
    def get_producer_count(self, record_count: int) -> int:
        """
        Determine number of producers based on record count using Fibonacci series.
        
        Args:
            record_count: Total number of records to process
            
        Returns:
            Number of producers to spawn (1-15)
        """
        if record_count <= 0:
            return 1
            
        for threshold, producers in self.config.fibonacci_thresholds:
            if record_count <= threshold:
                self.logger.info(f"Record count {record_count} -> {producers} producers")
                return producers
        
        self.logger.info(f"Record count {record_count} -> {self.config.max_producers} producers (max)")
        return self.config.max_producers
    
    def get_batch_parameters(self, record_count: int) -> Tuple[int, int]:
        """
        Calculate optimal batch parameters for split_jql_by_count.
        
        Args:
            record_count: Total number of records
            
        Returns:
            Tuple of (max_batch_size, num_batches)
        """
        num_producers = self.get_producer_count(record_count)
        
        # Calculate optimal batch size
        if record_count <= 1000:
            max_batch_size = record_count
        else:
            max_batch_size = max(100, record_count // num_producers)
        
        # Ensure we don't exceed reasonable limits
        max_batch_size = min(max_batch_size, 5000)
        
        self.logger.debug(f"Batch parameters: max_batch_size={max_batch_size}, num_batches={num_producers}")
        return max_batch_size, num_producers


class ScalableCoordinationManager:
    """
    Manages event-based coordination between producers and consumers with dynamic scaling.
    
    This replaces the None sentinel-based system with proper asyncio coordination.
    """
    
    def __init__(self, config: ScalingConfig = None):
        self.config = config or ScalingConfig()
        self.logger = logging.getLogger(__name__)
        
        # Core coordination events
        self.producers_completed = asyncio.Event()
        # self.all_consumers_ready = asyncio.Condition()
        # self.processing_complete = asyncio.Event()
        self.shutdown_requested = asyncio.Event()
        
        # Producer/Consumer tracking
        self.active_producers: Set[str] = set()
        self.active_consumers: Dict[str, Set[str]] = defaultdict(set)
        self.producer_count = 0
        self.consumer_counts: Dict[str, int] = defaultdict(int)
        
        # Queue monitoring
        self.queue_sizes: Dict[str, int] = defaultdict(int)
        self.queue_refs: Dict[str, asyncio.Queue] = {}
        
        # Processing triggers
        # self.consume_issue_complete = asyncio.Event()
        self.immediate_processing = asyncio.Event()

        # Completion tracking events
        self.consume_issues_complete = asyncio.Event()  # Tracks when consume_issues is done
        self.all_consume_issues_complete = asyncio.Event()  # Tracks when ALL consume_issues instances are done

        # Individual consumer completion events
        self.consume_changelog_complete = asyncio.Event()
        self.consume_worklog_complete = asyncio.Event()
        self.consume_comment_complete = asyncio.Event()
        self.consume_issue_links_complete = asyncio.Event()
        self.consume_issue_complete_event = asyncio.Event()  # Different from consume_issue_complete (processing trigger)
        self.consume_upsert_issue_complete = asyncio.Event()
        
        # Locks for thread safety
        self._producer_lock = asyncio.Lock()
        self._consumer_lock = asyncio.Lock()
        self.logger.info("Scalable Coordination Manager initialized")
    
    def update_logger(self, logger: logging.Logger) -> None:
        """Update the logger instance"""
        self.logger = logger
    async def register_producer(self, producer_id: str) -> None:
        """Register a new producer"""
        async with self._producer_lock:
            self.active_producers.add(producer_id)
            self.producer_count = len(self.active_producers)
            self.logger.debug(f"Registered producer {producer_id}. Total: {self.producer_count}")
    
    async def unregister_producer(self, producer_id: str) -> None:
        """Unregister a completed producer"""
        async with self._producer_lock:
            self.active_producers.discard(producer_id)
            self.producer_count = len(self.active_producers)
            self.logger.debug(f"Unregistered producer {producer_id}. Remaining: {self.producer_count}")
            
            # Signal completion when all producers are done
            if self.producer_count == 0:
                self.producers_completed.set()
                self.logger.info("All producers completed - signaling consumers")
    
    async def register_consumer(self, queue_name: str, consumer_id: str) -> None:
        """Register a new consumer for a specific queue"""
        async with self._consumer_lock:
            self.active_consumers[queue_name].add(consumer_id)
            self.consumer_counts[queue_name] = len(self.active_consumers[queue_name])
            self.logger.debug(f"Registered consumer {consumer_id} for {queue_name}. Total: {self.consumer_counts[queue_name]}")


    async def unregister_consumer(self, queue_name: str, consumer_id: str) -> None:
        """Unregister a completed consumer"""
        async with self._consumer_lock:
            self.active_consumers[queue_name].discard(consumer_id)
            self.consumer_counts[queue_name] = len(self.active_consumers[queue_name])
            self.logger.debug(f"Unregistered consumer {consumer_id} from {queue_name}. Remaining: {self.consumer_counts[queue_name]}")


    def register_queue(self, queue_name: str, queue_ref: asyncio.Queue) -> None:
        """Register a queue reference for monitoring"""
        self.queue_refs[queue_name] = queue_ref
        self.logger.debug(f"Registered queue {queue_name}")
    
    def get_queue_size(self, queue_name: str) -> int:
        """Get current size of a queue"""
        if queue_name in self.queue_refs:
            return self.queue_refs[queue_name].qsize()
        return 0
    
    async def should_terminate_consumer(self, queue_name: str) -> bool:
        """
        Check if a consumer should terminate.

        For queue_upsert_issue: Should terminate when all specialized consumers are done AND queue is empty
        For other queues: Should terminate when all producers are done AND queue is empty
        """
        queue_empty = self.get_queue_size(queue_name) == 0

        if queue_name == "queue_upsert_issue":
            # For upsert queue, check if all specialized consumers are done
            specialized_queues = ["queue_changelog", "queue_worklog", "queue_comment", "queue_issue_links", "queue_issue"]
            # Debug Why specialized_queues is not getting dereferenced
            all_specialized_consumers_done = all(
                len(self.active_consumers[q]) == 0 for q in specialized_queues
            )
            if not all_specialized_consumers_done:
                active_counts = {q: len(self.active_consumers[q]) for q in specialized_queues if len(self.active_consumers[q]) > 0}
                self.logger.debug(f"Active specialized consumers: {active_counts}")
            else:
                self.logger.debug("issue is resolved!!!!")

            # should_terminate = all_specialized_consumers_done and queue_empty
            should_terminate = self.all_consume_issues_complete.is_set() and queue_empty

            self.logger.debug(f"{queue_name} state: all_specialized_consumers_done={all_specialized_consumers_done}, queue_empty={queue_empty}")
            if should_terminate:
                self.logger.debug(f"queue_upsert_issue should terminate: specialized_consumers_done={all_specialized_consumers_done}, queue_empty={queue_empty}")
        elif queue_name == "queue_upsert_others":
            specialized_queues = ["queue_changelog", "queue_worklog", "queue_comment", "queue_issue_links", "queue_issue"]
            # Debug Why specialized_queues is not getting dereferenced
            all_specialized_consumers_done = all(
                len(self.active_consumers[q]) == 0 for q in specialized_queues
            )
            if not all_specialized_consumers_done:
                active_counts = {q: len(self.active_consumers[q]) for q in specialized_queues if len(self.active_consumers[q]) > 0}
                self.logger.debug(f"Active specialized consumers: {active_counts}")

            # For others upsert queue, check if all specialized consumers are done
            all_specialized_done = self.are_all_specialized_consumers_done()
            should_terminate = self.all_consume_issues_complete.is_set() and queue_empty
            if should_terminate:
                self.logger.debug(f"queue_upsert_others should terminate: specialized_consumers_done={all_specialized_consumers_done}, queue_empty={queue_empty}")

        elif queue_name in ["queue_changelog", "queue_worklog", "queue_comment", "queue_issue_links", "queue_issue"]:
            # For specialized queues, check if the corresponding consumer event is set
            consumer_type_mapping = {
                "queue_changelog": "consume_changelog",
                "queue_worklog": "consume_worklog",
                "queue_comment": "consume_comment",
                "queue_issue_links": "consume_issue_links",
                "queue_issue": "consume_issue"
            }

            consumer_type = consumer_type_mapping[queue_name]
            consumer_done = self.is_consumer_complete(consumer_type) or self.all_consume_issues_complete.is_set()
            should_terminate = consumer_done and queue_empty


            if should_terminate:

                self.logger.debug(f"Specialized consumer for {queue_name} should terminate")
        else:
            # For other queues, check if all producers are done
            producers_done = self.producers_completed.is_set()
            should_terminate = producers_done and queue_empty

            if should_terminate:
                self.logger.debug(f"Consumer for {queue_name} should terminate: producers_done={producers_done}, queue_empty={queue_empty}")

        return should_terminate

    def are_all_specialized_consumers_done(self) -> bool:
        """Check if all specialized consumers are done processing."""

        # Simple check: all individual consumer events are set
        all_done = (
            self.consume_changelog_complete.is_set() and
            self.consume_worklog_complete.is_set() and
            self.consume_comment_complete.is_set() and
            self.consume_issue_links_complete.is_set() and
            self.consume_issue_complete_event.is_set()
        )

        if all_done:
            self.logger.info("All specialized consumers have completed (event-based)")
        else:
            # Debug which ones are not complete
            incomplete = []
            if not self.consume_changelog_complete.is_set():
                incomplete.append("consume_changelog")
            if not self.consume_worklog_complete.is_set():
                incomplete.append("consume_worklog")
            if not self.consume_comment_complete.is_set():
                incomplete.append("consume_comment")
            if not self.consume_issue_links_complete.is_set():
                incomplete.append("consume_issue_links")
            if not self.consume_issue_complete_event.is_set():
                incomplete.append("consume_issue")

            self.logger.debug(f"Incomplete specialized consumers: {incomplete}")

        return all_done

    async def _auto_signal_specialized_consumers_complete(self) -> None:
        """Automatically signal specialized consumers as complete when consume_issues is done and queues are empty"""
        consumer_queue_mapping = {
            "consume_changelog": "queue_changelog",
            "consume_worklog": "queue_worklog",
            "consume_comment": "queue_comment",
            "consume_issue_links": "queue_issue_links",
            "consume_issue": "queue_issue"
        }

        for consumer_type, queue_name in consumer_queue_mapping.items():
            if not self.is_consumer_complete(consumer_type):
                queue_empty = self.get_queue_size(queue_name) == 0

                if queue_empty:
                    await self.signal_consumer_complete(consumer_type)

                    # Force cleanup active consumers for this queue
                    if len(self.active_consumers[queue_name]) > 0:
                        consumers_to_remove = list(self.active_consumers[queue_name])
                        for consumer_id in consumers_to_remove:
                            await self.unregister_consumer(queue_name, consumer_id)

    async def _force_cleanup_specialized_consumers(self) -> None:
        """Force cleanup of specialized consumers when consume_issues is complete"""
        specialized_queues = ["queue_changelog", "queue_worklog", "queue_comment", "queue_issue_links", "queue_issue"]

        for queue_name in specialized_queues:

            if len(self.active_consumers[queue_name]) > 0:
                queue_empty = self.get_queue_size(queue_name) == 0
                if queue_empty:
                    self.logger.info(f"Force cleaning up consumers for {queue_name} (consume_issues complete, queue empty)")
                    # Unregister all consumers for this queue
                    consumers_to_remove = list(self.active_consumers[queue_name])
                    for consumer_id in consumers_to_remove:
                        await self.unregister_consumer(queue_name, consumer_id)
                        self.logger.info(f"Force unregistered consumer {consumer_id} from {queue_name}")


    async def wait_for_producers_completion(self) -> None:
        """Wait for all producers to complete"""
        await self.producers_completed.wait()
        self.logger.info("All producers have completed")
    
    async def signal_consume_issue_complete(self) -> None:
        """Signal that consume_issue processing is complete"""
        # self.consume_issue_complete.set()
        self.consume_issue_complete_event.set()
        self.immediate_processing.set()
        self.logger.info("consume_issue processing completed - triggering immediate processing")

    async def signal_consume_issues_complete(self, consumer_id: str) -> None:
        """Signal that a consume_issues instance has completed"""

        self.logger.info(f"consume_issues {consumer_id} completed")

        # Signal consume_issues completion
        self.consume_issues_complete.set()
        self.all_consume_issues_complete.set()

        # Automatically signal all specialized consumers as complete if their queues are empty
        await self._auto_signal_specialized_consumers_complete()

    def is_consume_issues_complete(self) -> bool:
        """Check if consume_issues processing is complete"""
        return self.consume_issues_complete.is_set()

    def are_all_consume_issues_complete(self) -> bool:
        """Check if all consume_issues instances are complete"""
        return self.all_consume_issues_complete.is_set()

    async def signal_consumer_complete(self, consumer_type: str) -> None:
        """Signal that a specific consumer type has completed"""

        if consumer_type == "consume_changelog":
            self.consume_changelog_complete.set()
        elif consumer_type == "consume_worklog":
            self.consume_worklog_complete.set()
        elif consumer_type == "consume_comment":
            self.consume_comment_complete.set()
        elif consumer_type == "consume_issue_links":
            self.consume_issue_links_complete.set()
        elif consumer_type == "consume_issue":
            self.consume_issue_complete_event.set()

        self.logger.info(f"{consumer_type} completed")

    def is_consumer_complete(self, consumer_type: str) -> bool:
        """Check if a specific consumer type is complete"""
        if consumer_type == "consume_changelog":
            return self.consume_changelog_complete.is_set()
        elif consumer_type == "consume_worklog":
            return self.consume_worklog_complete.is_set()
        elif consumer_type == "consume_comment":
            return self.consume_comment_complete.is_set()
        elif consumer_type == "consume_issue_links":
            return self.consume_issue_links_complete.is_set()
        elif consumer_type == "consume_issue":
            return self.consume_issue_complete_event.is_set()
        return False
    
    async def wait_for_processing_trigger(self, batch_threshold: int = 10000) -> bool:
        """
        Wait for either immediate processing trigger or batch threshold.
        
        Returns:
            True if immediate processing was triggered, False if batch threshold reached
        """
        # Create tasks for both conditions
        immediate_task = asyncio.create_task(self.immediate_processing.wait())
        
        try:
            # Wait for immediate trigger
            await immediate_task
            self.logger.info("Immediate processing triggered")
            return True
        except asyncio.CancelledError:
            self.logger.debug("Processing trigger wait cancelled")
            return False
    
    async def should_scale_consumers(self, queue_name: str) -> Optional[str]:
        """
        Determine if consumers should be scaled up or down.
        
        Returns:
            'up' if should scale up, 'down' if should scale down, None if no change needed
        """
        queue_size = self.get_queue_size(queue_name)
        current_consumers = self.consumer_counts[queue_name]
        self.logger.debug(f"Queue size for {queue_name}: {queue_size}")
        self.logger.debug(f"Current consumers for {queue_name}: {current_consumers}")
        
        if queue_size > self.config.consumer_scale_threshold and current_consumers < self.config.max_consumers_per_queue:
            return 'up'
        elif queue_size <= self.config.consumer_scale_threshold and current_consumers > self.config.min_consumers_per_queue:
            return 'down'
        
        return None
    
    async def request_shutdown(self) -> None:
        """Request graceful shutdown of all components"""
        self.shutdown_requested.set()
        self.logger.info("Shutdown requested for coordination manager")
    
    def is_shutdown_requested(self) -> bool:
        """Check if shutdown has been requested"""
        return self.shutdown_requested.is_set()


class DynamicConsumerManager:
    """
    Manages dynamic scaling of consumers for specialized queues.

    Spawns additional consumers when queue size exceeds threshold,
    scales down when queue size is manageable.
    """

    def __init__(self, coordination_manager: ScalableCoordinationManager, logger=None):
        self.coordination_manager = coordination_manager
        self.logger = logger or logging.getLogger(__name__)
        self.consumer_tasks: Dict[str, List[asyncio.Task]] = defaultdict(list)
        self.scaling_lock = asyncio.Lock()
        # Maintain mapping from task object to originally registered consumer_id
        # This avoids relying on task.get_name() which can differ from the ID we registered
        self.task_to_consumer_id: Dict[str, Dict[asyncio.Task, str]] = defaultdict(dict)

    async def manage_queue_scaling(
        self,
        queue_name: str,
        consumer_factory: Callable,
        consumer_args: tuple,
        consumer_kwargs: dict,
        check_interval: float = 5.0
    ) -> None:
        """
        Monitor and manage consumer scaling for a specific queue.

        Args:
            queue_name: Name of the queue to monitor
            consumer_factory: Function to create new consumers
            consumer_args: Arguments for consumer factory
            consumer_kwargs: Keyword arguments for consumer factory
            check_interval: How often to check queue size (seconds)
        """
        self.logger.info(f"Starting dynamic scaling management for {queue_name}")

        try:
            # Always start with at least one consumer

            await self._ensure_minimum_consumers(queue_name, consumer_factory, consumer_args, consumer_kwargs)

            while not self.coordination_manager.is_shutdown_requested():
                # Clean up completed consumers first
                await self._cleanup_completed_consumers(queue_name)

                # Check if consume_issues is complete and we should stop maintaining minimum consumers
                if (queue_name in ["queue_changelog", "queue_worklog", "queue_comment", "queue_issue_links", "queue_issue"]
                    and self.coordination_manager.is_consume_issues_complete()):

                    # Force cleanup when consume_issues is complete and queue is empty
                    queue_empty = self.coordination_manager.get_queue_size(queue_name) == 0
                    if queue_empty:
                        self.logger.info(f"consume_issues complete and {queue_name} empty - force terminating all consumers")
                        await self._terminate_all_consumers(queue_name)
                        break
                    else:
                        self.logger.debug(f"consume_issues complete but {queue_name} not empty (size: {self.coordination_manager.get_queue_size(queue_name)})")
                else:
                    # Normal scaling logic
                    scaling_action = await self.coordination_manager.should_scale_consumers(queue_name)
                    self.logger.debug(f"Scaling action for {queue_name}: {scaling_action}")

                    if scaling_action == 'up':
                        await self._scale_up_consumers(queue_name, consumer_factory, consumer_args, consumer_kwargs)
                    elif scaling_action == 'down':
                        await self._scale_down_consumers(queue_name)


                    # Ensure minimum consumers are maintained (only if consume_issues not complete)
                    if not self.coordination_manager.is_consume_issues_complete():
                        await self._ensure_minimum_consumers(queue_name, consumer_factory, consumer_args, consumer_kwargs)

                await asyncio.sleep(check_interval)

        except asyncio.CancelledError:
            self.logger.info(f"Dynamic scaling cancelled for {queue_name}")
            await self._terminate_all_consumers(queue_name)
        except Exception as e:
            self.logger.error(f"Error in dynamic scaling for {queue_name}: {e}")
            await self._terminate_all_consumers(queue_name)

    async def _ensure_minimum_consumers(
        self,
        queue_name: str,
        consumer_factory: Callable,
        consumer_args: tuple,
        consumer_kwargs: dict
    ) -> None:
        """Ensure at least minimum number of consumers are running"""
        async with self.scaling_lock:
            current_count = len(self.consumer_tasks[queue_name])
            min_consumers = self.coordination_manager.config.min_consumers_per_queue

            while current_count < min_consumers:
                consumer_id = f"{queue_name}_consumer_{current_count + 1}"
                self.logger.info(f"Starting initial consumer {consumer_id} for {queue_name}")

                # Create new consumer task
                task = asyncio.create_task(
                    consumer_factory(*consumer_args, **consumer_kwargs),
                    name=consumer_id
                )

                self.consumer_tasks[queue_name].append(task)
                self.task_to_consumer_id[queue_name][task] = consumer_id
                await self.coordination_manager.register_consumer(queue_name, consumer_id)
                current_count += 1


    async def _cleanup_completed_consumers(self, queue_name: str) -> None:
        """Clean up consumers that have completed naturally or should be terminated"""
        async with self.scaling_lock:
            completed_tasks = []
            remaining_tasks = []

            for task in self.consumer_tasks[queue_name]:
                self.logger.debug(f"Checking task: {task.get_name()}")
                if task.done():
                    completed_tasks.append(task)
                    consumer_id = self.task_to_consumer_id[queue_name].pop(task, None)
                    if consumer_id is None:
                        consumer_id = task.get_name()
                        self.logger.debug(f"Consumer ID mapping missing for completed task; falling back to task name {consumer_id}")
                    self.logger.info(f"Consumer {consumer_id} for {queue_name} completed naturally")
                    await self.coordination_manager.unregister_consumer(queue_name, consumer_id)

                    # Signal completion for specialized consumers
                    await self._signal_specialized_consumer_completion(queue_name)
                else:
                    # Check if this consumer should be terminated
                    if await self.coordination_manager.should_terminate_consumer(queue_name):
                        consumer_id = self.task_to_consumer_id[queue_name].pop(task, None)
                        if consumer_id is None:
                            consumer_id = task.get_name()
                            self.logger.debug(f"Consumer ID mapping missing for cancelling task; falling back to task name {consumer_id}")
                        self.logger.info(f"Force cancelling consumer {consumer_id} for {queue_name} (should terminate)")
                        task.cancel()
                        completed_tasks.append(task)
                        await self.coordination_manager.unregister_consumer(queue_name, consumer_id)
                    else:
                        remaining_tasks.append(task)

            # Update the task list
            self.consumer_tasks[queue_name] = remaining_tasks

            if completed_tasks:
                self.logger.info(f"Cleaned up {len(completed_tasks)} completed/cancelled consumers for {queue_name}")

    async def _scale_up_consumers(
        self,
        queue_name: str,
        consumer_factory: Callable,
        consumer_args: tuple,
        consumer_kwargs: dict
    ) -> None:
        """Scale up consumers for a queue"""
        async with self.scaling_lock:
            current_count = len(self.consumer_tasks[queue_name])
            if current_count >= self.coordination_manager.config.max_consumers_per_queue:
                return

            consumer_id = f"{queue_name}_consumer_{current_count + 1}"
            self.logger.info(f"Scaling up: Adding consumer {consumer_id} for {queue_name}")

            # Create new consumer task
            task = asyncio.create_task(
                consumer_factory(*consumer_args, **consumer_kwargs),
                name=consumer_id
            )

            self.consumer_tasks[queue_name].append(task)
            self.task_to_consumer_id[queue_name][task] = consumer_id
            await self.coordination_manager.register_consumer(queue_name, consumer_id)

    async def _scale_down_consumers(self, queue_name: str) -> None:
        """Scale down consumers for a queue"""
        async with self.scaling_lock:
            if len(self.consumer_tasks[queue_name]) <= self.coordination_manager.config.min_consumers_per_queue:
                return

            # Cancel the most recent consumer
            task_to_cancel = self.consumer_tasks[queue_name].pop()
            consumer_id = self.task_to_consumer_id[queue_name].pop(task_to_cancel, None)
            if consumer_id is None:
                consumer_id = task_to_cancel.get_name()
                self.logger.debug(f"Consumer ID mapping missing for scale-down; falling back to task name {consumer_id}")

            self.logger.info(f"Scaling down: Removing consumer {consumer_id} for {queue_name}")

            task_to_cancel.cancel()
            await self.coordination_manager.unregister_consumer(queue_name, consumer_id)

    async def _terminate_all_consumers(self, queue_name: str) -> None:
        """Terminate all consumers for a queue"""
        async with self.scaling_lock:
            tasks = self.consumer_tasks[queue_name].copy()
            self.consumer_tasks[queue_name].clear()

            for task in tasks:
                consumer_id = self.task_to_consumer_id[queue_name].pop(task, None)
                if consumer_id is None:
                    consumer_id = task.get_name()
                    self.logger.debug(f"Consumer ID mapping missing during terminate-all; falling back to task name {consumer_id}")
                # Always unregister, even if the task already completed
                if not task.done():
                    task.cancel()
                await self.coordination_manager.unregister_consumer(queue_name, consumer_id)

            self.logger.info(f"Terminated all consumers for {queue_name}")

            # Signal completion for specialized consumers when all are terminated
            await self._signal_specialized_consumer_completion(queue_name)

    async def _signal_specialized_consumer_completion(self, queue_name: str) -> None:
        """Signal completion for specialized consumers when they finish naturally"""
        # Map queue names to consumer types
        queue_to_consumer_type = {
            "queue_changelog": "consume_changelog",
            "queue_worklog": "consume_worklog",
            "queue_comment": "consume_comment",
            "queue_issue_links": "consume_issue_links",
            "queue_issue": "consume_issue"
        }

        consumer_type = queue_to_consumer_type.get(queue_name)
        if consumer_type:
            # Check if all consumers for this queue are done and queue is empty
            queue_empty = self.coordination_manager.get_queue_size(queue_name) == 0
            no_active_consumers = len(self.consumer_tasks[queue_name]) == 0

            if queue_empty and no_active_consumers:
                self.logger.info(f"Signaling completion for {consumer_type} (queue empty, no active consumers)")
                await self.coordination_manager.signal_consumer_complete(consumer_type)


# Global instances for use across the application
default_scaling_config = ScalingConfig()
# fibonacci_scaler = FibonacciProducerScaler(default_scaling_config)
coordination_manager = ScalableCoordinationManager(default_scaling_config)
# dynamic_consumer_manager = DynamicConsumerManager(coordination_manager)
