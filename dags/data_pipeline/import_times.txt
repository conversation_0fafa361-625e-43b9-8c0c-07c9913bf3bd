import time:       170 |        170 | winreg
import time:       294 |        294 |   _io
import time:        44 |         44 |   marshal
import time:       201 |        201 |   nt
import time:       570 |       1108 | _frozen_importlib_external
import time:       655 |        655 |   time
import time:       188 |        842 | zipimport
import time:        81 |         81 |     _codecs
import time:       592 |        672 |   codecs
import time:      6284 |       6284 |   encodings.aliases
import time:      3395 |      10351 | encodings
import time:      2407 |       2407 | encodings.utf_8
import time:      1257 |       1257 | encodings.cp1252
import time:        81 |         81 | _signal
import time:        52 |         52 |     _abc
import time:       248 |        300 |   abc
import time:       243 |        543 | io
import time:       113 |        113 |       _stat
import time:       207 |        320 |     stat
import time:      1375 |       1375 |     _collections_abc
import time:       160 |        160 |       genericpath
import time:       509 |        509 |       _winapi
import time:      1156 |       1823 |     ntpath
import time:      2142 |       5659 |   os
import time:       319 |        319 |   _sitebuiltins
import time:      2528 |       2528 |   encodings.utf_8_sig
import time:      2513 |       2513 |   _virtualenv
import time:      1009 |       1009 |   _distutils_hack
import time:      1362 |       1362 |     pywin32_system32
import time:      6169 |       7530 |   pywin32_bootstrap
import time:       414 |        414 |   sitecustomize
import time:     10755 |      30724 | site
import time:       984 |        984 |       warnings
import time:      1566 |       2549 |     importlib
import time:       406 |       2955 |   importlib.machinery
import time:      5304 |       5304 |     importlib._abc
import time:      2314 |       2314 |     types
import time:      1118 |       8735 |   importlib.util
import time:       803 |      12492 | runpy
import time:       353 |        353 |       itertools
import time:      1387 |       1387 |       keyword
import time:       266 |        266 |         _operator
import time:      1738 |       2004 |       operator
import time:      1515 |       1515 |       reprlib
import time:       255 |        255 |       _collections
import time:      6622 |      12134 |     collections
import time:      2818 |       2818 |     collections.abc
import time:      2394 |       2394 |       concurrent
import time:       238 |        238 |                 _functools
import time:      3217 |       3454 |               functools
import time:      4373 |       7827 |             enum
import time:       214 |        214 |               _sre
import time:      2681 |       2681 |                 re._constants
import time:      5446 |       8127 |               re._parser
import time:      1865 |       1865 |               re._casefix
import time:      3019 |      13224 |             re._compiler
import time:      1494 |       1494 |             copyreg
import time:      4269 |      26812 |           re
import time:      4904 |       4904 |                 token
import time:       192 |        192 |                 _tokenize
import time:      4437 |       9532 |               tokenize
import time:      1237 |      10768 |             linecache
import time:      3131 |       3131 |             textwrap
import time:      1685 |       1685 |             contextlib
import time:      2835 |      18417 |           traceback
import time:      2449 |       2449 |             _weakrefset
import time:      2198 |       4647 |           weakref
import time:       164 |        164 |             _string
import time:      5016 |       5180 |           string
import time:      4655 |       4655 |           threading
import time:       435 |        435 |           atexit
import time:      8108 |      68251 |         logging
import time:      4058 |      72308 |       concurrent.futures._base
import time:      3240 |      77941 |     concurrent.futures
import time:       706 |        706 |     errno
import time:       172 |        172 |       _heapq
import time:      2931 |       3102 |     heapq
import time:      9913 |       9913 |       _socket
import time:       554 |        554 |         math
import time:      2970 |       2970 |         select
import time:      4176 |       7699 |       selectors
import time:      5684 |      23296 |     socket
import time:       230 |        230 |         _locale
import time:      3668 |       3897 |       locale
import time:      2938 |       2938 |       signal
import time:      1350 |       1350 |       fcntl
import time:       219 |        219 |       msvcrt
import time:      4473 |      12875 |     subprocess
import time:     28148 |      28148 |       _ssl
import time:       201 |        201 |           _struct
import time:      1258 |       1458 |         struct
import time:       707 |        707 |         binascii
import time:      7169 |       9333 |       base64
import time:     29859 |      67339 |     ssl
import time:      3539 |       3539 |     asyncio.constants
import time:      1473 |       1473 |           _ast
import time:      4658 |       6131 |         ast
import time:       132 |        132 |             _opcode
import time:      2116 |       2248 |           opcode
import time:      6006 |       8253 |         dis
import time:     11863 |      26246 |       inspect
import time:      2561 |      28806 |     asyncio.coroutines
import time:       100 |        100 |         _contextvars
import time:      1777 |       1876 |       contextvars
import time:      1744 |       1744 |       asyncio.format_helpers
import time:      2397 |       2397 |         asyncio.base_futures
import time:      2223 |       2223 |         asyncio.exceptions
import time:      2002 |       2002 |         asyncio.base_tasks
import time:      6497 |      13118 |       _asyncio
import time:      3908 |      20645 |     asyncio.events
import time:      2169 |       2169 |     asyncio.futures
import time:      5193 |       5193 |     asyncio.protocols
import time:      2927 |       2927 |       asyncio.transports
import time:      1331 |       1331 |       asyncio.log
import time:      4779 |       9036 |     asyncio.sslproto
import time:      3244 |       3244 |         asyncio.mixins
import time:      4777 |       8020 |       asyncio.locks
import time:       128 |        128 |             _typing
import time:      8287 |       8415 |           typing
import time:      2865 |      11280 |         asyncio.timeouts
import time:      3132 |      14411 |       asyncio.tasks
import time:      1882 |      24312 |     asyncio.staggered
import time:      2793 |       2793 |     asyncio.trsock
import time:      8974 |     305671 |   asyncio.base_events
import time:      2248 |       2248 |   asyncio.runners
import time:      1887 |       1887 |   asyncio.queues
import time:      1994 |       1994 |   asyncio.streams
import time:      3501 |       3501 |   asyncio.subprocess
import time:      2714 |       2714 |   asyncio.taskgroups
import time:      2967 |       2967 |   asyncio.threads
import time:      5027 |       5027 |     _overlapped
import time:      3903 |       3903 |     asyncio.base_subprocess
import time:      3477 |       3477 |     asyncio.proactor_events
import time:      2485 |       2485 |     asyncio.selector_events
import time:      1070 |       1070 |               posix
import time:       627 |       1697 |             posixpath
import time:      3428 |       5124 |           fnmatch
import time:       918 |        918 |           zlib
import time:      1719 |       1719 |             _compression
import time:      2325 |       2325 |             _bz2
import time:      3980 |       8023 |           bz2
import time:      5310 |       5310 |             _lzma
import time:      3362 |       8671 |           lzma
import time:      4163 |      26898 |         shutil
import time:        71 |         71 |             _bisect
import time:      1125 |       1195 |           bisect
import time:       143 |        143 |           _random
import time:       470 |        470 |           _sha2
import time:      4529 |       6335 |         random
import time:      2210 |      35443 |       tempfile
import time:      1542 |      36985 |     asyncio.windows_utils
import time:      4364 |      56239 |   asyncio.windows_events
import time:      6979 |     384195 | asyncio
import time:      3061 |       3061 |   copy
import time:      6438 |       9498 | dataclasses
import time:      1346 |       1346 |         __future__
import time:      7053 |       7053 |             _wmi
import time:      3574 |      10627 |           platform
import time:      1503 |       1503 |               _csv
import time:      4595 |       6097 |             csv
import time:      2538 |       2538 |             email
import time:      1076 |       1076 |                 urllib
import time:      4728 |       4728 |                 ipaddress
import time:      4930 |      10734 |               urllib.parse
import time:      3434 |      14167 |             pathlib
import time:      1132 |       1132 |                 zipfile._path.glob
import time:      2330 |       3462 |               zipfile._path
import time:      4512 |       7973 |             zipfile
import time:      2639 |       2639 |                 quopri
import time:       509 |        509 |                     _datetime
import time:      1968 |       2477 |                   datetime
import time:      3919 |       3919 |                     calendar
import time:      2914 |       6833 |                   email._parseaddr
import time:      2365 |       2365 |                     email.base64mime
import time:      2448 |       2448 |                     email.quoprimime
import time:      2037 |       2037 |                     email.errors
import time:      1146 |       1146 |                     email.encoders
import time:      3839 |      11834 |                   email.charset
import time:      3927 |      25069 |                 email.utils
import time:      3615 |       3615 |                   email.header
import time:      3363 |       6978 |                 email._policybase
import time:      3498 |       3498 |                 email._encoded_words
import time:      2026 |       2026 |                 email.iterators
import time:      5858 |      46067 |               email.message
import time:      1169 |       1169 |                 importlib.metadata._functools
import time:      1861 |       3029 |               importlib.metadata._text
import time:      2824 |      51919 |             importlib.metadata._adapters
import time:      3170 |       3170 |             importlib.metadata._meta
import time:      2867 |       2867 |             importlib.metadata._collections
import time:      1991 |       1991 |             importlib.metadata._itertools
import time:      1995 |       1995 |                   importlib.resources.abc
import time:      4426 |       4426 |                   importlib.resources._adapters
import time:      3209 |       9630 |                 importlib.resources._common
import time:      2929 |       2929 |                 importlib.resources._legacy
import time:      1811 |      14369 |               importlib.resources
import time:      2736 |      17104 |             importlib.abc
import time:      5682 |     113504 |           importlib.metadata
import time:      3029 |       3029 |           configparser
import time:      3465 |       3465 |             _compat_pickle
import time:       667 |        667 |             _pickle
import time:      4502 |       8634 |           pickle
import time:    155424 |     291215 |         sqlalchemy.util.compat
import time:      3586 |       3586 |         sqlalchemy.cimmutabledict
import time:      3130 |     299276 |       sqlalchemy.util._collections
import time:      1011 |       1011 |       sqlalchemy.util._preloaded
import time:      8246 |       8246 |           greenlet._greenlet
import time:      1586 |       9832 |         greenlet
import time:     16113 |      16113 |               _hashlib
import time:       665 |        665 |               _blake2
import time:      4017 |      20795 |             hashlib
import time:      3098 |       3098 |             sqlalchemy.exc
import time:      6167 |      30059 |           sqlalchemy.util.langhelpers
import time:      3698 |      33757 |         sqlalchemy.util._concurrency_py3k
import time:      1857 |       1857 |         sqlalchemy.util._compat_py3k
import time:      1855 |      47301 |       sqlalchemy.util.concurrency
import time:      3194 |       3194 |       sqlalchemy.util.deprecations
import time:      3159 |     353940 |     sqlalchemy.util
import time:      1501 |       1501 |                   sqlalchemy.sql.roles
import time:      2514 |       2514 |                   sqlalchemy.sql.visitors
import time:      2645 |       2645 |                     sqlalchemy.sql.operators
import time:       956 |        956 |                     sqlalchemy.inspection
import time:      5952 |       9552 |                   sqlalchemy.sql.traversals
import time:      5951 |      19517 |                 sqlalchemy.sql.base
import time:      2826 |       2826 |                     numbers
import time:      4266 |       7092 |                   sqlalchemy.sql.coercions
import time:      9043 |       9043 |                             _decimal
import time:      2471 |      11514 |                           decimal
import time:       181 |        181 |                                 _json
import time:      1781 |       1962 |                               json.scanner
import time:      2260 |       4222 |                             json.decoder
import time:      1992 |       1992 |                             json.encoder
import time:      2724 |       8938 |                           json
import time:      2098 |       2098 |                             sqlalchemy.sql.type_api
import time:      2155 |       2155 |                             sqlalchemy.sql.annotation
import time:      9064 |      13316 |                           sqlalchemy.sql.elements
import time:       953 |        953 |                                   sqlalchemy.event.legacy
import time:      1131 |       1131 |                                   sqlalchemy.event.registry
import time:      2461 |       4543 |                                 sqlalchemy.event.attr
import time:      1973 |       6515 |                               sqlalchemy.event.base
import time:      3110 |       9625 |                             sqlalchemy.event.api
import time:      2411 |      12035 |                           sqlalchemy.event
import time:      2008 |       2008 |                             sqlalchemy.cprocessors
import time:      1460 |       3468 |                           sqlalchemy.processors
import time:     11438 |      60707 |                         sqlalchemy.sql.sqltypes
import time:      1531 |      62237 |                       sqlalchemy.types
import time:      1307 |       1307 |                           sqlalchemy.util.topological
import time:      5149 |       6455 |                         sqlalchemy.sql.ddl
import time:     16354 |      16354 |                           sqlalchemy.sql.selectable
import time:     11488 |      27842 |                         sqlalchemy.sql.schema
import time:      3574 |      37870 |                       sqlalchemy.sql.util
import time:      4627 |     104733 |                     sqlalchemy.sql.dml
import time:      2742 |     107474 |                   sqlalchemy.sql.crud
import time:      5912 |       5912 |                   sqlalchemy.sql.functions
import time:      6339 |     126816 |                 sqlalchemy.sql.compiler
import time:      1586 |       1586 |                   sqlalchemy.sql.lambdas
import time:      4790 |       6376 |                 sqlalchemy.sql.expression
import time:      2439 |       2439 |                 sqlalchemy.sql.events
import time:      2169 |       2169 |                 sqlalchemy.sql.naming
import time:      1434 |       1434 |                 sqlalchemy.sql.default_comparator
import time:     12099 |     170848 |               sqlalchemy.sql
import time:       208 |     171056 |             sqlalchemy.sql.compiler
import time:      3239 |     174294 |           sqlalchemy.engine.interfaces
import time:      1050 |       1050 |           sqlalchemy.engine.util
import time:       971 |        971 |           sqlalchemy.log
import time:      8448 |     184762 |         sqlalchemy.engine.base
import time:      6288 |     191049 |       sqlalchemy.engine.events
import time:       867 |        867 |           sqlalchemy.dialects
import time:      2493 |       3360 |         sqlalchemy.engine.url
import time:      1101 |       1101 |         sqlalchemy.engine.mock
import time:      2419 |       2419 |             sqlalchemy.pool.base
import time:      2873 |       5292 |           sqlalchemy.pool.events
import time:      1770 |       1770 |               sqlalchemy.util.queue
import time:      2756 |       4525 |             sqlalchemy.pool.impl
import time:      2100 |       6625 |           sqlalchemy.pool.dbapi_proxy
import time:      1270 |      13185 |         sqlalchemy.pool
import time:      3321 |      20966 |       sqlalchemy.engine.create
import time:      3833 |       3833 |             sqlalchemy.cresultproxy
import time:      5304 |       9137 |           sqlalchemy.engine.row
import time:      3380 |      12516 |         sqlalchemy.engine.result
import time:      2778 |      15294 |       sqlalchemy.engine.cursor
import time:      2293 |       2293 |       sqlalchemy.engine.reflection
import time:      2004 |     231604 |     sqlalchemy.engine
import time:      1354 |       1354 |     sqlalchemy.schema
import time:      1111 |       1111 |     sqlalchemy.events
import time:      1581 |       1581 |       sqlalchemy.engine.characteristics
import time:      5069 |       6650 |     sqlalchemy.engine.default
import time:      3081 |     597738 |   sqlalchemy
import time:       286 |     598023 | sqlalchemy.exc
import time:      2719 |       2719 |           typing_extensions
import time:      2677 |       5396 |         bs4._typing
import time:      3490 |       3490 |           soupsieve.__meta__
import time:      1424 |       1424 |             soupsieve.util
import time:      4836 |       4836 |                 soupsieve.pretty
import time:      2529 |       7365 |               soupsieve.css_types
import time:      5231 |       5231 |               unicodedata
import time:      3481 |      16077 |             soupsieve.css_match
import time:     35979 |      53479 |           soupsieve.css_parser
import time:      3157 |      60125 |         soupsieve
import time:      1117 |      66637 |       bs4.css
import time:      1259 |       1259 |       bs4._deprecation
import time:      4771 |       4771 |               html.entities
import time:      4107 |       8877 |             html
import time:       164 |       9041 |           html.entities
import time:      1068 |       1068 |           cchardet
import time:       778 |        778 |           chardet
import time:      3959 |       3959 |                 charset_normalizer.constant
import time:      3643 |       3643 |                   charset_normalizer.md__mypyc
import time:       136 |        136 |                     _multibytecodec
import time:      1526 |       1662 |                   charset_normalizer.utils
import time:      3717 |       9021 |                 charset_normalizer.md
import time:      1288 |       1288 |                 charset_normalizer.models
import time:      1515 |      15782 |               charset_normalizer.cd
import time:      1722 |      17504 |             charset_normalizer.api
import time:       800 |        800 |             charset_normalizer.legacy
import time:      1068 |       1068 |             charset_normalizer.version
import time:      2167 |      21538 |           charset_normalizer
import time:     39122 |      71545 |         bs4.dammit
import time:      2301 |      73845 |       bs4.formatter
import time:      2408 |       2408 |       bs4._warnings
import time:      1977 |       1977 |       bs4.filter
import time:      3957 |     150081 |     bs4.element
import time:      1174 |       1174 |     bs4.exceptions
import time:      4256 |       4256 |         _markupbase
import time:      3734 |       7989 |       html.parser
import time:      1592 |       9581 |     bs4.builder._htmlparser
import time:       440 |        440 |       html5lib
import time:      2108 |       2547 |     bs4.builder._html5lib
import time:      1366 |       1366 |       lxml
import time:      3184 |       3184 |         lxml._elementpath
import time:      3099 |       3099 |         gzip
import time:      1433 |       1433 |         rnc2rng
import time:     35109 |      42824 |       lxml.etree
import time:      2488 |      46677 |     bs4.builder._lxml
import time:      2988 |     213046 |   bs4.builder
import time:      4282 |     217327 | bs4
import time:      1388 |       1388 |       multidict._abc
import time:      5124 |       5124 |         multidict._multidict
import time:      1615 |       6739 |       multidict._compat
import time:      1823 |       9949 |     multidict
import time:      2822 |      12771 |   aiohttp.hdrs
import time:       994 |        994 |         attr._compat
import time:      2300 |       2300 |           attr._config
import time:      1285 |       1285 |             attr.exceptions
import time:      1925 |       3209 |           attr.setters
import time:      8792 |      14300 |         attr._make
import time:      2301 |      17594 |       attr.converters
import time:      1052 |       1052 |       attr.filters
import time:      9614 |       9614 |       attr.validators
import time:      1616 |       1616 |       attr._cmp
import time:      1852 |       1852 |       attr._funcs
import time:      1556 |       1556 |       attr._next_gen
import time:      1683 |       1683 |       attr._version_info
import time:      3404 |      38367 |     attr
import time:      4882 |       4882 |             yarl._quoting_c
import time:      2082 |       6964 |           yarl._quoting
import time:      2127 |       9090 |         yarl._quoters
import time:      1189 |      10279 |       yarl._query
import time:      1505 |       1505 |             idna.idnadata
import time:       972 |        972 |             idna.intranges
import time:      2045 |       4521 |           idna.core
import time:       744 |        744 |           idna.package_data
import time:      1472 |       6736 |         idna
import time:      2968 |       2968 |           propcache
import time:      3583 |       3583 |             propcache._helpers_c
import time:      1871 |       5453 |           propcache._helpers
import time:      2810 |      11230 |         propcache.api
import time:      2272 |       2272 |         yarl._parse
import time:      1152 |       1152 |         yarl._path
import time:      5641 |      27028 |       yarl._url
import time:      1218 |      38524 |     yarl
import time:      3011 |       3011 |       http
import time:      1642 |       1642 |         aiohttp.typedefs
import time:      1791 |       3433 |       aiohttp.http_exceptions
import time:      1658 |       1658 |           aiohttp.client_exceptions
import time:      4105 |       4105 |             netrc
import time:      2517 |       2517 |               email.feedparser
import time:      2626 |       5143 |             email.parser
import time:      3638 |       3638 |               http.client
import time:      2430 |       2430 |                 urllib.response
import time:      3675 |       6105 |               urllib.error
import time:      1698 |       1698 |               nturl2path
import time:      5482 |      16921 |             urllib.request
import time:       897 |        897 |             aiohttp.log
import time:      8942 |      36006 |           aiohttp.helpers
import time:      2061 |       2061 |           aiohttp.tcp_helpers
import time:      1637 |      41361 |         aiohttp.base_protocol
import time:      1110 |       1110 |           brotlicffi
import time:       606 |        606 |           brotli
import time:      3240 |       4955 |         aiohttp.compression_utils
import time:      2933 |       2933 |             http.cookies
import time:      2934 |       2934 |             aiohttp._cookie_helpers
import time:      2103 |       7970 |           aiohttp.abc
import time:      3575 |       3575 |           aiohttp._http_writer
import time:      3875 |      15419 |         aiohttp.http_writer
import time:      2569 |       2569 |         aiohttp.streams
import time:      5550 |       5550 |         aiohttp._http_parser
import time:      4960 |      74812 |       aiohttp.http_parser
import time:      1545 |       1545 |           aiohttp._websocket
import time:      2278 |       2278 |           aiohttp._websocket.models
import time:      2083 |       2083 |           aiohttp._websocket.mask
import time:      2806 |       8710 |         aiohttp._websocket.helpers
import time:      4853 |       4853 |           aiohttp._websocket.reader_c
import time:      1250 |       6103 |         aiohttp._websocket.reader
import time:      1665 |       1665 |         aiohttp._websocket.writer
import time:      2631 |      19108 |       aiohttp.http_websocket
import time:      2353 |     102715 |     aiohttp.http
import time:      2875 |       2875 |       mimetypes
import time:      3179 |       6053 |     aiohttp.payload
import time:      3134 |       3134 |             _uuid
import time:      4029 |       7163 |           uuid
import time:      3908 |      11070 |         aiohttp.multipart
import time:      1609 |       1609 |         aiohttp.formdata
import time:      5331 |      18010 |       aiohttp.client_reqrep
import time:      1564 |      19573 |     aiohttp.client_middlewares
import time:      4350 |       4350 |     aiohttp.client_ws
import time:      1064 |       1064 |           aiohappyeyeballs._staggered
import time:      1129 |       1129 |           aiohappyeyeballs.types
import time:      2043 |       4234 |         aiohappyeyeballs.impl
import time:      1132 |       1132 |         aiohappyeyeballs.utils
import time:      1738 |       7103 |       aiohappyeyeballs
import time:      2093 |       2093 |       aiohttp.client_proto
import time:      4292 |       4292 |               _cffi_backend
import time:      5607 |       9898 |             pycares._cares
import time:      1590 |       1590 |               pycares.utils
import time:      4910 |       6500 |             pycares.errno
import time:       982 |        982 |             pycares._version
import time:      2843 |       2843 |               _queue
import time:      3589 |       6432 |             queue
import time:      4861 |      28670 |           pycares
import time:      1132 |       1132 |           aiodns.error
import time:      3007 |      32809 |         aiodns
import time:      3994 |      36803 |       aiohttp.resolver
import time:     58510 |     104508 |     aiohttp.connector
import time:      2984 |       2984 |     aiohttp.cookiejar
import time:      3135 |       3135 |           frozenlist._frozenlist
import time:      1344 |       4479 |         frozenlist
import time:      1893 |       6371 |       aiosignal
import time:     10810 |      17181 |     aiohttp.tracing
import time:      6836 |     341088 |   aiohttp.client
import time:      2250 |       2250 |   aiohttp.client_middleware_digest_auth
import time:      3364 |       3364 |   aiohttp.payload_streamer
import time:      3325 |     362795 | aiohttp
import time:      3554 |       3554 |     sysconfig
import time:      4142 |       7696 |   zoneinfo._tzpath
import time:      2052 |       2052 |   zoneinfo._common
import time:      3208 |       3208 |   _zoneinfo
import time:      3530 |      16484 | zoneinfo
import time:       988 |        988 |   dependency_injector
import time:       463 |        463 |   _interpreters
import time:       406 |        406 |   _xxsubinterpreters
import time:      1029 |       1029 |     _interpreters
import time:      1424 |       1424 |       yaml.error
import time:      1384 |       1384 |       yaml.tokens
import time:      1119 |       1119 |       yaml.events
import time:      2641 |       2641 |       yaml.nodes
import time:      9402 |       9402 |         yaml.reader
import time:      2215 |       2215 |         yaml.scanner
import time:      2539 |       2539 |         yaml.parser
import time:      2008 |       2008 |         yaml.composer
import time:      3504 |       3504 |         yaml.constructor
import time:      2559 |       2559 |         yaml.resolver
import time:      3341 |      25565 |       yaml.loader
import time:      1224 |       1224 |         yaml.emitter
import time:       793 |        793 |         yaml.serializer
import time:      1057 |       1057 |         yaml.representer
import time:      1162 |       4235 |       yaml.dumper
import time:      5984 |       5984 |         yaml._yaml
import time:      1490 |       7474 |       yaml.cyaml
import time:      2988 |      46828 |     yaml
import time:       928 |        928 |       pydantic_settings.exceptions
import time:      2807 |       2807 |           gettext
import time:      3378 |       6185 |         argparse
import time:      7991 |       7991 |                 pydantic_core._pydantic_core
import time:     37514 |      37514 |                 pydantic_core.core_schema
import time:      3172 |      48675 |               pydantic_core
import time:      1381 |      50056 |             pydantic.version
import time:      1972 |      52027 |           pydantic._migration
import time:      1045 |       1045 |               typing_inspection
import time:      2402 |       2402 |               typing_inspection.typing_objects
import time:      2463 |       5909 |             typing_inspection.introspection
import time:       596 |        596 |             pydantic._internal
import time:      2428 |       2428 |                 pydantic._internal._namespace_utils
import time:      2894 |       5322 |               pydantic._internal._typing_extra
import time:      1576 |       6897 |             pydantic._internal._repr
import time:      3406 |      16807 |           pydantic.errors
import time:      3457 |      72290 |         pydantic
import time:       554 |        554 |           pydantic._internal._internal_dataclass
import time:      3655 |       4209 |         pydantic.aliases
import time:      1631 |       1631 |         pydantic.warnings
import time:      2403 |       2403 |         pydantic._internal._config
import time:      1607 |       1607 |             pydantic._internal._import_utils
import time:      4989 |       6596 |           pydantic._internal._utils
import time:      2404 |       9000 |         pydantic._internal._signature
import time:      1971 |       1971 |             pydantic._internal._core_utils
import time:      9685 |      11655 |           pydantic._internal._decorators
import time:      1633 |       1633 |               pydantic.plugin
import time:      1418 |       3050 |             pydantic.plugin._schema_validator
import time:      3356 |       3356 |                 pydantic._internal._forward_ref
import time:      3012 |       6367 |               pydantic._internal._generics
import time:      1649 |       1649 |               pydantic._internal._docs_extraction
import time:      2070 |      10085 |             pydantic._internal._fields
import time:      3533 |       3533 |               fractions
import time:      1042 |       1042 |               pydantic.annotated_handlers
import time:      8895 |       8895 |               pydantic.functional_validators
import time:      1175 |       1175 |                 pydantic._internal._core_metadata
import time:      1486 |       1486 |                 pydantic._internal._mock_val_ser
import time:       874 |        874 |                 pydantic._internal._schema_generation_shared
import time:      5674 |       9208 |               pydantic.json_schema
import time:      2087 |       2087 |               pydantic._internal._discriminated_union
import time:      2331 |       2331 |               pydantic._internal._known_annotated_metadata
import time:      2895 |       2895 |               pydantic._internal._schema_gather
import time:      6198 |      36186 |             pydantic._internal._generate_schema
import time:      1908 |      51229 |           pydantic._internal._dataclasses
import time:     19980 |      19980 |             annotated_types
import time:      2604 |       2604 |             pydantic._internal._validators
import time:     16243 |      16243 |             pydantic.types
import time:      6569 |      45396 |           pydantic.fields
import time:      2341 |     110618 |         pydantic.dataclasses
import time:      1473 |       1473 |           pydantic._internal._model_construction
import time:      3244 |       4716 |         pydantic.main
import time:      1693 |       1693 |             pydantic_settings.utils
import time:      1689 |       1689 |             pydantic_settings.sources.types
import time:       688 |        688 |               pydantic.plugin._loader
import time:     50009 |      50697 |             pydantic_settings.sources.utils
import time:      4829 |      58907 |           pydantic_settings.sources.base
import time:       928 |        928 |                 pydantic_settings.sources.providers.env
import time:      1776 |       2704 |               pydantic_settings.sources.providers.aws
import time:      1576 |       1576 |               pydantic_settings.sources.providers.azure
import time:      2420 |       2420 |                 shlex
import time:      4363 |       6783 |               pydantic_settings.sources.providers.cli
import time:      3075 |       3075 |                     dotenv.parser
import time:      1159 |       1159 |                     dotenv.variables
import time:      2787 |       7020 |                   dotenv.main
import time:      1449 |       8468 |                 dotenv
import time:      1767 |      10235 |               pydantic_settings.sources.providers.dotenv
import time:      1210 |       1210 |               pydantic_settings.sources.providers.gcp
import time:       853 |        853 |               pydantic_settings.sources.providers.json
import time:      1235 |       1235 |                 pydantic_settings.sources.providers.toml
import time:      1282 |       2516 |               pydantic_settings.sources.providers.pyproject
import time:      2298 |       2298 |               pydantic_settings.sources.providers.secrets
import time:      1665 |       1665 |               pydantic_settings.sources.providers.yaml
import time:      3066 |      32902 |             pydantic_settings.sources.providers
import time:       481 |      33383 |           pydantic_settings.sources.providers.aws
import time:      1472 |      93761 |         pydantic_settings.sources
import time:     10093 |     314901 |       pydantic_settings.main
import time:       826 |        826 |       pydantic_settings.version
import time:      2178 |     318832 |     pydantic_settings
import time:      1078 |       1078 |     dependency_injector.errors
import time:     14950 |     382715 |   dependency_injector.providers
import time:      1982 |       1982 |     pkgutil
import time:      1193 |       1193 |       fastapi
import time:       246 |       1438 |     fastapi.params
import time:       875 |        875 |       fast_depends
import time:       357 |       1232 |     fast_depends.dependencies
import time:      1420 |       1420 |       starlette
import time:       808 |        808 |           anyio._core
import time:       666 |        666 |             sniffio._version
import time:      1273 |       1273 |             sniffio._impl
import time:      1588 |       3526 |           sniffio
import time:      1677 |       6010 |         anyio._core._eventloop
import time:      1289 |       1289 |         anyio._core._exceptions
import time:      2785 |       2785 |               anyio.abc._eventloop
import time:      1135 |       1135 |               anyio.abc._resources
import time:       736 |        736 |                 anyio._core._typedattr
import time:      1221 |       1221 |                   anyio.abc._tasks
import time:      1837 |       3058 |                 anyio.abc._streams
import time:      1792 |       5585 |               anyio.abc._sockets
import time:      1251 |       1251 |               anyio.abc._subprocesses
import time:      2194 |       2194 |               anyio.abc._testing
import time:      3670 |       3670 |                 anyio.lowlevel
import time:      1260 |       1260 |                 anyio._core._tasks
import time:       833 |        833 |                 anyio._core._testing
import time:     17562 |      23324 |               anyio._core._synchronization
import time:      2419 |       2419 |               anyio.from_thread
import time:      2878 |      41568 |             anyio.abc
import time:      2067 |      43635 |           anyio.to_thread
import time:      4110 |      47744 |         anyio._core._fileio
import time:       858 |        858 |         anyio._core._resources
import time:       821 |        821 |         anyio._core._signals
import time:      1970 |       1970 |             anyio.streams
import time:      4357 |       6326 |           anyio.streams.stapled
import time:      3126 |       3126 |           anyio.streams.tls
import time:      2525 |      11977 |         anyio._core._sockets
import time:      5632 |       5632 |           anyio.streams.memory
import time:      1928 |       7560 |         anyio._core._streams
import time:      1911 |       1911 |         anyio._core._subprocesses
import time:      4503 |       4503 |         anyio._core._tempfile
import time:      3423 |      86092 |       anyio
import time:      1176 |       1176 |         starlette.types
import time:      2745 |       3921 |       starlette._utils
import time:      1354 |       1354 |         starlette.concurrency
import time:      4769 |       6123 |       starlette.datastructures
import time:      1621 |       1621 |       starlette.exceptions
import time:       888 |        888 |               python_multipart.exceptions
import time:       989 |       1876 |             python_multipart.decoders
import time:      3527 |       5402 |           python_multipart.multipart
import time:      1538 |       6940 |         python_multipart
import time:      6013 |      12952 |       starlette.formparsers
import time:      3423 |     115550 |     starlette.requests
import time:      1103 |       1103 |       werkzeug
import time:       318 |       1420 |     werkzeug.local
import time:       531 |        531 |       _interpreters
import time:      3805 |       4336 |     dependency_injector._cwiring
import time:      5117 |     131072 |   dependency_injector.wiring
import time:      8079 |     523722 | dependency_injector.containers
import time:       381 |        381 |         num2words.compat
import time:       868 |        868 |         num2words.currency
import time:      2016 |       3265 |       num2words.base
import time:      1525 |       4789 |     num2words.lang_EU
import time:      1715 |       6503 |   num2words.lang_AM
import time:      2935 |       2935 |   num2words.lang_AR
import time:      2009 |       2009 |   num2words.lang_AZ
import time:       675 |        675 |     num2words.utils
import time:      1307 |       1982 |   num2words.lang_BE
import time:       696 |        696 |   num2words.lang_BN
import time:      1199 |       1199 |   num2words.lang_CA
import time:      2142 |       2142 |   num2words.lang_CE
import time:       980 |        980 |   num2words.lang_CS
import time:      3610 |       3610 |   num2words.lang_CY
import time:      1870 |       1870 |   num2words.lang_DA
import time:      1139 |       1139 |   num2words.lang_DE
import time:      1300 |       1300 |   num2words.lang_EN
import time:      1143 |       1143 |   num2words.lang_EN_IN
import time:      1283 |       1283 |   num2words.lang_EN_NG
import time:      1080 |       1080 |   num2words.lang_EO
import time:      1273 |       1273 |   num2words.lang_ES
import time:       899 |        899 |   num2words.lang_ES_CO
import time:      1178 |       1178 |   num2words.lang_ES_CR
import time:      1927 |       1927 |   num2words.lang_ES_GT
import time:      1763 |       1763 |   num2words.lang_ES_NI
import time:      1244 |       1244 |   num2words.lang_ES_VE
import time:      1716 |       1716 |   num2words.lang_FA
import time:      2520 |       2520 |   num2words.lang_FI
import time:      1218 |       1218 |   num2words.lang_FR
import time:       908 |        908 |   num2words.lang_FR_BE
import time:       970 |        970 |   num2words.lang_FR_CH
import time:       868 |        868 |   num2words.lang_FR_DZ
import time:      1029 |       1029 |   num2words.lang_HE
import time:      1837 |       1837 |   num2words.lang_HU
import time:      2247 |       2247 |   num2words.lang_ID
import time:      1422 |       1422 |   num2words.lang_IS
import time:      1893 |       1893 |   num2words.lang_IT
import time:      1903 |       1903 |   num2words.lang_JA
import time:       817 |        817 |   num2words.lang_KN
import time:      1296 |       1296 |   num2words.lang_KO
import time:      1081 |       1081 |   num2words.lang_KZ
import time:      1075 |       1075 |   num2words.lang_LT
import time:       811 |        811 |   num2words.lang_LV
import time:      1222 |       1222 |   num2words.lang_NL
import time:      2113 |       2113 |   num2words.lang_NO
import time:      1429 |       1429 |   num2words.lang_PL
import time:      2122 |       2122 |   num2words.lang_PT
import time:      1481 |       1481 |   num2words.lang_PT_BR
import time:      1187 |       1187 |   num2words.lang_RO
import time:      2520 |       2520 |   num2words.lang_RU
import time:       921 |        921 |   num2words.lang_SK
import time:      1094 |       1094 |   num2words.lang_SL
import time:      2135 |       2135 |   num2words.lang_SR
import time:      2059 |       2059 |   num2words.lang_SV
import time:      1469 |       1469 |   num2words.lang_TE
import time:      1968 |       1968 |   num2words.lang_TET
import time:      1787 |       1787 |   num2words.lang_TG
import time:      1525 |       1525 |   num2words.lang_TH
import time:      1736 |       1736 |   num2words.lang_TR
import time:      1744 |       1744 |   num2words.lang_UK
import time:      1907 |       1907 |   num2words.lang_VI
import time:     24973 |     117131 | num2words
import time:       677 |        677 |     numpy.version
import time:       734 |        734 |     numpy._expired_attrs_2_0
import time:      1342 |       1342 |         numpy._utils._convertions
import time:      1802 |       3143 |       numpy._utils
import time:      3306 |       6449 |     numpy._globals
import time:       162 |        162 |       numpy._distributor_init_local
import time:      1641 |       1802 |     numpy._distributor_init
import time:      1706 |       1706 |               numpy.exceptions
import time:      1412 |       1412 |               numpy._core._exceptions
import time:      2114 |       2114 |               numpy._core.printoptions
import time:      1908 |       1908 |               numpy.dtypes
import time:     48674 |      55812 |             numpy._core._multiarray_umath
import time:      1049 |       1049 |               numpy._utils._inspect
import time:      2061 |       3110 |             numpy._core.overrides
import time:      3844 |      62765 |           numpy._core.multiarray
import time:      1319 |       1319 |           numpy._core.umath
import time:      1069 |       1069 |             numpy._core._dtype
import time:      1510 |       1510 |             numpy._core._string_helpers
import time:      1356 |       1356 |             numpy._core._type_aliases
import time:      1671 |       5604 |           numpy._core.numerictypes
import time:      2212 |       2212 |             numpy._core._ufunc_config
import time:      1613 |       1613 |               numpy._core._methods
import time:      3654 |       5267 |             numpy._core.fromnumeric
import time:      2708 |      10186 |           numpy._core._machar
import time:      1110 |       1110 |               numpy._core.shape_base
import time:       861 |        861 |               numpy._core._asarray
import time:      2850 |       2850 |               numpy._core.arrayprint
import time:      2931 |       7750 |             numpy._core.numeric
import time:      1735 |       9484 |           numpy._core.einsumfunc
import time:      2091 |       2091 |           numpy._core.function_base
import time:      2072 |       2072 |           numpy._core.getlimits
import time:      1608 |       1608 |           numpy._core.memmap
import time:      1626 |       1626 |           numpy._core.records
import time:      2429 |       2429 |           numpy._core._add_newdocs
import time:      1279 |       1279 |           numpy._core._add_newdocs_scalars
import time:      1123 |       1123 |           numpy._core._dtype_ctypes
import time:      3212 |       3212 |               _ctypes
import time:      1973 |       1973 |               ctypes._endian
import time:      5027 |      10211 |             ctypes
import time:      6065 |      16275 |           numpy._core._internal
import time:      1857 |       1857 |           numpy._pytesttester
import time:      4967 |     124680 |         numpy._core
import time:       398 |     125077 |       numpy._core._multiarray_umath
import time:      1999 |     127075 |     numpy.__config__
import time:      1712 |       1712 |                       numpy._typing._nbit_base
import time:      2068 |       2068 |                       numpy._typing._nested_sequence
import time:      1175 |       1175 |                       numpy._typing._shape
import time:      5845 |      10799 |                     numpy._typing._array_like
import time:      4821 |       4821 |                     numpy._typing._char_codes
import time:      7148 |       7148 |                     numpy._typing._dtype_like
import time:      1610 |       1610 |                     numpy._typing._nbit
import time:      1202 |       1202 |                     numpy._typing._scalars
import time:       729 |        729 |                     numpy._typing._ufunc
import time:      2947 |      29253 |                   numpy._typing
import time:      1012 |       1012 |                     numpy.lib._stride_tricks_impl
import time:      2232 |       3244 |                   numpy.lib._twodim_base_impl
import time:       751 |        751 |                     numpy.lib._array_utils_impl
import time:       779 |       1530 |                   numpy.lib.array_utils
import time:      5005 |       5005 |                   numpy.linalg._umath_linalg
import time:      5178 |      44208 |                 numpy.linalg._linalg
import time:      1395 |       1395 |                 numpy.linalg.linalg
import time:      1216 |      46818 |               numpy.linalg
import time:      1595 |      48412 |             numpy.matrixlib.defmatrix
import time:       882 |      49294 |           numpy.matrixlib
import time:      1739 |       1739 |             numpy.lib._histograms_impl
import time:      4117 |       5856 |           numpy.lib._function_base_impl
import time:       575 |        575 |           numpy.lib.stride_tricks
import time:      2315 |      58040 |         numpy.lib._index_tricks_impl
import time:      2042 |      60081 |       numpy.lib._arraypad_impl
import time:      3173 |       3173 |       numpy.lib._arraysetops_impl
import time:      1845 |       1845 |       numpy.lib._arrayterator_impl
import time:      2788 |       2788 |       numpy.lib._nanfunctions_impl
import time:      1079 |       1079 |             numpy.lib._utils_impl
import time:      2938 |       4017 |           numpy.lib._format_impl
import time:      1146 |       5162 |         numpy.lib.format
import time:      1456 |       1456 |         numpy.lib._datasource
import time:      3658 |       3658 |         numpy.lib._iotools
import time:      3099 |      13374 |       numpy.lib._npyio_impl
import time:      1218 |       1218 |           numpy.lib._ufunclike_impl
import time:      2489 |       3707 |         numpy.lib._type_check_impl
import time:      2681 |       6388 |       numpy.lib._polynomial_impl
import time:      1430 |       1430 |       numpy.lib._shape_base_impl
import time:      1477 |       1477 |       numpy.lib._version
import time:      1233 |       1233 |       numpy.lib.introspect
import time:      1962 |       1962 |       numpy.lib.mixins
import time:      1471 |       1471 |       numpy.lib.npyio
import time:      2672 |       2672 |         numpy.lib._scimath_impl
import time:      1191 |       3863 |       numpy.lib.scimath
import time:      5154 |     104233 |     numpy.lib
import time:      1131 |       1131 |     numpy._array_api_info
import time:      8456 |     250553 |   numpy
import time:      1262 |       1262 |     pytz.exceptions
import time:      2578 |       2578 |     pytz.lazy
import time:      2172 |       2172 |     pytz.tzinfo
import time:      1742 |       1742 |     pytz.tzfile
import time:      3545 |      11296 |   pytz
import time:       746 |        746 |     dateutil._version
import time:      1641 |       2386 |   dateutil
import time:      1364 |       1364 |     pandas.compat._constants
import time:      1587 |       1587 |     pandas.compat.compressors
import time:      1449 |       1449 |         pandas.util
import time:      7008 |       8457 |       pandas.util.version
import time:      2472 |      10928 |     pandas.compat.numpy
import time:        86 |         86 |         gc
import time:       914 |        914 |         pyarrow._generated_version
import time:       907 |        907 |           backports_abc
import time:      1186 |       1186 |           cloudpickle
import time:      1274 |       1274 |           pyarrow.util
import time:    128235 |     131601 |         pyarrow.lib
import time:      1610 |       1610 |         pyarrow.ipc
import time:      2013 |       2013 |         pyarrow.types
import time:      2719 |     138941 |       pyarrow
import time:      2225 |     141166 |     pandas.compat.pyarrow
import time:      2097 |     157141 |   pandas.compat
import time:      5956 |       5956 |               numpy.random._common
import time:      1691 |       1691 |                 hmac
import time:      1853 |       3544 |               secrets
import time:      6306 |      15806 |             numpy.random.bit_generator
import time:      5898 |      21703 |           numpy.random._bounded_integers
import time:      2751 |       2751 |               numpy.random._pcg64
import time:      3872 |       3872 |               numpy.random._mt19937
import time:     14590 |      21211 |             numpy.random._generator
import time:      2880 |       2880 |             numpy.random._philox
import time:      1939 |       1939 |             numpy.random._sfc64
import time:     14764 |      14764 |             numpy.random.mtrand
import time:      4308 |      45101 |           numpy.random._pickle
import time:      1515 |      68318 |         numpy.random
import time:      6953 |      75271 |       pandas._typing
import time:      1667 |       1667 |       pandas.util._exceptions
import time:      4731 |      81667 |     pandas._config.config
import time:      2362 |       2362 |     pandas._config.dates
import time:      1624 |       1624 |     pandas._config.display
import time:      3139 |      88792 |   pandas._config
import time:       994 |        994 |     pandas.core
import time:      3710 |       4703 |   pandas.core.config_init
import time:      3338 |       3338 |       pandas._libs.pandas_parser
import time:      4348 |       4348 |       pandas._libs.pandas_datetime
import time:      2075 |       2075 |                   pandas._libs.tslibs.ccalendar
import time:      1821 |       1821 |                   pandas._libs.tslibs.np_datetime
import time:      7295 |      11189 |                 pandas._libs.tslibs.dtypes
import time:      3215 |       3215 |                   pandas._libs.tslibs.base
import time:      5282 |       5282 |                       pandas._libs.tslibs.nattype
import time:       888 |        888 |                           pandas.compat._optional
import time:      3572 |       3572 |                               six
import time:       298 |        298 |                               six.moves
import time:      1898 |       1898 |                               dateutil.tz._common
import time:      1837 |       1837 |                               dateutil.tz._factories
import time:      2544 |       2544 |                                 ctypes.wintypes
import time:      3260 |       5804 |                               dateutil.tz.win
import time:      3375 |      16782 |                             dateutil.tz.tz
import time:      1050 |      17832 |                           dateutil.tz
import time:     10471 |      29189 |                         pandas._libs.tslibs.timezones
import time:      2800 |       2800 |                           _strptime
import time:      1104 |       1104 |                           pandas._config.localization
import time:      6140 |      10043 |                         pandas._libs.tslibs.fields
import time:     12305 |      51536 |                       pandas._libs.tslibs.timedeltas
import time:      7627 |       7627 |                       pandas._libs.tslibs.tzconversion
import time:     12618 |      77062 |                     pandas._libs.tslibs.timestamps
import time:      1867 |       1867 |                     pandas._libs.properties
import time:     12500 |      91429 |                   pandas._libs.tslibs.offsets
import time:       891 |        891 |                         dateutil._common
import time:      5064 |       5954 |                       dateutil.parser._parser
import time:      1054 |       1054 |                       dateutil.parser.isoparser
import time:      2091 |       9098 |                     dateutil.parser
import time:      6442 |       6442 |                     pandas._libs.tslibs.strptime
import time:      7514 |      23053 |                   pandas._libs.tslibs.parsing
import time:      7439 |     125134 |                 pandas._libs.tslibs.conversion
import time:      6731 |       6731 |                 pandas._libs.tslibs.period
import time:      4201 |       4201 |                 pandas._libs.tslibs.vectorized
import time:      2956 |     150211 |               pandas._libs.tslibs
import time:       683 |     150894 |             pandas._libs.tslibs.nattype
import time:      3181 |       3181 |             pandas._libs.ops_dispatch
import time:      5827 |     159901 |           pandas._libs.missing
import time:     17123 |     177023 |         pandas._libs.hashtable
import time:     11349 |      11349 |         pandas._libs.algos
import time:      9581 |     197952 |       pandas._libs.interval
import time:      2000 |     207636 |     pandas._libs
import time:       808 |        808 |       pandas.core.dtypes
import time:     10353 |      10353 |       pandas._libs.lib
import time:      3346 |       3346 |       pandas.errors
import time:      1566 |       1566 |         pandas.core.dtypes.generic
import time:      1949 |       3515 |       pandas.core.dtypes.base
import time:      1230 |       1230 |       pandas.core.dtypes.inference
import time:      5800 |      25049 |     pandas.core.dtypes.dtypes
import time:      3184 |       3184 |       pandas.core.dtypes.common
import time:      1861 |       5044 |     pandas.core.dtypes.missing
import time:      1129 |       1129 |       pandas.util._decorators
import time:       848 |        848 |           pandas.io
import time:      2264 |       3111 |         pandas.io._util
import time:      2419 |       5529 |       pandas.core.dtypes.cast
import time:      1050 |       1050 |         pandas.core.dtypes.astype
import time:      1509 |       2559 |       pandas.core.dtypes.concat
import time:      1340 |       1340 |         pandas.core.array_algos
import time:      8258 |       8258 |             numpy.ma.core
import time:      4651 |       4651 |             numpy.ma.extras
import time:      2314 |      15222 |           numpy.ma
import time:      2785 |       2785 |           pandas.core.common
import time:      2757 |      20763 |         pandas.core.construction
import time:      4253 |      26355 |       pandas.core.array_algos.take
import time:      1392 |       1392 |         pandas.core.indexers.utils
import time:      1685 |       3077 |       pandas.core.indexers
import time:      3582 |      42228 |     pandas.core.algorithms
import time:     58646 |      58646 |             pyarrow._compute
import time:      1215 |       1215 |             pyarrow._compute_docstrings
import time:       711 |        711 |             pyarrow.vendored
import time:      4380 |       4380 |               pydoc
import time:      5953 |      10332 |             pyarrow.vendored.docscrape
import time:     49744 |     120646 |           pyarrow.compute
import time:      1776 |     122422 |         pandas.core.arrays.arrow.accessors
import time:      1942 |       1942 |           pandas.util._validators
import time:      2787 |       2787 |           pandas.core.missing
import time:      7212 |       7212 |               pandas._libs.ops
import time:      1918 |       1918 |               pandas.core.roperator
import time:      1186 |       1186 |               pandas.core.computation
import time:      2228 |       2228 |                 pandas.core.computation.check
import time:      2613 |       4841 |               pandas.core.computation.expressions
import time:      1686 |       1686 |               pandas.core.ops.missing
import time:      1665 |       1665 |               pandas.core.ops.dispatch
import time:      2934 |       2934 |               pandas.core.ops.invalid
import time:      3907 |      25345 |             pandas.core.ops.array_ops
import time:      1310 |       1310 |             pandas.core.ops.common
import time:      2302 |       2302 |             pandas.core.ops.docstrings
import time:      1495 |       1495 |             pandas.core.ops.mask_ops
import time:      2307 |      32756 |           pandas.core.ops
import time:      1861 |       1861 |           pandas.core.arraylike
import time:      1345 |       1345 |           pandas.core.arrays._arrow_string_mixins
import time:      1435 |       1435 |           pandas.core.arrays._utils
import time:      3267 |       3267 |             pandas.compat.numpy.function
import time:      1068 |       1068 |             pandas.core.array_algos.quantile
import time:      2106 |       2106 |             pandas.core.sorting
import time:      5981 |      12421 |           pandas.core.arrays.base
import time:      5800 |       5800 |             pandas.core.nanops
import time:      2228 |       2228 |             pandas.core.array_algos.masked_accumulations
import time:      1903 |       1903 |             pandas.core.array_algos.masked_reductions
import time:       937 |        937 |               pandas.core.util
import time:      7255 |       7255 |               pandas._libs.hashing
import time:      2547 |      10738 |             pandas.core.util.hashing
import time:      5488 |      26155 |           pandas.core.arrays.masked
import time:      2780 |       2780 |             pandas._libs.arrays
import time:      1287 |       1287 |               pandas.core.arrays.numeric
import time:      1624 |       2911 |             pandas.core.arrays.floating
import time:      1515 |       1515 |             pandas.core.arrays.integer
import time:      1779 |       1779 |                 pandas.core.array_algos.transforms
import time:      3979 |       5758 |               pandas.core.arrays._mixins
import time:      1434 |       1434 |                 pandas.core.strings
import time:      1805 |       1805 |                 pandas.core.strings.base
import time:      3578 |       6816 |               pandas.core.strings.object_array
import time:      2870 |      15442 |             pandas.core.arrays.numpy_
import time:      1236 |       1236 |             pandas.io.formats
import time:      1907 |       1907 |               pandas.io.formats.console
import time:      3711 |       5618 |             pandas.io.formats.printing
import time:      4179 |      33677 |           pandas.core.arrays.string_
import time:       959 |        959 |             pandas.tseries
import time:      3085 |       4043 |           pandas.tseries.frequencies
import time:      7256 |     125674 |         pandas.core.arrays.arrow.array
import time:      1461 |     249556 |       pandas.core.arrays.arrow
import time:      1781 |       1781 |       pandas.core.arrays.boolean
import time:      2663 |       2663 |         pandas.core.accessor
import time:      3625 |       3625 |         pandas.core.base
import time:      3140 |       9427 |       pandas.core.arrays.categorical
import time:      5058 |       5058 |         pandas._libs.tslib
import time:      2295 |       2295 |           pandas.core.array_algos.datetimelike_accumulations
import time:      5834 |       8129 |         pandas.core.arrays.datetimelike
import time:      1888 |       1888 |         pandas.core.arrays._ranges
import time:      1688 |       1688 |         pandas.tseries.offsets
import time:      3846 |      20606 |       pandas.core.arrays.datetimes
import time:      1823 |       1823 |         pandas.core.arrays.timedeltas
import time:      3511 |       5334 |       pandas.core.arrays.interval
import time:      4194 |       4194 |       pandas.core.arrays.period
import time:      6840 |       6840 |             pandas._libs.sparse
import time:      5276 |      12115 |           pandas.core.arrays.sparse.array
import time:      2696 |      14810 |         pandas.core.arrays.sparse.accessor
import time:      1578 |      16388 |       pandas.core.arrays.sparse
import time:      2716 |       2716 |       pandas.core.arrays.string_arrow
import time:      3304 |     313304 |     pandas.core.arrays
import time:      1440 |       1440 |     pandas.core.flags
import time:      6482 |       6482 |           pandas._libs.internals
import time:      1269 |       1269 |             pandas.core._numba
import time:      3189 |       4458 |           pandas.core._numba.executor
import time:      2858 |      13797 |         pandas.core.apply
import time:      1725 |       1725 |               pandas._libs.indexing
import time:       767 |        767 |                 pandas.core.indexes
import time:     10656 |      10656 |                   pandas._libs.index
import time:      5141 |       5141 |                   pandas._libs.writers
import time:      7859 |       7859 |                   pandas._libs.join
import time:      1991 |       1991 |                   pandas.core.array_algos.putmask
import time:      1173 |       1173 |                   pandas.core.indexes.frozen
import time:      3897 |       3897 |                   pandas.core.strings.accessor
import time:      5021 |      35737 |                 pandas.core.indexes.base
import time:      2735 |       2735 |                   pandas.core.indexes.extension
import time:      4284 |       7018 |                 pandas.core.indexes.category
import time:      2860 |       2860 |                     pandas.core.indexes.range
import time:      1194 |       1194 |                       pandas.core.tools
import time:      3788 |       4982 |                     pandas.core.tools.timedeltas
import time:      5087 |      12927 |                   pandas.core.indexes.datetimelike
import time:      1748 |       1748 |                   pandas.core.tools.times
import time:      4701 |      19376 |                 pandas.core.indexes.datetimes
import time:      3360 |       3360 |                   pandas.core.indexes.multi
import time:      1729 |       1729 |                   pandas.core.indexes.timedeltas
import time:      4578 |       9667 |                 pandas.core.indexes.interval
import time:      2878 |       2878 |                 pandas.core.indexes.period
import time:      2297 |      77738 |               pandas.core.indexes.api
import time:      4258 |      83720 |             pandas.core.indexing
import time:      2101 |       2101 |             pandas.core.sample
import time:      1402 |       1402 |             pandas.core.array_algos.replace
import time:      2030 |       2030 |                 pandas.core.internals.blocks
import time:      1534 |       3564 |               pandas.core.internals.api
import time:      2437 |       2437 |                 pandas.core.internals.base
import time:      1705 |       1705 |                   pandas.core.internals.ops
import time:      3362 |       5066 |                 pandas.core.internals.managers
import time:      2749 |      10251 |               pandas.core.internals.array_manager
import time:      1405 |       1405 |               pandas.core.internals.concat
import time:      1679 |      16898 |             pandas.core.internals
import time:      1610 |       1610 |             pandas.core.internals.construction
import time:       801 |        801 |               pandas.core.methods
import time:       838 |        838 |                 pandas.core.reshape
import time:      3931 |       4769 |               pandas.core.reshape.concat
import time:       603 |        603 |                   mmap
import time:       485 |        485 |                     pwd
import time:       534 |        534 |                     grp
import time:      4617 |       5636 |                   tarfile
import time:      2387 |       2387 |                   pandas.core.shared_docs
import time:      6522 |      15147 |                 pandas.io.common
import time:      4813 |      19959 |               pandas.io.formats.format
import time:      2963 |      28490 |             pandas.core.methods.describe
import time:       647 |        647 |                   pandas._libs.window
import time:     13717 |      14363 |                 pandas._libs.window.aggregations
import time:      2920 |       2920 |                   pandas._libs.window.indexers
import time:      1563 |       4482 |                 pandas.core.indexers.objects
import time:      2132 |       2132 |                 pandas.core.util.numba_
import time:      1802 |       1802 |                 pandas.core.window.common
import time:      1920 |       1920 |                 pandas.core.window.doc
import time:      2148 |       2148 |                 pandas.core.window.numba_
import time:      1760 |       1760 |                 pandas.core.window.online
import time:      3823 |       3823 |                 pandas.core.window.rolling
import time:      5614 |      38040 |               pandas.core.window.ewm
import time:      3748 |       3748 |               pandas.core.window.expanding
import time:      2506 |      44293 |             pandas.core.window
import time:     19357 |     197867 |           pandas.core.generic
import time:      1187 |       1187 |           pandas.core.methods.selectn
import time:       836 |        836 |             pandas.core.reshape.util
import time:       865 |        865 |             pandas.core.tools.numeric
import time:      2600 |       4299 |           pandas.core.reshape.melt
import time:      6488 |       6488 |             pandas._libs.reshape
import time:      2993 |       2993 |             pandas.core.indexes.accessors
import time:      1740 |       1740 |               pandas.arrays
import time:      3464 |       5204 |             pandas.core.tools.datetimes
import time:      3084 |       3084 |             pandas.io.formats.info
import time:      2083 |       2083 |               pandas.plotting._core
import time:      1318 |       1318 |               pandas.plotting._misc
import time:      1360 |       4760 |             pandas.plotting
import time:      9445 |      31971 |           pandas.core.series
import time:     18571 |     253892 |         pandas.core.frame
import time:      4540 |       4540 |         pandas.core.groupby.base
import time:      8886 |       8886 |           pandas._libs.groupby
import time:      2491 |       2491 |             pandas.core.groupby.categorical
import time:      4160 |       6651 |           pandas.core.groupby.grouper
import time:      4006 |      19542 |         pandas.core.groupby.ops
import time:      1438 |       1438 |           pandas.core.groupby.numba_
import time:      2243 |       2243 |           pandas.core.groupby.indexing
import time:      6338 |      10018 |         pandas.core.groupby.groupby
import time:      6894 |     308682 |       pandas.core.groupby.generic
import time:      1147 |     309828 |     pandas.core.groupby
import time:      2843 |     907369 |   pandas.core.api
import time:      1154 |       1154 |   pandas.tseries.api
import time:      1230 |       1230 |           pandas.core.computation.common
import time:      1535 |       2765 |         pandas.core.computation.align
import time:      4007 |       4007 |             pprint
import time:      1962 |       5969 |           pandas.core.computation.scope
import time:      3074 |       9042 |         pandas.core.computation.ops
import time:      1963 |      13769 |       pandas.core.computation.engines
import time:      1337 |       1337 |         pandas.core.computation.parsing
import time:     42230 |      43567 |       pandas.core.computation.expr
import time:      1859 |      59194 |     pandas.core.computation.eval
import time:       888 |      60081 |   pandas.core.computation.api
import time:      1997 |       1997 |     pandas.core.reshape.encoding
import time:      3617 |       3617 |     pandas.core.reshape.merge
import time:      2948 |       2948 |     pandas.core.reshape.pivot
import time:      1585 |       1585 |     pandas.core.reshape.tile
import time:      2215 |      12361 |   pandas.core.reshape.api
import time:      1342 |       1342 |     pandas.api.extensions
import time:       691 |        691 |     pandas.api.indexers
import time:       390 |        390 |         pandas.core.interchange
import time:      4977 |       5367 |       pandas.core.interchange.dataframe_protocol
import time:      1911 |       1911 |         pandas.core.interchange.utils
import time:      2713 |       4623 |       pandas.core.interchange.from_dataframe
import time:       943 |      10933 |     pandas.api.interchange
import time:       785 |        785 |       pandas.core.dtypes.api
import time:      1405 |       2189 |     pandas.api.types
import time:      3038 |       3038 |       pandas.core.resample
import time:      2925 |       2925 |             pandas._libs.json
import time:      1594 |       1594 |             pandas.io.json._normalize
import time:      1750 |       1750 |             pandas.io.json._table_schema
import time:      8057 |       8057 |                   pandas._libs.parsers
import time:      3221 |       3221 |                     pandas.io.parsers.base_parser
import time:      1829 |       5050 |                   pandas.io.parsers.arrow_parser_wrapper
import time:      1494 |       1494 |                   pandas.io.parsers.c_parser_wrapper
import time:      1708 |       1708 |                   pandas.io.parsers.python_parser
import time:      5244 |      21551 |                 pandas.io.parsers.readers
import time:      1085 |      22636 |               pandas.io.parsers
import time:       220 |      22855 |             pandas.io.parsers.readers
import time:      6386 |      35509 |           pandas.io.json._json
import time:      1040 |      36548 |         pandas.io.json
import time:       673 |      37221 |       pandas.io.json._json
import time:      6154 |       6154 |       pandas.io.stata
import time:      1375 |      47785 |     pandas.api.typing
import time:      1640 |      64578 |   pandas.api
import time:      1653 |       1653 |         pandas._testing.contexts
import time:      1894 |       3547 |       pandas._testing._io
import time:      1653 |       1653 |       pandas._testing._warnings
import time:       135 |        135 |           cmath
import time:      3371 |       3506 |         pandas._libs.testing
import time:      2965 |       6471 |       pandas._testing.asserters
import time:      1458 |       1458 |       pandas._testing.compat
import time:      3572 |      16697 |     pandas._testing
import time:       904 |      17601 |   pandas.testing
import time:      1256 |       1256 |   pandas.util._print_versions
import time:       800 |        800 |     pandas.io.clipboards
import time:       868 |        868 |         pandas.io.excel._util
import time:      2641 |       2641 |         pandas.io.excel._calamine
import time:      2137 |       2137 |         pandas.io.excel._odfreader
import time:      2124 |       2124 |         pandas.io.excel._openpyxl
import time:      1800 |       1800 |         pandas.io.excel._pyxlsb
import time:      1033 |       1033 |         pandas.io.excel._xlrd
import time:      7526 |      18126 |       pandas.io.excel._base
import time:      3322 |       3322 |       pandas.io.excel._odswriter
import time:      2071 |       2071 |       pandas.io.excel._xlsxwriter
import time:      1200 |      24718 |     pandas.io.excel
import time:      2169 |       2169 |     pandas.io.feather_format
import time:      1142 |       1142 |     pandas.io.gbq
import time:      1809 |       1809 |     pandas.io.html
import time:       857 |        857 |     pandas.io.orc
import time:      1560 |       1560 |     pandas.io.parquet
import time:       912 |        912 |       pandas.compat.pickle_compat
import time:      1320 |       2231 |     pandas.io.pickle
import time:      2223 |       2223 |       pandas.core.computation.pytables
import time:      6796 |       9018 |     pandas.io.pytables
import time:      1496 |       1496 |       pandas.io.sas.sasreader
import time:      1151 |       2647 |     pandas.io.sas
import time:      1889 |       1889 |     pandas.io.spss
import time:      5821 |       5821 |     pandas.io.sql
import time:      2847 |       2847 |     pandas.io.xml
import time:      3027 |      60529 |   pandas.io.api
import time:      1298 |       1298 |   pandas.util._tester
import time:       859 |        859 |   pandas._version_meson
import time:      4294 |    1646245 | pandas
import time:      1764 |       1764 |             construct.lib.py3compat
import time:      2980 |       4744 |           construct.lib.containers
import time:      2494 |       2494 |           construct.lib.binary
import time:       931 |        931 |           construct.lib.bitstream
import time:      1252 |       1252 |           construct.lib.hex
import time:      2647 |      12066 |         construct.lib
import time:      1524 |       1524 |         construct.expr
import time:       557 |        557 |         construct.version
import time:     11997 |      26142 |       construct.core
import time:      1851 |       1851 |           cmd
import time:      2040 |       2040 |           bdb
import time:      3026 |       3026 |             codeop
import time:      2187 |       5213 |           code
import time:      3325 |       3325 |           glob
import time:      4134 |      16561 |         pdb
import time:      2188 |      18749 |       construct.debug
import time:      1826 |      46716 |     construct
import time:       136 |        136 |         lxml.lxml
import time:       264 |        399 |       lxml.lxml.etree
import time:       174 |        174 |       lxml.functools
import time:       256 |        256 |       lxml.types
import time:      3481 |       4308 |     lxml.builder
import time:     10406 |      10406 |         lxml.objectify
import time:      2164 |       2164 |         pykeepass.baseelement
import time:      1658 |      14228 |       pykeepass.entry
import time:      1055 |       1055 |       pykeepass.exceptions
import time:      1450 |      16731 |     pykeepass.attachment
import time:      1071 |       1071 |     pykeepass.group
import time:      1231 |       1231 |               Cryptodome
import time:      1701 |       2932 |             Cryptodome.Cipher
import time:       729 |        729 |                 Cryptodome.Util
import time:       577 |        577 |                 Cryptodome.Util.py3compat
import time:       471 |        471 |                 Cryptodome.Util._file_system
import time:      1348 |       1348 |                 ctypes.util
import time:      1916 |       5039 |               Cryptodome.Util._raw_api
import time:      3611 |       3611 |               Cryptodome.Util._cpu_features
import time:      1751 |       1751 |               Cryptodome.Random
import time:      9754 |      20154 |             Cryptodome.Cipher.AES
import time:      4446 |       4446 |             Cryptodome.Cipher.ChaCha20
import time:      4917 |       4917 |             Cryptodome.Cipher.Salsa20
import time:      2275 |       2275 |             Cryptodome.Util.Padding
import time:      3211 |       3211 |               Cryptodome.Util.strxor
import time:       488 |        488 |                 psyco
import time:      5418 |       5905 |               pykeepass.kdbx_parsing.pytwofish
import time:      2468 |      11583 |             pykeepass.kdbx_parsing.twofish
import time:      3419 |      49723 |           pykeepass.kdbx_parsing.common
import time:      2172 |      51894 |         pykeepass.kdbx_parsing.kdbx3
import time:      2167 |       2167 |             argon2.exceptions
import time:      3015 |       3015 |                 _argon2_cffi_bindings._ffi
import time:      1097 |       4111 |               _argon2_cffi_bindings
import time:      1509 |       5620 |             argon2.low_level
import time:      3623 |       3623 |               argon2._utils
import time:      2132 |       5755 |             argon2.profiles
import time:      1429 |       1429 |               argon2._password_hasher
import time:      1918 |       3347 |             argon2._legacy
import time:      3572 |      20460 |           argon2
import time:      1964 |      22423 |         pykeepass.kdbx_parsing.kdbx4
import time:      2940 |      77257 |       pykeepass.kdbx_parsing.kdbx
import time:      1337 |      78593 |     pykeepass.kdbx_parsing
import time:      1491 |       1491 |     pykeepass.xpath
import time:       849 |        849 |     pykeepass.deprecated
import time:      3459 |     153216 |   pykeepass.pykeepass
import time:       919 |        919 |   pykeepass.icons
import time:      2295 |       2295 |   pykeepass.version
import time:      2108 |     158537 | pykeepass
import time:      2015 |       2015 |     rich._extension
import time:      3323 |       5338 |   rich
import time:       729 |        729 |           rich._cell_widths
import time:      2009 |       2737 |         rich.cells
import time:      1791 |       1791 |         rich.repr
import time:      1563 |       1563 |           rich.errors
import time:      1833 |       1833 |             colorsys
import time:       976 |        976 |                 rich.color_triplet
import time:      1120 |       2096 |               rich.palette
import time:      1020 |       3115 |             rich._palettes
import time:      1007 |       1007 |             rich.terminal_theme
import time:      3783 |       9737 |           rich.color
import time:      3828 |      15127 |         rich.style
import time:      8709 |      28363 |       rich.segment
import time:      1486 |      29848 |     rich.jupyter
import time:      1624 |       1624 |       rich.protocol
import time:      2304 |       3928 |     rich.measure
import time:      1420 |      35195 |   rich.constrain
import time:      2752 |      43284 | rich.align
import time:       980 |        980 |   rich._ratio
import time:       925 |        925 |       termios
import time:      3453 |       4378 |     getpass
import time:      1740 |       1740 |     rich._null_file
import time:      1790 |       1790 |       rich.default_styles
import time:      1806 |       1806 |       rich.theme
import time:      1548 |       5143 |     rich.themes
import time:      3633 |       3633 |       rich._emoji_codes
import time:      1296 |       4928 |     rich._emoji_replace
import time:      1092 |       1092 |     rich._export_format
import time:      2076 |       2076 |     rich._fileno
import time:      1781 |       1781 |         rich._loop
import time:      1910 |       1910 |         rich._pick
import time:      1553 |       1553 |         rich._wrap
import time:      1519 |       1519 |         rich.containers
import time:      1349 |       1349 |         rich.control
import time:      1891 |       1891 |         rich.emoji
import time:      6187 |      16188 |       rich.text
import time:      2661 |      18849 |     rich._log_render
import time:      1942 |       1942 |     rich.highlighter
import time:      2594 |       2594 |     rich.markup
import time:      1078 |       1078 |     rich.pager
import time:       926 |        926 |       array
import time:      1295 |       1295 |       rich.abc
import time:      6302 |       8523 |     rich.pretty
import time:      2477 |       2477 |     rich.region
import time:      1366 |       1366 |         rich.box
import time:      1189 |       1189 |         rich.padding
import time:      1573 |       4127 |       rich.panel
import time:      3133 |       3133 |       rich.table
import time:      1919 |       9179 |     rich.scope
import time:      1212 |       1212 |     rich.screen
import time:      1631 |       1631 |     rich.styled
import time:     10932 |      77765 |   rich.console
import time:      3295 |      82039 | rich.layout
import time:      1902 |       1902 |     rich.ansi
import time:      1485 |       3386 |   rich.file_proxy
import time:      1322 |       1322 |   rich.live_render
import time:      2624 |       7331 | rich.live
import time:      1653 |       1653 |   rich.filesize
import time:      2023 |       2023 |   rich.progress_bar
import time:      1441 |       1441 |     rich._spinners
import time:      1843 |       3283 |   rich.spinner
import time:      7146 |      14103 | rich.progress
import time:      2465 |       2465 | rich.prompt
import time:      2125 |       2125 |     sqlalchemy.dialects.postgresql.array
import time:      1260 |       1260 |       sqlalchemy.dialects.postgresql.ext
import time:      4033 |       5292 |     sqlalchemy.dialects.postgresql.dml
import time:      4656 |       4656 |     sqlalchemy.dialects.postgresql.hstore
import time:      2349 |       2349 |     sqlalchemy.dialects.postgresql.json
import time:      1906 |       1906 |     sqlalchemy.dialects.postgresql.ranges
import time:      8965 |      25291 |   sqlalchemy.dialects.postgresql.base
import time:      2231 |       2231 |   sqlalchemy.dialects.postgresql.pg8000
import time:      2547 |       2547 |   sqlalchemy.dialects.postgresql.psycopg2
import time:      2106 |       2106 |   sqlalchemy.dialects.postgresql.psycopg2cffi
import time:      2495 |       2495 |   sqlalchemy.dialects.postgresql.pygresql
import time:      1971 |       1971 |   sqlalchemy.dialects.postgresql.pypostgresql
import time:      4772 |       4772 |   sqlalchemy.dialects.postgresql.asyncpg
import time:      5141 |      46550 | sqlalchemy.dialects.postgresql
import time:       818 |        818 |   sqlalchemy.ext
import time:      1571 |       1571 |     sqlalchemy.ext.asyncio.exc
import time:      1819 |       1819 |     sqlalchemy.ext.asyncio.base
import time:      1912 |       1912 |     sqlalchemy.ext.asyncio.result
import time:      1162 |       1162 |       sqlalchemy.future.engine
import time:      1367 |       2529 |     sqlalchemy.future
import time:      5232 |      13061 |   sqlalchemy.ext.asyncio.engine
import time:      2636 |       2636 |         sqlalchemy.orm.exc
import time:      1783 |       1783 |               sqlalchemy.orm.base
import time:      2899 |       4682 |             sqlalchemy.orm.collections
import time:      2478 |       2478 |               sqlalchemy.orm.path_registry
import time:      3480 |       5958 |             sqlalchemy.orm.interfaces
import time:      3869 |      14508 |           sqlalchemy.orm.attributes
import time:      1893 |       1893 |             sqlalchemy.orm.state
import time:      2902 |       4794 |           sqlalchemy.orm.instrumentation
import time:      1626 |       1626 |               sqlalchemy.orm.util
import time:      2360 |       3986 |             sqlalchemy.orm.strategy_options
import time:      1935 |       5921 |           sqlalchemy.orm.loading
import time:      3755 |       3755 |             sqlalchemy.orm.descriptor_props
import time:      3802 |       3802 |             sqlalchemy.orm.relationships
import time:      3074 |      10630 |           sqlalchemy.orm.properties
import time:      7125 |      42977 |         sqlalchemy.orm.mapper
import time:      2526 |       2526 |         sqlalchemy.orm.context
import time:      3096 |       3096 |           sqlalchemy.orm.clsregistry
import time:      3305 |       3305 |           sqlalchemy.orm.decl_base
import time:      4197 |      10597 |         sqlalchemy.orm.decl_api
import time:      1608 |       1608 |         sqlalchemy.orm.identity
import time:     15774 |      15774 |         sqlalchemy.orm.query
import time:      2199 |       2199 |               sqlalchemy.orm.evaluator
import time:      1918 |       1918 |               sqlalchemy.orm.sync
import time:      4532 |       8648 |             sqlalchemy.orm.persistence
import time:      2333 |       2333 |             sqlalchemy.orm.unitofwork
import time:      4602 |      15582 |           sqlalchemy.orm.session
import time:     11800 |      27382 |         sqlalchemy.orm.scoping
import time:     11755 |      11755 |         sqlalchemy.orm.events
import time:      4035 |       4035 |           sqlalchemy.orm.strategies
import time:      2825 |       6860 |         sqlalchemy.orm.dynamic
import time:      2516 |       2516 |         sqlalchemy.orm.dependency
import time:      6315 |     130941 |       sqlalchemy.orm
import time:      6081 |     137022 |     sqlalchemy.ext.asyncio.session
import time:      1481 |     138503 |   sqlalchemy.ext.asyncio.events
import time:      9270 |       9270 |   sqlalchemy.ext.asyncio.scoping
import time:      2573 |     164224 | sqlalchemy.ext.asyncio
import time:      2924 |       2924 |       sqlalchemy.ext.declarative.extensions
import time:      2896 |       5820 |     sqlalchemy.ext.declarative
import time:      5740 |       5740 |     sqlalchemy_utils.compat
import time:       752 |        752 |           sqlalchemy_utils.utils
import time:      3305 |       3305 |             sqlalchemy.ext.hybrid
import time:      2106 |       5411 |           sqlalchemy_utils.functions.orm
import time:      1902 |       8064 |         sqlalchemy_utils.functions.database
import time:      1442 |       1442 |           sqlalchemy_utils.query_chain
import time:      2764 |       4206 |         sqlalchemy_utils.functions.foreign_keys
import time:      1352 |       1352 |         sqlalchemy_utils.functions.mock
import time:       860 |        860 |         sqlalchemy_utils.functions.render
import time:      1049 |       1049 |         sqlalchemy_utils.functions.sort_query
import time:      2216 |      17745 |       sqlalchemy_utils.functions
import time:       297 |      18041 |     sqlalchemy_utils.functions.orm
import time:       811 |        811 |       sqlalchemy_utils.relationships.chained_join
import time:      1481 |       2291 |     sqlalchemy_utils.relationships
import time:      2371 |      34261 |   sqlalchemy_utils.aggregates
import time:       786 |        786 |   sqlalchemy_utils.asserts
import time:      1152 |       1152 |   sqlalchemy_utils.exceptions
import time:      1375 |       1375 |     sqlalchemy.ext.compiler
import time:      3090 |       4464 |   sqlalchemy_utils.expressions
import time:      2836 |       2836 |   sqlalchemy_utils.generic
import time:      1643 |       1643 |         babel.localedata
import time:      3616 |       3616 |         babel.plural
import time:      2845 |       8102 |       babel.core
import time:      1290 |       9392 |     babel
import time:      1196 |       1196 |           babel.localtime._helpers
import time:     11035 |      12230 |         babel.localtime._win32
import time:      2312 |       2312 |         babel.localtime._fallback
import time:     98418 |     112959 |       babel.localtime
import time:      3761 |     116720 |     babel.dates
import time:      2250 |     128361 |   sqlalchemy_utils.i18n
import time:      3145 |       3145 |   sqlalchemy_utils.listeners
import time:      2024 |       2024 |   sqlalchemy_utils.models
import time:      1163 |       1163 |     sqlalchemy_utils.path
import time:      1645 |       2807 |   sqlalchemy_utils.observer
import time:      1224 |       1224 |     sqlalchemy_utils.primitives.country
import time:      1217 |       1217 |     sqlalchemy_utils.primitives.currency
import time:      2465 |       2465 |     sqlalchemy_utils.primitives.ltree
import time:      3736 |       3736 |     sqlalchemy_utils.primitives.weekday
import time:      1584 |       1584 |     sqlalchemy_utils.primitives.weekdays
import time:      2142 |      12366 |   sqlalchemy_utils.primitives
import time:      1156 |       1156 |   sqlalchemy_utils.proxy_dict
import time:       642 |        642 |           arrow
import time:      1549 |       2190 |         sqlalchemy_utils.types.enriched_datetime.arrow_datetime
import time:       867 |        867 |             pendulum
import time:      1575 |       2442 |           sqlalchemy_utils.types.enriched_datetime.pendulum_datetime
import time:      1103 |       1103 |           pendulum
import time:      1694 |       5238 |         sqlalchemy_utils.types.enriched_datetime.pendulum_date
import time:      1375 |       8802 |       sqlalchemy_utils.types.enriched_datetime
import time:      1453 |       1453 |         sqlalchemy_utils.types.scalar_coercible
import time:      2488 |       3940 |       sqlalchemy_utils.types.enriched_datetime.enriched_datetime_type
import time:       913 |        913 |       arrow
import time:      2320 |      15974 |     sqlalchemy_utils.types.arrow
import time:      1855 |       1855 |     sqlalchemy_utils.types.choice
import time:       403 |        403 |       colour
import time:      1477 |       1880 |     sqlalchemy_utils.types.color
import time:       821 |        821 |     sqlalchemy_utils.types.country
import time:      1884 |       1884 |     sqlalchemy_utils.types.currency
import time:      1785 |       1785 |       sqlalchemy_utils.operators
import time:      3101 |       4885 |     sqlalchemy_utils.types.email
import time:       953 |        953 |       sqlalchemy_utils.types.encrypted
import time:      1094 |       1094 |       sqlalchemy_utils.types.encrypted.padding
import time:      1224 |       1224 |       sqlalchemy_utils.types.json
import time:       490 |        490 |       cryptography
import time:      2959 |       6719 |     sqlalchemy_utils.types.encrypted.encrypted_type
import time:       860 |        860 |     sqlalchemy_utils.types.enriched_datetime.enriched_date_type
import time:       952 |        952 |     sqlalchemy_utils.types.ip_address
import time:      2393 |       2393 |     sqlalchemy_utils.types.locale
import time:      2442 |       2442 |     sqlalchemy_utils.types.ltree
import time:      5669 |       5669 |         sqlalchemy.dialects.oracle.base
import time:      2477 |       2477 |         sqlalchemy.dialects.oracle.cx_oracle
import time:      1548 |       9694 |       sqlalchemy.dialects.oracle
import time:      1774 |       1774 |           sqlalchemy.dialects.sqlite.json
import time:      6646 |       8420 |         sqlalchemy.dialects.sqlite.base
import time:      1619 |       1619 |           sqlalchemy.dialects.sqlite.pysqlite
import time:      1672 |       3291 |         sqlalchemy.dialects.sqlite.pysqlcipher
import time:      2843 |       2843 |         sqlalchemy.dialects.sqlite.dml
import time:      2292 |       2292 |         sqlalchemy.dialects.sqlite.aiosqlite
import time:      3195 |      20039 |       sqlalchemy.dialects.sqlite
import time:      3068 |       3068 |       sqlalchemy.ext.mutable
import time:      1964 |       1964 |       passlib
import time:      1212 |       1212 |         passlib.exc
import time:      1035 |       1035 |                 passlib.utils.compat
import time:      2369 |       2369 |                 stringprep
import time:      2751 |       2751 |                 timeit
import time:      1812 |       1812 |                   passlib.utils.decor
import time:      3402 |       5213 |                 passlib.utils.binary
import time:       424 |        424 |                   _crypt
import time:      2040 |       2464 |                 crypt
import time:      2236 |      16067 |               passlib.utils
import time:       137 |      16204 |             passlib.utils.decor
import time:      1077 |      17280 |           passlib.ifc
import time:      1634 |      18914 |         passlib.registry
import time:      4257 |       4257 |         passlib.utils.handlers
import time:      4199 |      28581 |       passlib.context
import time:      3264 |      66607 |     sqlalchemy_utils.types.password
import time:      2488 |       2488 |           psycopg2.errors
import time:     19100 |      21588 |         psycopg2._psycopg
import time:      1643 |       1643 |           psycopg2._json
import time:      4795 |       4795 |           psycopg2._range
import time:      3169 |       9607 |         psycopg2.extensions
import time:      2705 |      33899 |       psycopg2
import time:      1166 |       1166 |         psycopg2._ipaddress
import time:      4026 |       5192 |       psycopg2.extras
import time:      2311 |      41401 |     sqlalchemy_utils.types.pg_composite
import time:       445 |        445 |       phonenumbers
import time:      1287 |       1731 |     sqlalchemy_utils.types.phone_number
import time:       370 |        370 |       intervals
import time:      1645 |       2014 |     sqlalchemy_utils.types.range
import time:      2171 |       2171 |     sqlalchemy_utils.types.scalar_list
import time:      2383 |       2383 |     sqlalchemy_utils.types.timezone
import time:      1716 |       1716 |     sqlalchemy_utils.types.ts_vector
import time:       660 |        660 |       furl
import time:      1785 |       2445 |     sqlalchemy_utils.types.url
import time:      8123 |       8123 |           sqlalchemy.dialects.mssql.information_schema
import time:      1604 |       1604 |           sqlalchemy.dialects.mssql.json
import time:      8099 |      17826 |         sqlalchemy.dialects.mssql.base
import time:      1913 |       1913 |               sqlalchemy.connectors
import time:      1861 |       3774 |             sqlalchemy.connectors.pyodbc
import time:      2541 |       6315 |           sqlalchemy.dialects.mssql.pyodbc
import time:      2013 |       2013 |           sqlalchemy.connectors.mxodbc
import time:      2046 |      10373 |         sqlalchemy.dialects.mssql.mxodbc
import time:      1849 |       1849 |         sqlalchemy.dialects.mssql.pymssql
import time:      3091 |      33136 |       sqlalchemy.dialects.mssql
import time:      1659 |      34795 |     sqlalchemy_utils.types.uuid
import time:      1312 |       1312 |       sqlalchemy_utils.types.bit
import time:      1565 |       2876 |     sqlalchemy_utils.types.weekdays
import time:      5346 |     204142 |   sqlalchemy_utils.types
import time:      2081 |       2081 |   sqlalchemy_utils.view
import time:      4498 |     404072 | sqlalchemy_utils
import time:       618 |        618 |   pwd
import time:      7045 |       7045 |   psutil._common
import time:     10723 |      10723 |     psutil._psutil_windows
import time:      4309 |      15031 |   psutil._pswindows
import time:      6022 |      28715 | psutil
import time:      1014 |       1014 |       dags
import time:      1125 |       2138 |     dags.data_pipeline
import time:       953 |       3091 |   dags.data_pipeline.database
import time:      1248 |       1248 |       asyncpg.compat
import time:      1581 |       1581 |           asyncpg.exceptions._base
import time:      5278 |       6858 |         asyncpg.exceptions
import time:       626 |        626 |               asyncpg.pgproto
import time:      4653 |       4653 |               asyncpg.pgproto.types
import time:      6156 |      11434 |             asyncpg.pgproto.pgproto
import time:      2121 |       2121 |             backports_abc
import time:      1922 |       1922 |             asyncpg.types
import time:     12502 |      27976 |           asyncpg.protocol.protocol
import time:      1949 |      29925 |         asyncpg.protocol
import time:      4442 |      41224 |       asyncpg.connect_utils
import time:      1544 |       1544 |         asyncpg.connresource
import time:      3720 |       5263 |       asyncpg.cursor
import time:      1647 |       1647 |       asyncpg.introspection
import time:      2055 |       2055 |       asyncpg.prepared_stmt
import time:      1660 |       1660 |       asyncpg.serverversion
import time:      1008 |       1008 |       asyncpg.transaction
import time:       675 |        675 |       asyncpg.utils
import time:      5535 |      60311 |     asyncpg.connection
import time:      2099 |       2099 |     asyncpg.pool
import time:      2136 |       2136 |     asyncpg._version
import time:      3630 |      68175 |   asyncpg
import time:      2803 |       2803 |       sqlalchemy.testing.config
import time:      1678 |       1678 |               unittest.util
import time:      2729 |       4407 |             unittest.result
import time:      3246 |       3246 |               difflib
import time:      5367 |       8612 |             unittest.case
import time:      1957 |       1957 |             unittest.suite
import time:      2457 |       2457 |             unittest.loader
import time:      1764 |       1764 |                 unittest.signals
import time:      2550 |       4314 |               unittest.runner
import time:      2074 |       6388 |             unittest.main
import time:      2793 |      26612 |           unittest
import time:      6280 |      32892 |         unittest.mock
import time:      1822 |      34713 |       sqlalchemy.testing.mock
import time:      1729 |       1729 |         sqlalchemy.testing.assertsql
import time:      1468 |       1468 |           sqlalchemy.testing.util
import time:      1560 |       3028 |         sqlalchemy.testing.engines
import time:      2817 |       2817 |         sqlalchemy.testing.exclusions
import time:      3964 |      11537 |       sqlalchemy.testing.assertions
import time:      1508 |       1508 |       sqlalchemy.testing.schema
import time:      1511 |       1511 |       sqlalchemy.testing.warnings
import time:      3314 |      55384 |     sqlalchemy.testing
import time:       282 |      55665 |   sqlalchemy.testing.exclusions
import time:      1073 |       1073 |     dags.data_pipeline.dataframe_utils
import time:      1517 |       1517 |     concurrent.futures.thread
import time:      2849 |       2849 |           multiprocessing.process
import time:      2276 |       2276 |           multiprocessing.reduction
import time:      5578 |      10702 |         multiprocessing.context
import time:      2045 |      12747 |       multiprocessing
import time:      2013 |       2013 |         _multiprocessing
import time:      2998 |       2998 |         multiprocessing.util
import time:      2867 |       7877 |       multiprocessing.connection
import time:      3073 |       3073 |       multiprocessing.queues
import time:      2704 |      26400 |     concurrent.futures.process
import time:      2339 |       2339 |       logging.handlers
import time:       971 |        971 |             pygments
import time:      7581 |       7581 |             pygments.lexers._mapping
import time:      2438 |       2438 |             pygments.modeline
import time:      1666 |       1666 |             pygments.plugin
import time:      1747 |       1747 |             pygments.util
import time:      2702 |      17103 |           pygments.lexers
import time:      1447 |       1447 |           pygments.token
import time:      1205 |       1205 |           rich.columns
import time:      1017 |       1017 |               pygments.filter
import time:      2535 |       2535 |               pygments.filters
import time:      1116 |       1116 |               pygments.regexopt
import time:      2814 |       7481 |             pygments.lexer
import time:      1653 |       1653 |             pygments.style
import time:      1341 |       1341 |               pygments.styles._mapping
import time:      1442 |       2783 |             pygments.styles
import time:      5570 |      17485 |           rich.syntax
import time:     10019 |      47257 |         rich.traceback
import time:      1453 |      48710 |       rich.logging
import time:      2203 |      53251 |     dags.data_pipeline.custom_logger
import time:      7647 |      89886 |   dags.data_pipeline.dataframe_utils.dataframe_debugger
import time:      1815 |       1815 |           citext
import time:      2323 |       4138 |         dags.data_pipeline.dbmodels.base
import time:     14523 |      18661 |       dags.data_pipeline.dbmodels.user
import time:      2110 |       2110 |           sqlalchemy_json.track
import time:      2350 |       4460 |         sqlalchemy_json
import time:     22197 |      26656 |       dags.data_pipeline.dbmodels.issue
import time:      7010 |       7010 |       dags.data_pipeline.dbmodels.initiativeattribute
import time:      6084 |       6084 |       dags.data_pipeline.dbmodels.issueclassification
import time:      2828 |       2828 |       dags.data_pipeline.dbmodels.gs_jira_issues
import time:      9875 |       9875 |       dags.data_pipeline.dbmodels.user_addons
import time:      1200 |       1200 |       dags.data_pipeline.dbmodels.mixins
import time:      3790 |       3790 |                 sqlalchemy.dialects.mysql.types
import time:      1686 |       5476 |               sqlalchemy.dialects.mysql.enumerated
import time:      1882 |       7358 |             sqlalchemy.dialects.mysql.reflection
import time:      2262 |       2262 |             sqlalchemy.dialects.mysql.json
import time:      2025 |       2025 |             sqlalchemy.dialects.mysql.reserved_words
import time:      6998 |      18641 |           sqlalchemy.dialects.mysql.base
import time:      1355 |       1355 |             sqlalchemy.dialects.mysql.mysqldb
import time:      1255 |       2610 |           sqlalchemy.dialects.mysql.cymysql
import time:      1635 |       1635 |           sqlalchemy.dialects.mysql.mariadbconnector
import time:      4079 |       4079 |           sqlalchemy.dialects.mysql.mysqlconnector
import time:      1977 |       1977 |           sqlalchemy.dialects.mysql.oursql
import time:      2624 |       2624 |           sqlalchemy.dialects.mysql.pymysql
import time:      2000 |       2000 |           sqlalchemy.dialects.mysql.pyodbc
import time:      1768 |       1768 |           sqlalchemy.dialects.mysql.dml
import time:      1161 |       1161 |           sqlalchemy.dialects.mysql.expression
import time:      3086 |       3086 |           sqlalchemy.dialects.mysql.aiomysql
import time:      2947 |       2947 |           sqlalchemy.dialects.mysql.asyncmy
import time:      4416 |      46940 |         sqlalchemy.dialects.mysql
import time:      6722 |      53661 |       dags.data_pipeline.dbmodels.worklog
import time:      4466 |       4466 |       dags.data_pipeline.dbmodels.versions
import time:     19544 |      19544 |       dags.data_pipeline.dbmodels.task_monitoring
import time:      4538 |     154517 |     dags.data_pipeline.dbmodels
import time:       298 |     154815 |   dags.data_pipeline.dbmodels.base
import time:      3328 |     374958 | dags.data_pipeline.database.upsert_operations
import time:      1015 |       1015 |   dags.data_pipeline.utilities
import time:      1059 |       1059 |     passlib.handlers
import time:       557 |        557 |       passlib.crypto
import time:       733 |        733 |       fastpbkdf2
import time:      2550 |       3839 |     passlib.crypto.digest
import time:      5513 |      10410 |   passlib.handlers.argon2
import time:      3362 |      14786 | dags.data_pipeline.utilities.security
import time:      9932 |       9932 | dags.data_pipeline.dbmodels.log_entry
import time:       761 |        761 |       dbmodels.base
import time:     12061 |      12821 |     dbmodels.user
import time:     17490 |      17490 |     dbmodels.issue
import time:      2099 |       2099 |     dbmodels.initiativeattribute
import time:      4264 |       4264 |     dbmodels.issueclassification
import time:      4484 |       4484 |     dbmodels.gs_jira_issues
import time:      5027 |       5027 |     dbmodels.user_addons
import time:       968 |        968 |     dbmodels.mixins
import time:      8083 |       8083 |     dbmodels.worklog
import time:      4022 |       4022 |     dbmodels.versions
import time:     12486 |      12486 |     dbmodels.task_monitoring
import time:      3919 |      75657 |   dbmodels
import time:       725 |      76381 | dbmodels.base
import time:      5170 |       5170 | dbmodels.allboards
import time:      2117 |       2117 |   sqlalchemy.ext.indexable
import time:     76547 |      78664 | dbmodels.changelog
import time:      8040 |       8040 | dbmodels.sprint
import time:      4829 |       4829 | dbmodels.rundetails
import time:      2933 |       2933 |     socketserver
import time:      5567 |       8500 |   logging.config
import time:      5942 |       5942 |   dags.data_pipeline.progress_manager
import time:       715 |        715 |     dags.data_pipeline.utils
import time:      3282 |       3996 |   dags.data_pipeline.utils.circuit_breaker
import time:      1500 |       1500 |   dags.data_pipeline.utils.shutdown_handler
import time:    149127 |     169062 | containers
import time:      4814 |       4814 | priority_queue_system
import time:       937 |        937 |   dags.data_pipeline.jira
import time:    157201 |     157201 |   dags.data_pipeline.containers
import time:      1846 |       1846 |     dags.data_pipeline.debug
import time:      3488 |       5334 |   dags.data_pipeline.debug.debug_utils
import time:     89466 |     252935 | dags.data_pipeline.jira.api_client
import time:      3743 |       3743 |   markdownify
import time:     54998 |      54998 |   dags.data_pipeline.field_mappers
import time:     51466 |      51466 |     field_mappers
import time:      4103 |      55569 |   dags.data_pipeline.data_type_handlers
import time:      6457 |       6457 |     dags.data_pipeline.dbmodels.changelog
import time:      1958 |       1958 |     dags.data_pipeline.database.get_model_by_name
import time:      4652 |      13066 |   dags.data_pipeline.specialized_field_mappers
import time:      3076 |       3076 |   dags.data_pipeline.priority_queue_system
import time:      4134 |     134584 | dags.data_pipeline.queue_processors
import time:      2071 |       2071 |   rich._win32_console
import time:      2222 |       4293 | rich._windows
import time:      1975 |       1975 | dags.data_pipeline.logging_utils
import time:      3209 |       3209 |       Cryptodome.Cipher._mode_ecb
import time:  -6026270 |       4059 |     importlib.resources._itertools
import time:      1988 |       6046 |   importlib.resources.readers
import time:      1368 |       7414 | importlib.readers
import time:      5567 |       5567 | Cryptodome.Cipher._mode_cbc
