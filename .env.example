# =============================================================================
# JIRA Data Pipeline Environment Configuration
# =============================================================================
# Copy this file to .env and update the values for your environment
# Sensitive values like passwords should be managed through KeePass

# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================
ENVIRONMENT=development
DEBUG_MODE=true
APP_NAME=jira-data-pipeline
APP_VERSION=1.0.0

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Database Connection
DB_HOST=localhost
DB_PORT_RW=5432
DB_PORT_RO=5433
DB_NAME=alembic_migration
DB_DRIVER=postgresql+psycopg2

# Database Pool Settings
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=10
DB_POOL_RECYCLE=3600
DB_POOL_TIMEOUT=60
DB_POOL_PRE_PING=true

# Connection Settings
DB_CONNECT_TIMEOUT=30
DB_KEEPALIVES=1
DB_KEEPALIVES_IDLE=30
DB_KEEPALIVES_INTERVAL=10
DB_KEEPALIVES_COUNT=5

# Schema Configuration
DB_PUBLIC_SCHEMA=public
DB_CUSTOM_SCHEMAS=plat,plp,acq
DB_DEFAULT_SCHEMA=plat

# =============================================================================
# KEEPASS CONFIGURATION
# =============================================================================
# KeePass Database Paths (platform-specific)
KEEPASS_DB_PATH=C:/KeePass/Database.kdbx
KEEPASS_KEY_PATH=C:/KeePass/Database.key
KEEPASS_JIRA_ENTRY=corecard Jira

# =============================================================================
# JIRA API CONFIGURATION
# =============================================================================
# JIRA Connection
JIRA_BASE_URL=https://corecard.atlassian.net
JIRA_ACCEPT_HEADER=application/json
JIRA_CONTENT_TYPE=application/json

# JIRA Rate Limiting & Retry Logic
JIRA_MAX_RETRIES=5
JIRA_INITIAL_RETRY_DELAY=5000
JIRA_MAX_RETRY_DELAY=10000
JIRA_JITTER_MIN=0.5
JIRA_JITTER_MAX=1.5

# =============================================================================
# QUEUE CONFIGURATION
# =============================================================================
# Queue Sizes for Different Data Types
QUEUE_ISSUES_SIZE=1000
QUEUE_STATS_SIZE=500
QUEUE_ISSUE_SIZE=500
QUEUE_CHANGELOG_SIZE=500
QUEUE_WORKLOG_SIZE=500
QUEUE_COMMENT_SIZE=500
QUEUE_ISSUE_LINKS_SIZE=500
QUEUE_ISSUE_STAGING_SIZE=500

# =============================================================================
# CIRCUIT BREAKER CONFIGURATION
# =============================================================================
# Circuit Breaker Settings
CB_FAILURE_THRESHOLD=5
CB_RECOVERY_TIMEOUT=30.0
CB_HALF_OPEN_MAX_CALLS=3
CB_MAX_CONSECUTIVE_RATE_LIMITS=3
CB_CONNECTION_POOL_RETRY_MULTIPLIER=2.0
CB_NETWORK_ERROR_RETRY_MULTIPLIER=1.5
CB_MAX_CONCURRENT_CONNECTIONS=50
CB_HEALTH_CHECK_INTERVAL=10.0

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
# Logging Files and Levels
LOGGING_CONFIG_PATH=logging_config.yaml
LOG_LEVEL=INFO
LOG_FILE_LEVEL=DEBUG
LOG_CONSOLE_LEVEL=WARNING

# Database Logging
DB_LOG_BATCH_SIZE=50
DB_LOG_FLUSH_INTERVAL=30
DB_LOG_ENABLED=true

# Debug Monitoring
ENABLE_DEBUG_MONITOR=true
DEBUG_MONITOR_INTERVAL=30
DEBUG_QUEUE_SIZE_WARNING=100
DEBUG_SLOW_OP_THRESHOLD=5.0
DEBUG_MEMORY_WARNING=80.0
DEBUG_LOG_SUPPRESSION=300

# =============================================================================
# FILE PATHS
# =============================================================================
# Configuration Files
ISSUE_FIELDS_YAML_FILE=./dags/data_pipeline/issue_fields.yaml
CONFIG_YAML_PATH=./dags/data_pipeline/config.yaml

# Airflow Configuration
AIRFLOW_HOME=/opt/airflow

# =============================================================================
# DATABASE EXTENSIONS
# =============================================================================
# PostgreSQL Extensions to Install
DB_EXTENSIONS=citext,ltree,intarray,hstore,btree_gist,pg_trgm

# =============================================================================
# MONITORING & PERFORMANCE
# =============================================================================
# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_LOG_INTERVAL=60
MEMORY_THRESHOLD_WARNING_GB=2.0
CPU_THRESHOLD_WARNING_PERCENT=80.0

# Async Task Monitoring
ASYNC_TASK_MONITORING=true
ASYNC_TASK_LOG_INTERVAL=30

# =============================================================================
# TESTING CONFIGURATION
# =============================================================================
# Test Database Settings (for pytest)
TEST_DB_HOST=localhost
TEST_DB_PORT=5432
TEST_DB_NAME=test_jira
TEST_DB_USER=test_user
TEST_DB_PASSWORD=test_password

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Development-specific settings
DEV_ENABLE_SQL_ECHO=false
DEV_ENABLE_POOL_ECHO=false
DEV_ENABLE_RICH_TRACEBACKS=true
DEV_LOG_SQL_QUERIES=false
