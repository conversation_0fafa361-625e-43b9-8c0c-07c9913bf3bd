# Foreign Key Error Handling Improvements

## Overview
Enhanced error handling for ForeignKeyViolationError and reduced verbose stack traces in RetryableAsyncSession. The improvements focus on logging key information for retry purposes with clear, greppable markers.

## Changes Made

### 1. Enhanced Imports (`upsert_operations.py` & `utility_code.py`)

**Added imports for specific foreign key violation exceptions:**
```python
from asyncpg.exceptions import ForeignKeyViolationError as AsyncpgForeignKeyViolationError
from psycopg2.errors import ForeignKeyViolation as Psycopg2ForeignKeyViolation
```

### 2. Key Information Extraction Function (`upsert_operations.py`)

**Added `_extract_key_info_for_retry()` function:**
- Extracts key columns (`key`, `issue_key`, `id`, `issue_id`) from DataFrames
- Provides concise, greppable logging format: `RETRY_NEEDED: ModelName - key_column: [values] - record_count records`
- Handles edge cases: empty DataFrames, missing key columns, large datasets (truncates after 10 values)
- Returns fallback information when key columns aren't available

**Example outputs:**
```
RETRY_NEEDED: Issue - key: [PROJ-123, PROJ-124, PROJ-125] - 3 records
RETRY_NEEDED: WorkLog - issue_key: [PROJ-456, PROJ-457] - 2 records
RETRY_NEEDED: User - id: [10, 11, 12, 13, 14] - 5 records
RETRY_NEEDED: TestModel - No key column found, columns: ['name', 'description'] - 1 records
```

### 3. Enhanced upsert_async Error Handling (`upsert_operations.py`)

**Added specific foreign key violation handling:**
```python
except (AsyncpgForeignKeyViolationError, Psycopg2ForeignKeyViolation) as fk_err:
    # Handle foreign key violations specifically
    if isinstance(my_logger, Logger):
        key_info = _extract_key_info_for_retry(rows, model.__name__ if model else None)
        my_logger.error(f"Foreign key violation in upsert_async: {key_info}")
        my_logger.debug(f"Foreign key violation details: {type(fk_err).__name__}: {fk_err}")
    return False

except (IntegrityError, PendingRollbackError) as e:
    # Check if this is a foreign key violation wrapped in IntegrityError
    if hasattr(e, 'orig') and e.orig:
        if isinstance(e.orig, (AsyncpgForeignKeyViolationError, Psycopg2ForeignKeyViolation)):
            if isinstance(my_logger, Logger):
                key_info = _extract_key_info_for_retry(rows, model.__name__ if model else None)
                my_logger.error(f"Foreign key violation (wrapped) in upsert_async: {key_info}")
                my_logger.debug(f"Foreign key violation details: {type(e.orig).__name__}: {e.orig}")
            return False
```

### 4. Enhanced _process_single_model Error Handling (`utility_code.py`)

**Added foreign key violation handling with try-catch wrapper:**
- Catches both direct foreign key violations and wrapped IntegrityErrors
- Logs key information using `_extract_key_info_for_retry()`
- Provides clear error messages for retry processing

### 5. Enhanced _process_issue_hierarchy_levels Error Handling (`utility_code.py`)

**Added foreign key violation handling for Issue hierarchy processing:**
- Same pattern as `_process_single_model` but with hierarchy level context
- Logs level name (Initiative, Epic, Story, Subtask) in error messages
- Maintains referential integrity error handling for parent-child relationships

### 6. Reduced Verbose Logging in RetryableAsyncSession (`progress_manager.py`)

**Enhanced `_log_retry()` method:**
```python
def _log_retry(self, exc: Exception, attempt: int):
    try:
        logger = self._manager.logger if getattr(self, "_manager", None) else logging.getLogger(__name__)
        
        # Check if this is a connection-related error that should be logged minimally
        exc_str = str(exc).lower()
        if any(keyword in exc_str for keyword in ['connection', 'closed', 'transaction', 'context manager']):
            # For connection/transaction errors, log minimal info without stack trace
            logger.warning(f"[RetryableAsyncSession] retrying statement after connection error (attempt {attempt}): {type(exc).__name__}")
            logger.debug(f"[RetryableAsyncSession] connection error details: {exc!r}")
        else:
            # For other errors, log full details
            logger.warning(f"[RetryableAsyncSession] retrying statement after error (attempt {attempt}): {exc!r}")
    except Exception:
        pass
```

**Added `_extract_retry_info_from_statement()` method:**
- Extracts key information from statement parameters
- Handles batch parameters (list of dicts)
- Provides retry-friendly logging format
- Fallback handling for different parameter types

**Enhanced execute() method:**
- Logs key information for connection/transaction errors
- Reduces stack trace noise while maintaining debugging capability
- Uses `RETRY_NEEDED` format for consistency

## Benefits

### 1. **Greppable Error Logs**
All foreign key violations and retry-needed scenarios use consistent `RETRY_NEEDED:` prefix for easy searching:
```bash
grep "RETRY_NEEDED" logfile.log
grep "Foreign key violation" logfile.log
```

### 2. **Reduced Log Noise**
- Connection/transaction errors log minimal information at WARNING level
- Full stack traces moved to DEBUG level
- Key information extracted for retry processing

### 3. **Better Error Context**
- Model names included in error messages
- Key column values logged for identification
- Record counts provided for batch size context

### 4. **Consistent Error Handling**
- Same pattern across `upsert_async`, `_process_single_model`, and `_process_issue_hierarchy_levels`
- Handles both direct exceptions and wrapped IntegrityErrors
- Maintains existing error handling for other exception types

## Usage Examples

### Before (Verbose Stack Traces):
```
2025-08-22T12:56:14.432 ERROR [RetryableAsyncSession] retrying statement after error (attempt 2): InvalidRequestError("Can't operate on closed transaction inside context manager...")
[Full stack trace with 20+ lines]
```

### After (Concise Key Information):
```
2025-08-22T12:56:14.432 WARNING [RetryableAsyncSession] retrying statement after connection error (attempt 2): InvalidRequestError
2025-08-22T12:56:14.433 WARNING [RetryableAsyncSession] RETRY_NEEDED: key: PROJ-123 (+2725 more records)
2025-08-22T12:56:14.434 ERROR Foreign key violation in _process_single_model: RETRY_NEEDED: Story - key: [PROJ-123, PROJ-124, PROJ-125] - 2726 records
```

## Testing
- Created comprehensive test suite for `_extract_key_info_for_retry()` function
- Verified foreign key exception type handling
- Tested edge cases: empty DataFrames, missing columns, large datasets
- All tests pass successfully

## Compatibility
- Maintains backward compatibility with existing error handling
- No breaking changes to existing APIs
- Additional logging provides more context without removing existing functionality
