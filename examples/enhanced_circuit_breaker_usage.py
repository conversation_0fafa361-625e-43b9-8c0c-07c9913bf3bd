"""
Enhanced GlobalCircuitBreaker Usage Examples

This file demonstrates how to use the enhanced circuit breaker with different error types,
cancellable sleep, and health monitoring.
"""

import asyncio
import aiohttp
import ssl
from typing import Dict, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import the enhanced circuit breaker (adjust import path as needed)
try:
    from dags.data_pipeline.utils.circuit_breaker import ErrorType
    from archived.archived_code import GlobalCircuitBreaker, CircuitBreakerConfig
    from dags.data_pipeline.jira.api_client import fetch_with_retries
except ImportError:
    logger.error("Could not import circuit breaker components. Adjust import paths.")
    exit(1)


async def example_1_basic_usage():
    """Example 1: Basic usage with error classification."""
    print("\n" + "="*50)
    print("Example 1: Basic Usage with Error Classification")
    print("="*50)
    
    # Create circuit breaker with enhanced configuration
    config = CircuitBreakerConfig(
        failure_threshold=3,
        recovery_timeout=30.0,
        max_consecutive_rate_limits=5,
        connection_pool_retry_multiplier=2.0,
        network_error_retry_multiplier=2,
        health_check_interval=30.0,
        max_concurrent_connections=50
    )
    
    circuit_breaker = GlobalCircuitBreaker(config=config)
    
    # Simulate different types of errors
    errors_to_test = [
        Exception("Rate limit exceeded - 429"),
        Exception("Connection pool exhausted"),
        Exception("Connection timeout occurred"),
        Exception("Service unavailable (503)")
    ]
    
    for error in errors_to_test:
        error_type = circuit_breaker.classify_error(error)
        print(f"Error: {error} -> Type: {error_type.value}")
        
        # Record the error
        if error_type == ErrorType.RATE_LIMIT:
            await circuit_breaker.record_error(error, 2000)  # 2 second delay
        else:
            await circuit_breaker.record_error(error)
    
    # Get circuit status
    status = await circuit_breaker.get_circuit_status()
    print(f"\nCircuit Status:")
    print(f"  State: {status['state']}")
    print(f"  Rate Limits: {status['consecutive_rate_limits']}")
    print(f"  Connection Pool Errors: {status['connection_pool_errors']}")
    print(f"  Network Errors: {status['network_errors']}")
    
    await circuit_breaker.cleanup()


async def example_2_http_client_with_enhanced_circuit_breaker():
    """Example 2: HTTP client using enhanced circuit breaker."""
    print("\n" + "="*50)
    print("Example 2: HTTP Client with Enhanced Circuit Breaker")
    print("="*50)
    
    config = CircuitBreakerConfig(
        failure_threshold=3,
        recovery_timeout=10.0,
        max_consecutive_rate_limits=5,
        connection_pool_retry_multiplier=1.5,
        network_error_retry_multiplier=2
    )
    
    circuit_breaker = GlobalCircuitBreaker(config=config)
    
    # Create HTTP session with connection limits
    connector = aiohttp.TCPConnector(
        limit=50, 
        limit_per_host=25, 
        resolver=aiohttp.AsyncResolver(),
        ssl=ssl.create_default_context()
    )
    
    timeout = aiohttp.ClientTimeout(total=30)
    
    async with aiohttp.ClientSession(
        connector=connector,
        timeout=timeout
    ) as session:
        
        # Example URLs (replace with actual endpoints)
        test_urls = [
            "https://corecard.atlassian.net",  # Success
            "https://httpbin.org/status/429",  # Rate limit
            "https://httpbin.org/status/503",  # Service unavailable
            "https://httpbin.org/delay/5",     # Timeout (if timeout < 5)
        ]
        
        for url in test_urls:
            print(f"\nTesting URL: {url}")
            
            try:
                # Check if we can execute
                if not await circuit_breaker.can_execute():
                    print("  Circuit breaker blocking request, waiting for recovery...")
                    await circuit_breaker.wait_for_recovery(timeout=10.0)
                
                # Enter request context
                await circuit_breaker.enter_request()
                
                try:
                    # Make the request
                    async with session.get(url) as response:
                        if response.status == 200:
                            print(f"  ✅ Success: {response.status}")
                            await circuit_breaker.record_success()
                        elif response.status == 429:
                            print(f"  ⚠️  Rate limited: {response.status}")
                            rate_limit_error = Exception(f"Rate limit - {response.status}")
                            await circuit_breaker.record_error(rate_limit_error, 2000)
                        else:
                            print(f"  ❌ Error: {response.status}")
                            error = Exception(f"HTTP {response.status}")
                            await circuit_breaker.record_error(error)
                            
                except asyncio.TimeoutError:
                    print("  ❌ Timeout error")
                    timeout_error = Exception("Request timeout")
                    await circuit_breaker.record_error(timeout_error)
                    
                except aiohttp.ClientConnectorError as e:
                    print(f"  ❌ Connection error: {e}")
                    await circuit_breaker.record_error(e)
                    
                finally:
                    # Always exit request context
                    await circuit_breaker.exit_request()
                    
            except Exception as e:
                print(f"  ❌ Unexpected error: {e}")
                await circuit_breaker.record_error(e)
    
    # Final status
    status = await circuit_breaker.get_circuit_status()
    print(f"\nFinal Circuit Status:")
    print(f"  State: {status['state']}")
    print(f"  Active Connections: {status['active_connections']}")
    print(f"  Recovery Events Set: {sum(status['recovery_events'].values())}")
    
    await circuit_breaker.cleanup()


async def example_3_cancellable_waiting():
    """Example 3: Demonstrating cancellable waiting."""
    print("\n" + "="*50)
    print("Example 3: Cancellable Waiting")
    print("="*50)
    
    config = CircuitBreakerConfig(failure_threshold=1, recovery_timeout=10.0)
    circuit_breaker = GlobalCircuitBreaker(config=config)
    
    # Open the circuit
    await circuit_breaker.record_error(Exception("Service failure"))
    print("Circuit opened due to service failure")
    
    # Start a task that waits for recovery
    async def wait_for_recovery_task():
        print("Starting to wait for recovery...")
        start_time = asyncio.get_event_loop().time()
        await circuit_breaker.wait_for_recovery(timeout=30.0)
        elapsed = asyncio.get_event_loop().time() - start_time
        print(f"Recovery wait completed in {elapsed:.2f} seconds")
        return elapsed
    
    # Start the waiting task
    wait_task = asyncio.create_task(wait_for_recovery_task())
    
    # Wait a bit, then trigger recovery
    await asyncio.sleep(2.0)
    print("Recording success to trigger recovery...")
    await circuit_breaker.record_success()
    
    # Wait for the task to complete
    elapsed = await wait_task
    
    # Should complete much faster than 30 seconds due to cancellation
    if elapsed < 5.0:
        print("✅ Wait was successfully cancelled when circuit recovered")
    else:
        print("❌ Wait was not cancelled properly")
    
    await circuit_breaker.cleanup()


async def example_4_error_specific_waiting():
    """Example 4: Error-specific waiting strategies."""
    print("\n" + "="*50)
    print("Example 4: Error-Specific Waiting Strategies")
    print("="*50)
    
    config = CircuitBreakerConfig(
        failure_threshold=5,
        recovery_timeout=5.0,
        max_consecutive_rate_limits=3
    )
    circuit_breaker = GlobalCircuitBreaker(config=config)
    
    # Test different error types and their waiting strategies
    error_scenarios = [
        (Exception("Rate limit - 429"), ErrorType.RATE_LIMIT, 1000),
        (Exception("Connection pool exhausted"), ErrorType.CONNECTION_POOL, 0),
        (Exception("Network timeout"), ErrorType.NETWORK, 0),
        (Exception("Service unavailable"), ErrorType.SERVICE, 0)
    ]
    
    for error, expected_type, delay in error_scenarios:
        print(f"\nTesting {expected_type.value} error...")
        
        # Record the error
        if delay > 0:
            await circuit_breaker.record_error(error, delay)
        else:
            await circuit_breaker.record_error(error)
        
        # Check if execution is blocked
        can_execute = await circuit_breaker.can_execute()
        print(f"  Can execute after error: {can_execute}")
        
        if not can_execute:
            print(f"  Waiting for {expected_type.value} recovery...")
            start_time = asyncio.get_event_loop().time()
            
            # Wait for specific error type recovery
            await circuit_breaker.wait_for_recovery(
                timeout=3.0, 
                error_type=expected_type
            )
            
            elapsed = asyncio.get_event_loop().time() - start_time
            print(f"  Recovery took {elapsed:.2f} seconds")
        
        # Check status
        status = await circuit_breaker.get_circuit_status()
        print(f"  Circuit state: {status['state']}")
    
    await circuit_breaker.cleanup()


async def main():
    """Run all examples."""
    print("🚀 Enhanced GlobalCircuitBreaker Usage Examples")
    print("=" * 60)
    
    try:
        await example_1_basic_usage()
        await example_2_http_client_with_enhanced_circuit_breaker()
        await example_3_cancellable_waiting()
        await example_4_error_specific_waiting()
        
        print("\n" + "=" * 60)
        print("🎉 All examples completed successfully!")
        print("=" * 60)
        
    except Exception as e:
        logger.error(f"Error running examples: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
