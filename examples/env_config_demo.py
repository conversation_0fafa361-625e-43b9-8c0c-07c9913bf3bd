#!/usr/bin/env python3
"""
Environment Configuration Demo

This script demonstrates how to use the new environment configuration system
with python-dotenv integration.

Usage:
    python examples/env_config_demo.py
"""

import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from dags.data_pipeline.utils.env_config import (
    get_env_config, 
    EnvironmentConfigLoader,
    Environment
)


def demo_environment_detection():
    """Demonstrate environment detection and configuration loading."""
    print("=" * 60)
    print("ENVIRONMENT CONFIGURATION DEMO")
    print("=" * 60)
    
    # Get the global environment configuration
    env_config = get_env_config()
    
    print(f"🌍 Current Environment: {env_config.current_env.value}")
    print(f"📁 Project Root: {env_config.project_root}")
    print(f"🔧 Environment File: .env.{env_config.current_env.value}")
    print()


def demo_database_config():
    """Demonstrate database configuration from environment variables."""
    print("📊 DATABASE CONFIGURATION")
    print("-" * 30)
    
    env_config = get_env_config()
    db_config = env_config.get_database_config()
    
    print(f"Host: {db_config.host}")
    print(f"Port (RW): {db_config.port_rw}")
    print(f"Port (RO): {db_config.port_ro}")
    print(f"Database: {db_config.name}")
    print(f"Driver: {db_config.driver}")
    print(f"Public Schema: {db_config.public_schema}")
    print(f"Custom Schemas: {', '.join(db_config.custom_schemas)}")
    print(f"Default Schema: {db_config.default_schema}")
    print(f"Pool Size: {db_config.pool_size}")
    print(f"Max Overflow: {db_config.max_overflow}")
    print()


def demo_jira_config():
    """Demonstrate JIRA configuration from environment variables."""
    print("🔗 JIRA CONFIGURATION")
    print("-" * 30)
    
    env_config = get_env_config()
    jira_config = env_config.get_jira_config()
    
    print(f"Base URL: {jira_config.base_url}")
    print(f"Max Retries: {jira_config.max_retries}")
    print(f"Initial Retry Delay: {jira_config.initial_retry_delay}ms")
    print(f"Max Retry Delay: {jira_config.max_retry_delay}ms")
    print(f"Jitter Range: {jira_config.jitter_min} - {jira_config.jitter_max}")
    print()


def demo_circuit_breaker_config():
    """Demonstrate circuit breaker configuration from environment variables."""
    print("⚡ CIRCUIT BREAKER CONFIGURATION")
    print("-" * 30)
    
    env_config = get_env_config()
    cb_config = env_config.get_circuit_breaker_config()
    
    print(f"Failure Threshold: {cb_config.failure_threshold}")
    print(f"Recovery Timeout: {cb_config.recovery_timeout}s")
    print(f"Half Open Max Calls: {cb_config.half_open_max_calls}")
    print(f"Max Consecutive Rate Limits: {cb_config.max_consecutive_rate_limits}")
    print(f"Connection Pool Retry Multiplier: {cb_config.connection_pool_retry_multiplier}")
    print(f"Network Error Retry Multiplier: {cb_config.network_error_retry_multiplier}")
    print(f"Max Concurrent Connections: {cb_config.max_concurrent_connections}")
    print(f"Health Check Interval: {cb_config.health_check_interval}s")
    print()


def demo_logging_config():
    """Demonstrate logging configuration from environment variables."""
    print("📝 LOGGING CONFIGURATION")
    print("-" * 30)
    
    env_config = get_env_config()
    log_config = env_config.get_logging_config()
    
    print(f"Config Path: {log_config.config_path}")
    print(f"Log Level: {log_config.level}")
    print(f"File Level: {log_config.file_level}")
    print(f"Console Level: {log_config.console_level}")
    print(f"DB Log Batch Size: {log_config.db_log_batch_size}")
    print(f"DB Log Flush Interval: {log_config.db_log_flush_interval}s")
    print(f"DB Log Enabled: {log_config.db_log_enabled}")
    print(f"Debug Monitor Enabled: {log_config.enable_debug_monitor}")
    print(f"Debug Monitor Interval: {log_config.debug_monitor_interval}s")
    print()


def demo_environment_variables():
    """Demonstrate individual environment variable access."""
    print("🔧 INDIVIDUAL ENVIRONMENT VARIABLES")
    print("-" * 30)
    
    env_config = get_env_config()
    
    # Demonstrate different types of environment variable access
    environment = env_config.get_env_var("ENVIRONMENT", str, "development")
    debug_mode = env_config.get_env_var("DEBUG_MODE", bool, False)
    queue_size = env_config.get_env_var("QUEUE_ISSUES_SIZE", int, 1000)
    custom_schemas = env_config.get_env_var("DB_CUSTOM_SCHEMAS", list, ["plat", "plp", "acq"])
    
    print(f"Environment: {environment}")
    print(f"Debug Mode: {debug_mode}")
    print(f"Queue Issues Size: {queue_size}")
    print(f"Custom Schemas: {custom_schemas}")
    print()


def demo_environment_switching():
    """Demonstrate switching between different environments."""
    print("🔄 ENVIRONMENT SWITCHING DEMO")
    print("-" * 30)
    
    # Show current environment
    current_env = os.getenv("ENVIRONMENT", "development")
    print(f"Current ENVIRONMENT variable: {current_env}")
    
    # Create environment-specific loaders
    environments = ["development", "staging", "production"]
    
    for env_name in environments:
        print(f"\n--- {env_name.upper()} Environment ---")
        
        # Temporarily set environment variable
        os.environ["ENVIRONMENT"] = env_name
        
        # Create new loader for this environment
        env_loader = EnvironmentConfigLoader()
        db_config = env_loader.get_database_config()
        
        print(f"Pool Size: {db_config.pool_size}")
        print(f"Max Overflow: {db_config.max_overflow}")
        
        # Check if environment-specific file exists
        env_file = env_loader.project_root / f".env.{env_name}"
        print(f"Environment File Exists: {env_file.exists()}")
    
    # Restore original environment
    os.environ["ENVIRONMENT"] = current_env
    print()


def main():
    """Main demo function."""
    try:
        demo_environment_detection()
        demo_database_config()
        demo_jira_config()
        demo_circuit_breaker_config()
        demo_logging_config()
        demo_environment_variables()
        demo_environment_switching()
        
        print("✅ Environment configuration demo completed successfully!")
        print("\n💡 Tips:")
        print("- Copy .env.example to .env and customize for your environment")
        print("- Use environment-specific files (.env.development, .env.staging, .env.production)")
        print("- Set ENVIRONMENT variable to switch between configurations")
        print("- Keep sensitive data in KeePass, not in .env files")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
