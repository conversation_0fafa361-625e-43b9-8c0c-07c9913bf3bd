# Queue Processor Refactoring Fixes

## Issue Identified

The refactored queue processors were not calling `task_done()` on the queues, causing the `unfinished_tasks` counter to remain non-zero and preventing proper queue completion detection.

**Symptoms:**
- `queue_upsert_issue: qsize=2, unfinished_tasks=2`
- `queue_upsert_others: qsize=4, unfinished_tasks=4`
- Code appears to be stuck waiting for queue completion

## Root Cause Analysis

The original `process_upsert_queue` and `process_upsert_others` functions used an `item_fetched` flag pattern:

```python
while True:
    item_fetched = False
    try:
        item = await priority_queue_manager.get_priority_message(queue)
        item_fetched = True
        
        # Process item...
        
    except asyncio.TimeoutError:
        continue
    finally:
        if item_fetched:
            queue.task_done()
```

The refactored `AbstractQueueProcessor` was missing this critical `task_done()` call pattern.

## Fixes Applied

### 1. Fixed `AbstractQueueProcessor.process_queue()` Method

**File:** `dags/data_pipeline/queue_processors/base_queue_processor.py`

**Changes:**
- Added `item_fetched` flag tracking
- Added proper `finally` block with `task_done()` call
- Added exception handling for `ValueError` in `task_done()`
- Restructured the loop to match the original pattern

```python
# Main processing loop
while True:
    item_fetched = False
    
    # Check for early processing triggers
    if await self._should_process_early(coordination_manager):
        await self._process_consolidated_data()
        
    # Check termination conditions
    if await self._should_terminate(coordination_manager):
        self.logger.info(f"Terminating {self.queue_name} processor")
        break
        
    # Process next queue item
    try:
        item = await self._get_queue_item(queue)
        item_fetched = True  # Mark that we successfully got an item
        
        if item is not None:
            await self._process_queue_item(item)
            
        # Check batch processing triggers
        if await self._should_process_batch():
            await self._process_consolidated_data()
            
    except asyncio.TimeoutError:
        self.logger.debug(f"Queue get timeout in {self.queue_name}. Queue size: {queue.qsize()}")
        continue
    except Exception as err:
        self.logger.exception(f"Error processing queue item in {self.queue_name}: {err}")
    finally:
        # Call task_done() only if we successfully fetched an item
        if item_fetched:
            try:
                queue.task_done()
            except ValueError as ve:
                self.logger.warning(f"task_done() error in {self.queue_name}: {ve}. unfinished tasks = {queue._unfinished_tasks}")
```

### 2. Fixed Import Issues

**File:** `dags/data_pipeline/queue_processors/concrete_processors.py`

**Changes:**
- Fixed `Provide["QueueContainer"]` to `Provide[QueueContainer]`
- Added missing `QueueContainer` import
- Added `BatchProcessingMetrics` to imports
- Removed redundant local imports

```python
from dags.data_pipeline.containers import LoggerContainer, QueueContainer
from dags.data_pipeline.queue_processors.batch_processing_strategy import (
    BatchProcessingStrategy, BatchProcessingMetrics, create_upsert_queue_strategy, create_upsert_others_strategy
)
```

### 3. Fixed Priority Queue Manager Import

**File:** `dags/data_pipeline/queue_processors/base_queue_processor.py`

**Changes:**
- Added fallback import for `priority_queue_system`

```python
async def _get_queue_item(self, queue: asyncio.Queue) -> Optional[Dict[str, Any]]:
    """Get next item from queue with timeout."""
    try:
        from dags.data_pipeline.priority_queue_system import priority_queue_manager
    except ModuleNotFoundError:
        from priority_queue_system import priority_queue_manager
    
    return await asyncio.wait_for(
        priority_queue_manager.get_priority_message(queue),
        timeout=self.config.timeout_seconds
    )
```

## Testing

Created `test_refactored_processors.py` to verify the fixes:

1. **Basic Functionality Test**: Ensures processors can be imported and instantiated
2. **Mock Data Test**: Tests with actual asyncio.Queue to verify `task_done()` behavior
3. **Queue State Verification**: Checks that `unfinished_tasks` counter reaches zero

## Usage Instructions

### Option 1: Use Refactored Functions (Recommended)

Replace the existing function calls in `utility_code.py`:

```python
# Replace this:
task_group.create_task(
    process_upsert_queue(project_key=project_key, q_container=q_container, logger=my_logger),
    name="process_upsert_queue"
)

# With this:
task_group.create_task(
    process_upsert_queue_refactored(project_key=project_key, q_container=q_container, logger=my_logger),
    name="process_upsert_queue"
)
```

### Option 2: Use Processors Directly

For more control over the processing:

```python
from dags.data_pipeline.queue_processors import (
    create_upsert_queue_processor,
    create_upsert_others_processor
)

# Create processors
queue_processor = create_upsert_queue_processor(project_key)
others_processor = create_upsert_others_processor(project_key)

# Process queues
result1 = await queue_processor.process_queue(queue1, coordination_manager, q_container)
result2 = await others_processor.process_queue(queue2, coordination_manager, q_container)
```

## Verification Steps

1. **Run the test script:**
   ```bash
   python test_refactored_processors.py
   ```

2. **Check queue completion:**
   - Monitor `unfinished_tasks` counters in logs
   - Verify they reach zero after processing
   - Confirm no "stuck" behavior

3. **Compare with original functions:**
   - Run both original and refactored versions
   - Verify identical processing results
   - Check performance metrics

## Benefits of the Fix

1. **Proper Queue Management**: `task_done()` is called correctly, allowing proper queue completion detection
2. **Error Handling**: Robust exception handling prevents crashes
3. **Logging**: Enhanced logging for debugging queue issues
4. **Compatibility**: Maintains compatibility with existing coordination logic
5. **Maintainability**: Clean, structured code following SOLID principles

## Migration Path

1. **Phase 1**: Deploy fixed refactored functions alongside existing ones
2. **Phase 2**: Test with subset of data to verify behavior
3. **Phase 3**: Replace existing function calls with refactored versions
4. **Phase 4**: Remove original functions after validation

The fixes ensure that the refactored queue processors behave identically to the original functions while providing the benefits of the improved architecture.
