import json

import pandas as pd

def main():
    df = pd.read_json(r"C:\Users\<USER>\Downloads\response_changelog.json")
    df = pd.json_normalize(df['issues'])
    df.drop(columns=['expand', 'self', 'changelog.startAt', 'changelog.maxResults', 'changelog.total', 'fields.summary' ], inplace=True)
    df.rename(columns={
        'changelog.histories': 'histories',
        'id': 'issue_id', 'key': 'issue_key'
    }, inplace=True)

    df = df.explode(column="histories")
    df.to_csv(r"C:\Users\<USER>\Downloads\response_exploded.csv", index=False)
    df['histories'] = df['histories'].apply(
        lambda x: {**x, 'items': (x['items'])} if isinstance(x, dict) and 'items' in x else x
    )
    df = df.join(
        pd.json_normalize(df['histories'])
        [
            [
                "id", "created", "author.accountId", "items"
            ]
        ]
    ).drop(columns=["histories"])


    df.to_csv(r"C:\Users\<USER>\Downloads\response_processed.csv", index=False)
    df.to_json(r"C:\Users\<USER>\Downloads\response_processed.json", orient='records', lines=True)

if __name__ == '__main__':
    main()