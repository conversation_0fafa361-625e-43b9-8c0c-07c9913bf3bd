# Fix for process_upsert_queue Infinite Loop

## Problem

The `process_upsert_queue` function was getting stuck in an infinite loop with 5 messages remaining in the queue. The issue was that it was checking if all **producers** were done, but it should have been checking if all **specialized consumers** (`consume_changelog`, `consume_worklog`, `consume_comment`, `consume_issue_links`, `consume_issue`) were done.

## Root Cause

The termination logic in `should_terminate_consumer()` was designed for regular queues that depend on producers, but `queue_upsert_issue` is different - it receives messages from specialized consumers, not directly from producers.

### Original Logic (Incorrect for upsert queue):
```python
# This checks if producers are done - wrong for upsert queue
producers_done = self.producers_completed.is_set()
queue_empty = self.get_queue_size(queue_name) == 0
should_terminate = producers_done and queue_empty
```

### The Flow:
1. **Producers** → `queue_issues` → **consume_issues** → specialized queues
2. **Specialized consumers** → `queue_upsert_issue` → **process_upsert_queue**

So `process_upsert_queue` should wait for specialized consumers, not producers.

## Solution

### 1. Updated Termination Logic

Modified `should_terminate_consumer()` to handle `queue_upsert_issue` differently:

```python
async def should_terminate_consumer(self, queue_name: str) -> bool:
    queue_empty = self.get_queue_size(queue_name) == 0
    
    if queue_name == "queue_upsert_issue":
        # For upsert queue, check if all specialized consumers are done
        specialized_queues = ["queue_changelog", "queue_worklog", "queue_comment", "queue_issue_links", "queue_issue"]
        all_specialized_consumers_done = all(
            len(self.active_consumers[q]) == 0 for q in specialized_queues
        )
        should_terminate = all_specialized_consumers_done and queue_empty
    else:
        # For other queues, check if all producers are done
        producers_done = self.producers_completed.is_set()
        should_terminate = producers_done and queue_empty
    
    return should_terminate
```

### 2. Added Helper Method

Created `are_all_specialized_consumers_done()` for better debugging:

```python
def are_all_specialized_consumers_done(self) -> bool:
    """Check if all specialized consumers are done processing."""
    specialized_queues = ["queue_changelog", "queue_worklog", "queue_comment", "queue_issue_links", "queue_issue"]
    all_done = all(len(self.active_consumers[q]) == 0 for q in specialized_queues)
    
    if all_done:
        self.logger.info("All specialized consumers have completed")
    else:
        active_counts = {q: len(self.active_consumers[q]) for q in specialized_queues if len(self.active_consumers[q]) > 0}
        self.logger.debug(f"Active specialized consumers: {active_counts}")
    
    return all_done
```

### 3. Enhanced Consumer Cleanup

Added automatic cleanup of completed consumers in the dynamic consumer manager:

```python
async def _cleanup_completed_consumers(self, queue_name: str) -> None:
    """Clean up consumers that have completed naturally"""
    async with self.scaling_lock:
        completed_tasks = []
        remaining_tasks = []
        
        for task in self.consumer_tasks[queue_name]:
            if task.done():
                completed_tasks.append(task)
                consumer_id = task.get_name()
                await self.coordination_manager.unregister_consumer(queue_name, consumer_id)
            else:
                remaining_tasks.append(task)
        
        self.consumer_tasks[queue_name] = remaining_tasks
```

### 4. Added Debug Logging

Enhanced logging in `process_upsert_queue` to help diagnose issues:

```python
should_terminate = await coordination_manager.should_terminate_consumer("queue_upsert_issue")
if should_terminate:
    my_logger.info("All specialized consumers completed and queue empty - processing remaining data and shutting down")
else:
    # Debug: Show current state
    queue_size = coordination_manager.get_queue_size("queue_upsert_issue")
    all_done = coordination_manager.are_all_specialized_consumers_done()
    my_logger.debug(f"Upsert queue state: queue_size={queue_size}, all_specialized_consumers_done={all_done}")
```

## Files Modified

1. **`dags/data_pipeline/scalable_coordination.py`**:
   - Updated `should_terminate_consumer()` method
   - Added `are_all_specialized_consumers_done()` method
   - Added `_cleanup_completed_consumers()` method
   - Enhanced `manage_queue_scaling()` to clean up completed consumers

2. **`dags/data_pipeline/utility_code.py`**:
   - Enhanced debug logging in `process_upsert_queue()`

3. **`tests/unit/test_bug_fixes.py`**:
   - Added comprehensive tests for the new termination logic

## Testing

### Test Cases Added

1. **`test_upsert_queue_terminates_when_specialized_consumers_done`**:
   - Verifies upsert queue terminates only when all specialized consumers are done AND queue is empty

2. **`test_upsert_queue_different_from_other_queues`**:
   - Confirms upsert queue uses different termination logic than regular queues

3. **`test_are_all_specialized_consumers_done_method`**:
   - Tests the helper method for checking specialized consumer status

## Expected Behavior

### Before Fix:
- `process_upsert_queue` would wait for producers to complete
- Even with specialized consumers done and queue having messages, it wouldn't terminate
- Infinite loop with remaining messages

### After Fix:
- `process_upsert_queue` waits for specialized consumers to complete
- Terminates when all of `consume_changelog`, `consume_worklog`, `consume_comment`, `consume_issue_links`, `consume_issue` are done AND `queue_upsert_issue` is empty
- Proper cleanup and termination

## Verification

To verify the fix works:

1. **Check Logs**: Look for debug messages showing queue state:
   ```
   Upsert queue state: queue_size=5, all_specialized_consumers_done=False
   ```

2. **Monitor Consumer Counts**: The coordination manager will log when consumers complete:
   ```
   All specialized consumers have completed
   ```

3. **Termination Message**: Should see:
   ```
   All specialized consumers completed and queue empty - processing remaining data and shutting down
   ```

## Impact

- **Fixes Infinite Loop**: `process_upsert_queue` now terminates correctly
- **Proper Resource Cleanup**: Consumers are properly tracked and cleaned up
- **Better Debugging**: Enhanced logging helps diagnose issues
- **Maintains Performance**: No significant performance impact
- **Backward Compatible**: Doesn't affect other queue processing logic

The fix ensures that `process_upsert_queue` follows the correct dependency chain and terminates when its upstream consumers (the specialized processors) have completed their work.
