from sqlalchemy import create_engine
import pandas as pd
from urllib.parse import quote_plus

# Encode password
encoded_password = quote_plus('Dope@123')
db_connection_str = f'postgresql+psycopg2://postgres:{encoded_password}@localhost:5432/jira'
engine = create_engine(db_connection_str)

# Sample DataFrame
df = pd.DataFrame({
    'col1': [1, 2, 3],
    'col2': ['A', 'B', 'C']
})

table_name = "test_table"

try:
    # Pass the SQLAlchemy engine directly
    df.to_sql(
        table_name,
        con=engine,  # <-- Engine, not raw connection
        if_exists='replace',
        index=False,
        method='multi'
    )
    print(f"Data inserted successfully into {table_name}")
except Exception as e:
    print(f"Error inserting data: {e}")

# Read back
try:
    result_df = pd.read_sql(f"SELECT * FROM {table_name}", con=engine)
    print(result_df)
except Exception as e:
    print(f"Error reading data: {e}")
finally:
    engine.dispose()
